<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <dependencies>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-core</artifactId>
        </dependency>
    </dependencies>
    <parent>
        <artifactId>fleet-user-parent</artifactId>
        <groupId>com.chervon.iot</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>fleet-user-api</artifactId>
    <name>fleet-user-api</name>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>


</project>