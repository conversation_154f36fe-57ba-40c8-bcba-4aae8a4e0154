package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**
 * @Description: 启用枚举类型
 * <AUTHOR>
 * @Date: 2022/9/29 13:34
 */
public enum EnableEnum {
    /**
     * 未启用
     */
    NOT_ENABLE(0, "未启用"),
    /**
     * 已启用
     */
    ENABLED(1, "已启用"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    EnableEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(EnableEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static EnableEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
