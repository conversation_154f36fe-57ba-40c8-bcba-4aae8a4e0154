package com.chervon.fleet.user.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.dto.AppSettingDto;
import com.chervon.fleet.user.api.entity.vo.AppSettingVo;

/**
 * fleet用户设置接口
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description fleet用户设置接口
 */
public interface RemoteAppSettingService {

    /**
     * 保存用户设置
     * @param requestDto 请求对象
     * @return
     * @throws ServiceException 服务异常
     */
    void saveSetting(AppSettingDto requestDto) throws ServiceException;

    /**
     * * 获取用户设置信息
     * @param userId 用户id
     * @return AppSettingVo 设置对象
     * @throws ServiceException 服务异常
     */
    AppSettingVo getUserSetting(Long userId) throws ServiceException;

}
