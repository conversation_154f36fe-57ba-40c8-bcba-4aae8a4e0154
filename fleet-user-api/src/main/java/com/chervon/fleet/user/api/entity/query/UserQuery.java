package com.chervon.fleet.user.api.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 用户查询对象
 *
 * <AUTHOR> 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "User查询对象", description = "User用户表")
public class UserQuery extends PageQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;
    /**
     * 全名
     */
    @ApiModelProperty(value = "全名")
    private String name;
    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private Long companyId;

    /**
     * 创建人用户Id
     */
    @ApiModelProperty(value = "创建人用户Id")
    private Long parentId;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     *  是否删除 1：删除 0：未删除
     */
    @ApiModelProperty(value = "是否删除 1：删除 0：未删除")
    private Boolean isDeleted = false;
    /**
     * 用户id集合
     */
    @ApiModelProperty(value = "idList")
    private List<Long> idList;
    /**
     * 用户类型
     */
    @ApiModelProperty(value = "角色类型列表,IN操作")
    private List<Integer> userTypeList;
    /**
     * 用户账号状态:0 未激活  1已激活
     * @see AccountStatusEnum
     */
    @ApiModelProperty(value = "用户账号状态: 0未激活 1已激活")
    private Integer accountStatus;

    /**
     * 账号状态列表
     */
    @ApiModelProperty(value = "账号状态列表")
    private List<Integer> listAccountStatus;

    /**
     * 注册开始时间
     */
    private Long beginTime;
    /**
     * 注册结束时间
     */
    private Long endTime;
    /**
     * 修改开始时间
     */
    private Long modifyBeginTime;
    /**
     * 修改结束时间
     */
    private Long modifyEndTime;
    /**
     * 是否启用 1 启用 2 停用
     */
    private Integer isEnabled;

}
