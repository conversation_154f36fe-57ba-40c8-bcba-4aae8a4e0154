package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 修改密码请求对象
 *
 * <AUTHOR> 2023/6/29
 */
@Data
@ApiModel("修改密码DTO")
public class ModifyPasswordDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("旧密码,RSA加密后密文")
    private String oldPassword;
    @ApiModelProperty("新密码,RSA加密后密文")
    private String newPassword;
}
