package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**
 * 设备类型枚举：1-PC 2-Android  3-IOS
 *
 * <AUTHOR>
 * @since 2020-03-14 10:38:54
 */
public enum DeviceTypeEnum {
    /**
     * PC
     */
    PC(1, "PC"),
    /**
     * Android
     */
    ANDROID(2, "Android"),
    /**
     * IOS
     */
    IOS(3,"IOS"),
    ;

    /**
    * 类型
    */
    private int type;
    /**
    * 描述
    */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    DeviceTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(DeviceTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static DeviceTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
