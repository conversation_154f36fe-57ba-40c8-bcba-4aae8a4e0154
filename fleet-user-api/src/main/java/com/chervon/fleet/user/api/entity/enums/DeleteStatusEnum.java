package com.chervon.fleet.user.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否删除
 * @Description:删除状态的枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DeleteStatusEnum implements TypeEnum {
    /**
     * 正常
     */
    NORMAL(0, "正常"),
    /**
     * 已删除
     */
    DELETE(1, "已删除");

    int type;
    String desc;

}
