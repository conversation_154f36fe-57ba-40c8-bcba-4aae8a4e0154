package com.chervon.fleet.user.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 用户固定角色枚举：1-admin  2-manager 3-employee
 * <AUTHOR>
 */
public enum UserSourceTypeEnum implements TypeEnum {
    //管理员不验证角色权限信息，加载所有资源
    /**
     * Fleet Service Web自行注册
     */
    REGISTER_BY_SELF(1, "Fleet Service Web自行注册"),
    /**
     * Fleet Service Web邀请
     */
    INVITED(2,"Fleet Service Web邀请"),
    /**
     * CRM同步
     */
    CRM_SYNC(3,"CRM同步"),
    ;

    /**
     * 用户类型id
     */
    private int type;
    /**
     * 用户类型描述
     */
    private String desc;

    UserSourceTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(UserSourceTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static UserSourceTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
