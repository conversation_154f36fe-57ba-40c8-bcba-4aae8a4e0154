package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/2 16:42
 */
@Data
@Accessors(chain = true)
public class FleetUserVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 账户id，操作需要传入的userId
     */
    @ApiModelProperty("账户id，操作需要传入的userId")
    private Long userId;
    /**
     * 员工邮箱
     */
    @ApiModelProperty("员工邮箱")
    private String userEmail;
    /**
     * 员工姓名1
     */
    @ApiModelProperty("员工姓名1")
    private String userFirstName;
    /**
     *员工姓名2
     */
    @ApiModelProperty("员工姓名2")
    private String userLastName;
    /**
     *员工角色
     */
    @ApiModelProperty("员工角色")
    private String userRole;
    /**
     *所属租户id
     */
    @ApiModelProperty("所属租户id")
    private Long companyId;
    /**
     *所属租户名称
     */
    @ApiModelProperty("所属租户名称")
    private String companyName;
    /**
     *员工账户来源
     */
    @ApiModelProperty("员工账户来源")
    private String userSourceType;
    /**
     *邀请人邮箱
     */
    @ApiModelProperty("邀请人邮箱")
    private String invitationEmail;
    /**
     *状态 0 未激活 1 已激活 2 游离 3 停用 4 已注销
     */
    @ApiModelProperty("状态 0 未激活 1 已激活 2 游离 3 停用 4 已注销")
    private String userState;
    /**
     *  注册时间
     */
    @ApiModelProperty("注册时间")
    private Long registerTime;
    /**
     *注册时间
     */
    @ApiModelProperty("注册时间")
    private Long activeTime;
    /**
     *  启用状态 0 未启用 1 启用
     */
    @ApiModelProperty("启用状态 0 未启用 1 启用")
    private String isEnabled;

}
