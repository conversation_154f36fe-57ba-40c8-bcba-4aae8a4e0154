package com.chervon.fleet.user.api.entity.query;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户分页查询类
 * <AUTHOR>
 * @since 2023-08-04 18:16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("用户分页查询类")
public class UserPageQuery extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名,模糊搜索")
    private String name;
    /**
     * 帐号激活状态: 0未激活 1激活 4已注销
     */
    @ApiModelProperty("帐号激活状态: 0未激活 1激活 4已注销")
    private Integer accountStatus;
    /**
     * 角色类型: 1超管 2管理员 3普通用户
     */
    @ApiModelProperty("角色类型: 1超管 2管理员 3普通用户")
    private Integer userType;
}
