package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 不携带token创建企业DTO
 * <AUTHOR>
 * @date 2023/8/25
 */
@Data
@Accessors(chain = true)
public class CompanyAddWithoutTokenDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("企业名称")
    private String companyName;
}
