package com.chervon.fleet.user.api.service;


import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.dto.CompanyDto;
import com.chervon.fleet.user.api.entity.dto.CompanyNameDto;
import com.chervon.fleet.user.api.entity.query.CompanyQuery;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;

/**
 * 租户表服务接口
 * <AUTHOR>
 * @since 2023-06-20 14:28:36
 */
public interface RemoteCompanyService {

    /**
     * 租户信息分页查询
     *
     * @param query 查询条件
     * @return 经销商分页信息
     * @throws ServiceException 服务内部异常
     */
    PageResult<CompanyVo> getPagedList(CompanyQuery query) throws ServiceException;

    /**
     * 租户详情查询
     * @param id 主键ID
     * @return 租户详情
     * @throws ServiceException 服务内部异常
     */
    CompanyVo getDetail(Long id) throws ServiceException;

    /**
     * 根据名称查询租户信息
     * @param name 租户名称
     * @return 企业信息
     * @throws ServiceException 服务内部异常
     */
    CompanyVo getCompanyByName(String name) throws ServiceException;

    /**
     * 经销商信息新增,绑定给指定的用户
     *
     * @param companyName 经销商名称
     * @param userId 用户ID
     * @return companyId
     * @throws ServiceException 服务内部异常
     */
    Long add(String companyName, Long userId) throws ServiceException;

    /**
     * * 租户编辑
     * @param companyDto 编辑对象
     * @return 编辑结果
     * @throws ServiceException 服务内部异常
     */
    boolean edit(CompanyDto companyDto) throws ServiceException;

    /**
     * 编辑名称
     * @param requestDto 编辑Dto
     * @throws ServiceException 服务内部异常
     */
    void editName(CompanyNameDto requestDto) throws ServiceException;
}
