package com.chervon.fleet.user.api.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * 查询对象
 * * <AUTHOR> 2022/10/17
 */
@Accessors(chain = true)
@Data
public class ResourceQuery {
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 是否启用：0未启用  1已启用 *
     */
    private Integer isEnable;
    /**
     * 所属系统应用: 1
     */
    private Integer application;
    /**
     * 资源编码（资源标识）sys:user:info
     */
    @ApiModelProperty(value = "资源编码（资源标识）sys:user:info")
    private String code;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String name;
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private Long userId;
    /**
     * 资源id列表
     */
    @ApiModelProperty(value = "资源id列表")
    private List<Long> ids;
    /**
     * 角色Id
     */
    @ApiModelProperty(value = "角色Id")
    private Long roleId;
    /**
     * 是否只查询出一二级页面：true是，false否(默认)
     */
    @ApiModelProperty(value = "是否只查询出一二级页面：true是，false否(默认)")
    private Boolean flag;


}
