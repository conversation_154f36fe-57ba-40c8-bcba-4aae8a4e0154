package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName:FleetUserPageVo
 * @Description: FleetUserPageVo
 * @author:liwei
 * @date 2023/8/2 16:42
 * rerun:FleetUserPageVo
 */
@Data
@Accessors(chain = true)
public class FleetUserPageVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 页面展示的id
     */
    @ApiModelProperty("页面展示的id")
    private Long showId;
    /**
     * 账户id，操作需要传入的userId
     */
    @ApiModelProperty("账户id，操作需要传入的userId")
    private List<Long> userId;
    /**
     * 员工邮箱
     */
    @ApiModelProperty("员工邮箱")
    private String userEmail;
    /**
     * 员工姓名
     */
    @ApiModelProperty("员工姓名1")
    private List<String> userFirstName;
    /**
     * 员工姓名
     */
    @ApiModelProperty("员工姓名2")
    private List<String> userLastName;
    /**
     * 员工角色
     */
    @ApiModelProperty("员工角色")
    private List<String> userRole;
    /**
     * 所属租户id
     */
    @ApiModelProperty("所属租户id")
    private List<Long> companyId;
    /**
     * 所属租户名称
     */
    @ApiModelProperty("所属租户名称")
    private List<String> companyName;
    /**
     * 员工账户来源
     */
    @ApiModelProperty("员工账户来源")
    private List<String> userSourceType;
    /**
     * 邀请人邮箱
     */
    @ApiModelProperty("邀请人邮箱")
    private List<String> invitationEmail;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private List<String> userState;
    /**
     * 注册时间
     */
    @ApiModelProperty("注册时间")
    private List<Long> registerTime;
    /**
     *  激活时间
     */
    @ApiModelProperty("激活时间")
    private List<Long> activeTime;

}
