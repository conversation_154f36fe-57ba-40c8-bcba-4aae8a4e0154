package com.chervon.fleet.user.api.entity.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 租户导出excel对象
 * <AUTHOR>
 * @date 2023/8/2 16:48
 */
@Data
@Accessors(chain = true)
public class FleetCompanyExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 租户ID
     */
    @Alias("租户ID")
    private Long companyId;
    /**
     * 租户名称
     */
    @Alias("租户名称")
    private String companyName;
    /**
     * 管理员ID
     */
    @Alias("管理员ID")
    private Long adminId;
    /**
     * 管理员邮箱
     */
    @Alias("管理员邮箱")
    private String adminEmail;
    /**
     * 管理员First name
     */
    @Alias("管理员First name")
    private String adminFirstName;
    /**
     * 管理员Last name
     */
    @Alias("管理员Last name")
    private String adminLastName;
    /**
     * 状态
     */
    @Alias("状态")
    private String companyState;
    /**
     * 注册时间
     */
    @Alias("租户注册时间")
    private String registerTime;

    /**
     * 获取: companyId
     * @return
     */
    public String getCompanyId() {
        return CsvUtil.format(this.companyId);
    }

    /**
     * 获取: companyName
     * @return
     */
    public String getCompanyName() {
        return CsvUtil.format(this.companyName);
    }

    /**
     * 获取: adminId
     * @return
     */
    public String getAdminId() {
        return CsvUtil.format(this.adminId);
    }

    /**
     * 获取: adminEmail
     * @return
     */
    public String getAdminEmail() {
        return CsvUtil.format(this.adminEmail);
    }

    /**
     * 获取: adminFirstName
     * @return
     */
    public String getAdminFirstName() {
        return CsvUtil.format(this.adminFirstName);
    }

    /**
     * 获取: adminLastName
     * @return
     */
    public String getAdminLastName() {
        return CsvUtil.format(this.adminLastName);
    }

    /**
     * 获取: companyState
     * @return
     */
    public String getCompanyState() {
        return CsvUtil.format(this.companyState);
    }

    /**
     * 获取: registerTime
     * @return
     */
    public String getRegisterTime() {
        return CsvUtil.format(this.registerTime);
    }
}
