package com.chervon.fleet.user.api.entity.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/2 16:42
 */
@Data
@Accessors(chain = true)
public class FleetUserExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    @Alias("账户ID")
    private Long userId;

    @Alias("员工邮箱")
    private String userEmail;

    @Alias("员工First name")
    private String userFirstName;

    @Alias("员工Last name")
    private String userLastName;

    @Alias("员工角色")
    private String userRole;

    @Alias("所属租户ID")
    private Long companyId;

    @Alias("所属租户名称")
    private String companyName;

    @Alias("员工账户来源")
    private String userSourceType;

    @Alias("邀请人邮箱")
    private String invitationEmail;

    @Alias("状态")
    private String userState;

    @Alias("启用状态")
    private String isEnabled;

    @Alias("员工账户激活时间")
    private String activeTime;

    @Alias("员工账户注册时间")
    private String registerTime;

    public String getUserId() {
        return CsvUtil.format(this.userId);
    }

    public String getUserEmail() {
        return CsvUtil.format(this.userEmail);
    }

    public String getUserFirstName() {
        return CsvUtil.format(this.userFirstName);
    }

    public String getUserLastName() {
        return CsvUtil.format(this.userLastName);
    }

    public String getUserRole() {
        return CsvUtil.format(this.userRole);
    }

    public String getCompanyId() {
        return CsvUtil.format(this.companyId);
    }

    public String getCompanyName() {
        return CsvUtil.format(this.companyName);
    }

    public String getUserSourceType() {
        return CsvUtil.format(this.userSourceType);
    }

    public String getInvitationEmail() {
        return CsvUtil.format(this.invitationEmail);
    }

    public String getUserState() {
        return CsvUtil.format(this.userState);
    }

    public String getActiveTime() {
        return CsvUtil.format(this.activeTime);
    }

    public String getRegisterTime() {
        return CsvUtil.format(this.registerTime);
    }

}
