package com.chervon.fleet.user.api.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 租户分页查询对象
 * <AUTHOR>
 * @date 2023/8/2 16:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FleetCompanyPageDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 注册开始时间
     */
    @ApiModelProperty("注册开始时间")
    private Long registerStartTime;
    /**
     * 注册结束时间
     */
    @ApiModelProperty("注册结束时间")
    private Long registerEndTime;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String companyId;
    /**
     * 租户名称
     */
    @ApiModelProperty("租户名称")
    private String companyName;
    /**
     * 管理员id
     */
    @ApiModelProperty("管理员id")
    private String adminId;
    /**
     * 管理员邮箱
     */
    @ApiModelProperty("管理员邮箱")
    private String adminEmail;
    /**
     * 管理员姓名
     */
    @ApiModelProperty("管理员姓名")
    private String adminName;
    /**
     * 状态，支持多选
     */
    @ApiModelProperty("状态，支持多选")
    private List<String> companyState;

}
