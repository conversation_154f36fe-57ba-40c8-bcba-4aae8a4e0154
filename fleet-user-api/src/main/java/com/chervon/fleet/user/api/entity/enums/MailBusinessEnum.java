package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**
 * 发送邮件的业务类型: 1 忘记密码发送邮件 2 管理员注册邮件
 * <AUTHOR>
 * @Date: 2023/7/5 13:34
 */
public enum MailBusinessEnum {
    /**
     * 忘记密码发送邮件
     */
    FORGET_PASSWORD(1, "忘记密码发送邮件"),
    /**
     * 管理员注册邮件
     */
    USER_REGISTER(2, "管理员注册邮件"),
    /**
     * 邀请员工邮件
     */
    USER_INVITATION(3, "邀请员工邮件"),
    /**
     * 用户角色变更通知邮件
     */
    USER_ROLE_UPDATED(4, "用户角色变更通知邮件"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;
    /**
     * 配置的模板路径：
     * mail.template.forgetpassword
     * mail.template.userregister
     */
    private String template;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    MailBusinessEnum(int type,String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(MailBusinessEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static MailBusinessEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
