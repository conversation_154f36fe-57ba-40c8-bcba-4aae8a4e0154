package com.chervon.fleet.user.api.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * (t_app_setting)实体类
 *
 * <AUTHOR>
 * @since 2023-07-15 17:30:36
 * @description 
 */
@Data
@Accessors(chain = true)
public class AppSettingDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 默认语言
     */
    private String language;
    /**
     * 网关模式，1 正常模式  2 省电模式
     */
    private Integer gatewayMode;
    /**
     * app用户协议版本
     */
    private String appUserAgreementVersion;
    /**
     * app隐私协议版本
     */
    private String appSecretAgreementVersion;
    /**
     * web用户协议版本
     */
    private String webUserAgreementVersion;
    /**
     * web隐私协议版本
     */
    private String webSecretAgreementVersion;

}