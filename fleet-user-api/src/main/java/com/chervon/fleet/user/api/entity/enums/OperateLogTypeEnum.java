package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**

 *  用户操作类型枚举
 * @Description:操作日志类型枚举

 * <AUTHOR>
public enum OperateLogTypeEnum {
    /**
     * 登录
     */
    LOGIN(1, "user login"),
    /**
     * 登录AD域
     */
    LOGIN_AD_DOMAIN(2, "user AD login"),
    /**
     * 创建用户
     */
    USER_CREATE(11, "add user"),
    /**
     * 编辑用户
     */
    USER_EDIT(12, "edit user"),
    /**
     * 编辑角色
     */
    ROLE_EDIT(21,"edit role"),
    /**
     * 删除角色
     */
    ROLE_DELETE(22,"delete role")
    ;

    /**
    * 类型
    */
    private int type;
    /**
    * 描述
    */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    OperateLogTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(OperateLogTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static OperateLogTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
