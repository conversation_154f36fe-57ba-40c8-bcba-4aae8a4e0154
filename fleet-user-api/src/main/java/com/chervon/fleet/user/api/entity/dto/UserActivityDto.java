package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户激活登录请求dto
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class UserActivityDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;

    /**
     * 待激活的用户Id
     */
    @ApiModelProperty("待激活的用户Id")
    private Long userId;
    /**
     * 激活用户密码
     */
    @ApiModelProperty("密码")
    private String password;

}
