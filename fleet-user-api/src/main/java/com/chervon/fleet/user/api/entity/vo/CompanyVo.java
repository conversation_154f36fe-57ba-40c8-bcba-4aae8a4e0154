package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * CompanyVo 租户返回对象
 * <AUTHOR>
 * @date 2024/2/3
 * @return Company信息vo
 */
@Data
@Accessors(chain = true)
@ApiModel("Company信息vo")
public class CompanyVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 管理员用户Id
     */
    private Long userId;
    /**
     * 是否启用 1：启用 0：停用
     */
    private Integer enabled;
    /**
     * 租户状态：1-正常、2-停用、4-已注销
     */
    private Integer status;

}
