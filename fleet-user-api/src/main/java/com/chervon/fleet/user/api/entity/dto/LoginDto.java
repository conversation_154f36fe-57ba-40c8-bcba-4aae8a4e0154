package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 登录数据传输的载体
 *
 * <AUTHOR>
 * @date 2022/9/11
 */
@Data
@Accessors(chain = true)
public class LoginDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;
    /**
     * 用户密码
     */
    @ApiModelProperty("密码")
    private String password;
}
