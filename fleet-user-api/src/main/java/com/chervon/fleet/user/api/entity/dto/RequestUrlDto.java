package com.chervon.fleet.user.api.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 请求黑白名单配置实体类
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class RequestUrlDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 访问路径
     */
    private String path;
    /**
     * 匹配方法
     */
    private String matchMethod;
    /**
     * 限制类型（1.白名单，2黑名单）
     */
    private Integer type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否删除 1：删除 0：未删除
     */
    private Integer isDeleted;

}