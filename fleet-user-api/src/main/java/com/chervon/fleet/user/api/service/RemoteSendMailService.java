package com.chervon.fleet.user.api.service;


import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.dto.MailRequestDto;

/**
 * 发送业务邮件服务
 *
 * <AUTHOR> 2022/9/20
 */
public interface RemoteSendMailService {
    /**
     * 验证邮件请求参数
     *
     * @param mailRequest 邮件请求参数
     * @throws ServiceException 服务内部异常
     */
    void validateMailRequestParam(MailRequestDto mailRequest) throws ServiceException;

    /**
     * * 用户注册发送邮件
     *
     * @param email 邮箱
     * @throws ServiceException 服务内部异常
     */
    void sendUserRegisterHtml(String email) throws ServiceException;

    /**
     * * 忘记密码发送邮件
     *
     * @param email     邮箱
     * @param firstName 姓
     * @param lastName  名
     * @throws ServiceException 服务内部异常
     */
    void sendForgetPasswordHtml(String email, String firstName, String lastName) throws ServiceException;

    /**
     * 简单文本邮件
     *
     * @param mailRequest 邮箱请求体
     * @throws ServiceException 服务内部异常
     */
    void sendSimpleMail(MailRequestDto mailRequest) throws ServiceException;

    /**
     * Html格式邮件,可带附件
     *
     * @param mailRequest 邮箱请求体
     * @return 邮件发送结果
     * @throws ServiceException 服务内部异常
     */
    String sendHtmlMail(MailRequestDto mailRequest) throws ServiceException;

    /**
     * * 给dealer和staff发送邮件
     *
     * @param email 邮箱
     * @throws ServiceException 服务内部异常
     */
    void sendMailAfterAddDealer(String email) throws ServiceException;
}

