package com.chervon.fleet.user.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 网关配置
 * <AUTHOR>
 * @date 2022/9/14 14:00
 * @since JDK1.8
 * Versions 1.0
 *
 */
@Data
public class GatewayProperties {
    /*
     * 创建一个新的BlockingExecute对象
     */
    private BlockingExecute blocking = new BlockingExecute();


    /**
     * 阻塞执行配置
     * <AUTHOR>
     * @date 2024/2/5 14:00
     * @since JDK1.8
     * Versions 1.0
     */
    @Data
    public static class BlockingExecute {
        /**
         * 最大池大小 默认：2*CPU核心数
         */
        private int maxActive = Runtime.getRuntime().availableProcessors() << 1;
        /**
         * 初始线程数 默认：CPU核心数+1
         */
        private int initialSize = Runtime.getRuntime().availableProcessors() + 1;
        /**
         * 最大空闲时间 单位：毫秒  默认：10000
         */
        private int maxIdleTime = 10000;
        /**
         * 最大等候任务数  默认：4
         */
        private int maxWaitingTask = 1 << 4;

        /**
         * 设置最大等待任务数
         * @param maxWaitingTask
         */
        public void setMaxWaitingTask(int maxWaitingTask) { 
            // 如果传入的最大等待任务数大于0
            if (maxWaitingTask > 0) { 
                // 将最大等待任务数设置为传入的值
                this.maxWaitingTask = maxWaitingTask; 
            } else { 
                // 如果传入的最大等待任务数不大于0，则将最大等待任务数设置为1
                this.maxWaitingTask = 1; 
            } 
        }
        
    }
}
