package com.chervon.fleet.user.api.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 经销商门店信息
 */
@Data
@Accessors(chain = true)
public class AgreementQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "协议类型code")
    private String typeCode;

    @ApiModelProperty(value = "具体版本协议id")
    private String appAgreementContentId;

    @ApiModelProperty(value = "主协议id")
    private String appAgreementId;

}
