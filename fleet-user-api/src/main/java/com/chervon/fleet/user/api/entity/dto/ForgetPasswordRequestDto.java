package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 忘记密码请求对象
 * <AUTHOR> 2022/10/14
 */
@Data
public class ForgetPasswordRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("邮箱地址")
    private String email;

    @ApiModelProperty("邮箱随机验证码")
    private String verificationCode;

    @ApiModelProperty("新密码")
    private String newPassword;
}
