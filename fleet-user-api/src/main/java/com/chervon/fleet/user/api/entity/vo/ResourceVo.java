package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 资源输出对象
 *
 * <AUTHOR> @since 2023-7-14 10:52:46
 */
@Data
@Accessors(chain = true)
public class ResourceVo implements Serializable {

    private static final long serialVersionUID = 8382078016804452696L;
    /**
     * 自增长Id
     */
    private Long id;

    /**
     * 资源编码（资源标识）sys:user:info
     */
    private String code;

    /**
     * 资源名称:通过国际化替换
     */
    private String name;

    /**
     * 父资源id
     */
    private Long parentId;

    /**
     * 资源类型：菜单-页面-页签-按钮
     */
    private Integer type;

    /**
     * 资源主体：pc，pda
     */
    private Integer device;

    /**
     * 页面访问的uri
     */
    private String url;

    /**
     * 子资源列表
     */
    @ApiModelProperty("子资源列表")
    private List<ResourceVo> subResourceList;

}
