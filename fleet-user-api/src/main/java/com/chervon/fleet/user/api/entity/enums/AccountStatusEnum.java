package com.chervon.fleet.user.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 用户账号状态枚举:0 未激活  1已激活
 * @Date: 2020/8/29 13:34
 */
public enum AccountStatusEnum implements TypeEnum {
    /**
     * 未激活
     */
    NOT_ACTIVATED(0, "Not activated", "未激活"),
    /**
     * 已激活
     */
    ACTIVATED(1, "Activated", "已激活"),
    /**
     * 已注销
     */
    CANCELED(4, "CANCELED", "用户已注销"),
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type 类型
     * @param value 值
     * @param desc 描述
     */
    AccountStatusEnum(int type, String value, String desc) {
        this.type = type;
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @param
     * @return
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型
     * @return 返回值
     */
    public static String getValue(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(AccountStatusEnum::getValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(AccountStatusEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static AccountStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
