package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * fleet用户登录响应信息
 * <AUTHOR>
 * @date 2023/6/11
 */
@Data
@Accessors(chain = true)
@ApiModel("登录信息")
public class LoginResultVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private Long id;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;
    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 全名
     */
    private String name;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 用户类型：1-超级管理员、2-普通管理员  3-员工
     * @See UserRoleEnum
     */
    @ApiModelProperty(value = "用户类型：1-超级管理员、2-普通管理员  3-员工")
    private Integer userType;
    /**
     * 公司id（租户Id）
     */
    @ApiModelProperty(value = "公司id（租户Id）")
    private Long companyId;
    /**
     * 公司名称（租户名称）
     */
    @ApiModelProperty(value = "公司id（租户名称）")
    private String companyName;
    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;
    @ApiModelProperty("头像颜色值")
    private String avatarColor;

}
