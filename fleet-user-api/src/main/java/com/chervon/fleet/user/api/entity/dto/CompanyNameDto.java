package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 企业注册信息载体
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class CompanyNameDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 企业id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    private String companyName;
}
