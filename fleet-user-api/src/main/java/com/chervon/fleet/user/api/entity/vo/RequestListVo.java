package com.chervon.fleet.user.api.entity.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 请求黑白名单配置实体类
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class RequestListVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 访问路径
     */
    private String path;
    /**
     * 匹配方法
     */
    private String matchMethod;
    /**
     * 限制类型（1.白名单，2黑名单）
     */
    private Integer type;

}