package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * fleet用户同步信息
 * <AUTHOR>
 * @date 2023/6/11
 */
@Data
@Accessors(chain = true)
@ApiModel("用户同步信息")
public class UserSyncVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private Long userId;
    /**
     * 公司id（租户Id）
     */
    @ApiModelProperty(value = "公司id（租户Id）")
    private Long companyId;


    @ApiModelProperty("账号状态：0未激活  1已激活（正常）")
    private Integer userStatus;

}
