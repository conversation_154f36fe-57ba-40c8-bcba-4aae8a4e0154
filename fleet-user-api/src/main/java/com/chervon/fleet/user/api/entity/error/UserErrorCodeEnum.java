package com.chervon.fleet.user.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 错误码定义：错误码：3位应用名+3位模块名+2+3位流水号
 * 用户中心应用名：117+001+2+三位流水号
 * **********-1170012100 用户相关错误信息提示编号
 *
 * <AUTHOR>
 * @since 2023/7/13 11:50
 */
public enum UserErrorCodeEnum implements IError {
    /**
     * 用户不存在
     */
    USER_NOT_EXIST("**********", "User information does not exist"),
    /**
     * 用户已经激活
     */
    USER_ALREADY_ACTIVITY("**********", "The user is already in an activated state"),
    /**
     * 验证码错误，请输入正确的验证码！
     */
    VERIFICATION_ERROR("**********", "This link is invalid. Please contact the inviter to resend the invitation."),
    /**
     * 密码不能为空
     */
    PASSWORD_NULL("**********", "Password needs to be entered"),
    ACCOUNT_IS_NOT_ENABLED("**********", "User status not enabled, unable to log in"),

    LOGIN_PASSWORD_OR_NAME_ERROR("**********", "The account does not match the password."),
    EMAIL_REGISTERED("**********", "Email already registered. Please log in."),
    ADMIN_TRANSFER_TO_SELF("**********", "The role cannot be transferred to oneself"),
    ADMIN_TRANSFER_TO_OTHER_COMPANY("**********", "The role cannot be transferred to users of other tenants"),
    DATA_NULL("**********", "User information does not exist"),
    TOKEN_COMPUTE_ERROR("**********", "Login token generation failed"),
    TOKEN_VALIDATE_ERROR("**********", "Login token verification failed"),
    PASSWORD_RULE("**********", "Input a minimum of 6 characters, a maximum of 18 characters, and only uppercase and lowercase letters and numbers can be entered"),
    ILLEGAL_GET_USER_PERMISSIONS("**********", "Illegal acquisition of user permissions[{0}]"),
    NEW_PASSWORD_RULE_ERROR("**********", "The new password format is incorrect"),
    OLD_PASSWORD_NOT_RIGHT("**********", "The account does not match the password."),
    INVITED_USER_CANT_BE_ADMIN("**********", "The invited user cannot have a super administrator role"),
    USER_TYPE_EDIT_INVALID("**********", "The administrator role information cannot be modified"),
    USER_ALREADY_AN_EMPLOYEE_OF_YOUR_COMPANY("**********", "Invite failed. The user is already an employee of your company."),

    USER_ALREADY_AN_EMPLOYEE_OF_ANOTHER_COMPANY("**********", "Invite failed. The user is already an employee of another company."),
    PREVIOUS_VERIFICATION_NOT_EXPIRE("**********", "The verification code of the user who sent the invitation email again has not expired before"),
    USER_TYPE_NOT_ADMIN("**********", "The user who wants to transfer the super management role is not a super manager"),
    USER_NOT_FOUND("**********", "user not found"),
    USER_LOGIN_TOO_MANY_DEVICES("**********", "Your account is logged into too many devices."),
    USER_REGISTER_VERIFICATION_ERROR("**********", "Invalid link.\nPlease resend your request"),
    USER_INVITATION_VERIFICATION_ERROR("**********", "Invalid link.\nPlease contact the inviter to resend the invitation"),
    FORGET_PASSWORD_VERIFICATION_ERROR("**********", "Invalid link.\nPlease resend your request to reset the password"),
    ACTIVATION_FAILED_ALREADY_AN_EMPLOYEE_OF_ANOTHER_COMPANY("**********", "Activation failed. You are already registered to another company."),
    ACTIVATION_FAILED_ALREADY_ACTIVATED("**********", "Activation failed. You have already registered."),
    MAIL_SERVER_SLOW("**********", "Mail server slow, please try again later."),
    INVITATION_WITHIN_72_HOURS("**********", "An invitation was sent within 72 hours. Please wait for activation."),
    USER_ALREADY_SEND_INVITATION("**********","An invitation was sent. You can resend the invitation via \"invite Again\"."),
    ;

    UserErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private String errorCode;
    /**
     * 描述
     */
    private String errorReason;

    @Override
    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
