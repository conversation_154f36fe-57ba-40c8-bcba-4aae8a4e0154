package com.chervon.fleet.user.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 租户相关错误码
 * 1170042001-1170042100 租户相关错误码信息提示编号
 *
 * <AUTHOR>
 * @since 2023-07-27 14:10
 **/
public enum CompanyErrorCodeEnum implements IError {
    /**
     * 用户已绑定过企业
     */
    USER_ALREADY_BOUND_COMPANY("1170042001", "用户已绑定过企业"),
    /**
     * 企业名称已被占用
     */
    COMPANY_NAME_ALREADY_EXISTED("1170042002", "企业名称已被占用"),
    /**
     * 用户不存在
     */
    USER_NOT_EXIST("1170012001", "User information does not exist"),
    ;

    /**
     * 异常编码
     */
    private String errorCode;
    /**
     * 异常描述
     */
    private String message;

    CompanyErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.message = errorReason;
    }

    /**
     * 获取异常编码
     * @return 故障码
     */
    @Override
    public String getCode() {
        return errorCode;
    }

    /**
     * 获取异常信息
     * @return
     */
    @Override
    public String getMessage() {
        return message;
    }
}
