package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 修改密码请求对象
 * <AUTHOR> 2023/6/29
 */
@Data
@ApiModel("转移AdminDto")
public class TransferAdminDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("转移给目标用户id")
    private Long targetUserId;
}
