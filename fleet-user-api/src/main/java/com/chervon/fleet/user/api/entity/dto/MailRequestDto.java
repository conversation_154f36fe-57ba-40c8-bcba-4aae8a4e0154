package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 邮件请求实体
 * <AUTHOR> 2022/9/20
 */
@Data
@Accessors(chain = true)
public class MailRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 接收人
     */
    @ApiModelProperty("收件人")
    private String sendTo;

    /**
     *  邮件主题
     */
    @ApiModelProperty("邮件主题")
    private String subject;

    /**
     *  邮件内容
     */
    @ApiModelProperty("邮件内容")
    private String text;

    /**
     *  附件路径
     */
    @ApiModelProperty("附件路径")
    private String filePath;
    /**
     * 是否异步发送
     */
    @ApiModelProperty("是否异步发送：true:异步  false:同步发送")
    private boolean asyncSend=true;
}