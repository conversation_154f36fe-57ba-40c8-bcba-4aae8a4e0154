package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 企业注销信息载体
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class CompanyCancelDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private Long userId;
    /**
     * 企业Id
     */
    @ApiModelProperty("企业Id")
    private Long companyId;
}
