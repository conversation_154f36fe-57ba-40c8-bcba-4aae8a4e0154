package com.chervon.fleet.user.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Title: UserEnabledEnum
 * @Description: 用户启用状态:0 停用  1启用
 * @Date: 2020/8/29 13:34
 * @Version: 1.0
 * @Copyright: Copyright (c) 2024
 */
public enum UserEnabledEnum implements TypeEnum {
    /**
     * 停用
     */
    DEACTIVATE(0, "Deactivate", "停用"),
    /**
     * 启用
     */
    ACTIVE(1, "Active", "启用"),
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param value
     * @param desc
     */
    UserEnabledEnum(int type, String value, String desc) {
        this.type = type;
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getValue(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(UserEnabledEnum::getValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(UserEnabledEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static UserEnabledEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
