package com.chervon.fleet.user.api.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.dto.CompanyAdminChangeRecordDto;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.dto.FleetUserPageDto;
import com.chervon.fleet.user.api.entity.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/06
 * @description: 远程用户中心服务接口
 */
public interface RemoteFleetUserCenterService {

    /**
     * 分页接口
     *
     * @param req 查询条件
     * @return 分页数据
     * @throws ServiceException 服务内部异常
     */
    PageResult<FleetCompanyVo> companyPage(FleetCompanyPageDto req) throws ServiceException;

    /**
     * 列表接口
     *
     * @param companyId   租户id
     * @param companyName 租户名称
     * @return 列表数据
     * @throws ServiceException 服务内部异常
     */
    List<FleetCompanyVo> companyList(String companyId, String companyName) throws ServiceException;

    /**
     * 详情
     *
     * @param companyId 租户id
     * @return 详情数据
     * @throws ServiceException 服务内部异常
     */
    FleetCompanyVo companyDetail(Long companyId) throws ServiceException;

    /**
     * 停用
     * @param companyId 租户id
     * @throws ServiceException 服务内部异常
     */
    void companyDeactivate(Long companyId) throws ServiceException;

    /**
     * 启用
     * @param companyId 租户id
     * @throws ServiceException 服务内部异常
     */
    void companyEnable(Long companyId) throws ServiceException;

    /**
     * 注销
     *
     * @param companyId 租户id
     * @throws ServiceException 服务内部异常
     */
    void companyLogoff(Long companyId) throws ServiceException;

    /**
     * 管理员变更记录
     *
     * @param req 查询条件
     * @return 变更记录数据
     * @throws ServiceException 服务内部异常
     */
    PageResult<AdminChangeRecordVo> companyAdminChangeRecord(CompanyAdminChangeRecordDto req) throws ServiceException;

    /**
     * 列表数据
     *
     * @param req 查询条件
     * @return 列表数据
     * @throws ServiceException 服务内部异常
     */
    List<FleetCompanyExcel> companyListData(FleetCompanyPageDto req) throws ServiceException;

    /**
     * 用户分页
     *
     * @param req 查询条件
     * @return 分页数据
     * @throws ServiceException 服务内部异常
     */
    PageResult<FleetUserVo> userPage(FleetUserPageDto req) throws ServiceException;

    /**
     * 用户详情
     *
     * @param userId 员工id
     * @return 员工详情数据
     * @throws ServiceException 服务内部异常
     */
    FleetUserVo userDetail(Long userId) throws ServiceException;

    /**
     * fleet账号管理--停用用户
     *
     * @param userId 员工id
     * @throws ServiceException 服务内部异常
     */
    void userDeactivate(Long userId) throws ServiceException;

    /**
     * fleet账号管理--启用用户
     *
     * @param userId 员工id
     * @throws ServiceException 服务内部异常
     */
    void userEnable(Long userId) throws ServiceException;

    /**
     * 员工列表数据
     *
     * @param req 查询条件
     * @return 员工列表数据
     * @throws ServiceException 服务内部异常
     */
    List<FleetUserExcel> userListData(FleetUserPageDto req) throws ServiceException;

    /**
     * 用户退出登录，退出所有客户端
     *
     * @param userId 用户id
     * @throws ServiceException 服务内部异常
     */
    void userLogoff(Long userId) throws ServiceException;

}
