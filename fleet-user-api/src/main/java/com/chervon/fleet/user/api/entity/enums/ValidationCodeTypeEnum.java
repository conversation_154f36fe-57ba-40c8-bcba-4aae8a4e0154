package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**
 * @Description: 邮箱验证码类型
 * <AUTHOR>
 * @Date: 2020/8/29 13:34
 */
public enum ValidationCodeTypeEnum {
    /**
     * 数字类型验证码
     */
    NUMBER(0, "数字类型验证码"),
    /**
     * UUID随机码类型
     */
    UUID(1, "UUID随机码类型"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    ValidationCodeTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(ValidationCodeTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static ValidationCodeTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
