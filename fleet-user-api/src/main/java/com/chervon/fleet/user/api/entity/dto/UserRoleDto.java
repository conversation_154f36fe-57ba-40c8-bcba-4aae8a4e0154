package com.chervon.fleet.user.api.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 经销商角色实体类
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Data
@Accessors(chain = true)
public class UserRoleDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 角色Id *
     */
    private Long id;
    /**
     * 角色编码
     */
    private String code;
    /**
     * 角色名称
     */
    private String name;
    /**
     * 角色类型：0固定角色(PP创建),1自定义角色(DP经销商创建)
     */
    private Integer type;
    /**
     * 备注
     */
    private String description;

    /**
     * 角色对应的资源idList
     */
    private List<Long> resourceIdList;
}