package com.chervon.fleet.user.api.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet用户实体类
 * <AUTHOR>
 * @since 2023-07-5 10:38:34
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
	private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 全名
     */
    private String name;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 用户类型：1-超级管理员、2-管理员  3-staff员工
     */
    private Integer userType;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 账号状态：0未激活  1已激活（正常） 4已注销
     */
    private Integer accountStatus;
    /**
     * 邀请时间
     */
    private Long invitationTime;
    /**
     * 激活时间
     */
    private Long activationTime;

}