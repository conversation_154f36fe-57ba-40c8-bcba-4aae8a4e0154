package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 企业注册信息载体
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class CompanyDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 企业id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 绑定关联的管理员用户Id
     */
    @ApiModelProperty("绑定关联的管理员用户Id")
    private Long userId;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    private String companyName;
    /**
     * 是否启用 1：启用 0：停用
     */
    @ApiModelProperty("是否启用 1：启用 0：停用")
    private Integer enabled;
    /**
     * 租户状态：1-正常、2-停用、4-已注销
     */
    @ApiModelProperty("租户状态：1-正常、2-停用、4-已注销")
    private Integer status;
}
