package com.chervon.fleet.user.api.entity.query;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 租户查询对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "租户查询对象", description = "租户表")
public class CompanyQuery extends PageQuery<CompanyQuery> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户Id
     */
    private Long id;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 管理员用户Id
     */
    private Long userId;
    /**
     * 管理员用户Id集合
     */
    private List<Long> listUserId;
    /**
     * 管理员邮箱
     */
    private String email;
    /**
     * 管理员姓名
     */
    private String name;
    /**
     * 是否启用 1：启用 0：未启用
     */
    private Integer enabled;
    /**
     * 租户状态：1-正常、2-停用、4-已注销
     * @see com.chervon.fleet.user.api.entity.enums.CompanyStatusEnum
     */
    private Integer status;
    /**
     * 注册开始时间
     */
    private Long beginTime;
    /**
     * 注册结束时间
     */
    private Long endTime;

    public String getEmail() {
        return StringUtils.symbolFormat(email);
    }

    public String getCompanyName() {
        return StringUtils.symbolFormat(companyName);
    }


}
