package com.chervon.fleet.user.api.entity.query;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.List;

/**

 * 角色查询对象
 * <AUTHOR>
 * 
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "Role查询对象", description = "角色")
public class RoleQuery extends PageQuery {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码")
    private String code;
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String name;
    /**
     * 角色类型：0固定角色(PP创建),1自定义角色(DP经销商创建)
     */
    @ApiModelProperty(value = "角色类型：0固定角色 ,1自定义角色")
    private Integer type;
    /**
     * 角色描述
     */
    @ApiModelProperty(value = "角色描述")
    private String description;
    /**
     * 角色idList
     */
    @ApiModelProperty(value = "角色idList")
    private List<Long> roleIdList;
    /**
     * 创建人userId
     */
    @ApiModelProperty(value = "创建人userId")
    private Long userId;
    /**
     * 用户idList
     */
    @ApiModelProperty(value = "用户idList")
    private List<Long> userIdList;
    /**
     * companyId
     */
    @ApiModelProperty(value = "companyId")
    private Long companyId;

    /**
     * 获取格式化后的名称
     * @return
     */
    public String getName() {
        return StringUtils.symbolFormat(name);
    }

}
