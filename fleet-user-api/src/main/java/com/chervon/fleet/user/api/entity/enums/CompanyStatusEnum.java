package com.chervon.fleet.user.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 租户状态枚举：1-正常、2-停用、4-已注销
 * <AUTHOR>
 * @Date: 2023/7/6 13:34
 */
public enum CompanyStatusEnum implements TypeEnum {
    /**
     * 正常
     */
    NORMAL(1, "NORMAL", "正常"),
    /**
     * 停用
     */
    STOP_USING(2, "STOP_USING", "已停用"),
    /**
     * 已注销
     */
    CANCELED(4,"CANCELED","已注销")
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param value
     * @param desc
     */
    CompanyStatusEnum(int type, String value, String desc) {
        this.type = type;
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getValue(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(CompanyStatusEnum::getValue)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(CompanyStatusEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static CompanyStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
