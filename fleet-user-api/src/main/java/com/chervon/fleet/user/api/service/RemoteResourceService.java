package com.chervon.fleet.user.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.query.ResourceQuery;
import com.chervon.fleet.user.api.entity.vo.ResourceVo;
import java.util.List;

/**
 * 资源表(菜单按钮)服务接口
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
public interface RemoteResourceService {
    /**
     * 根据查询参数获取资源树 （子列表填充值） *
     * @param query 查询对象
     * @return 资源列表
     * @throws ServiceException 服务内部异常
     */
    List<ResourceVo> getResourceVoTree(ResourceQuery query) throws ServiceException;

    /**
     *  根据查询参数获取资源列表（子列表为空） *
     * @param query 查询对象
     * @return 资源列表
     * @throws ServiceException 服务内部异常
     */
    List<ResourceVo> getResourceList(ResourceQuery query) throws ServiceException;

    /**
     *  根据查询参数获取资源列表（子列表为空） *
     * @param query 查询对象
     * @return 资源列表
     * @throws ServiceException 服务内部异常
     */
    List<ResourceVo> getResourceVoList(ResourceQuery query) throws ServiceException;

    /**
     * 将列表资源转为树形tree列表返回 *
     * @param resourceList resourceList
     * @param flag 是否只查询出一二级页面：true是，false否(默认)
     * @return list
     * @throws ServiceException 服务内部异常
     */
    List<ResourceVo> convertToTreeList(List<ResourceVo> resourceList, boolean flag) throws ServiceException;
}
