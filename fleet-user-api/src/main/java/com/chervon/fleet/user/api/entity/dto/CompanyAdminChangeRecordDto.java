package com.chervon.fleet.user.api.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 租户管理员变更记录查询对象
 * @ClassName:CompanyAdminChangeRecordDto
 * <AUTHOR>
 * @date 2023/9/20 15:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompanyAdminChangeRecordDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;

}
