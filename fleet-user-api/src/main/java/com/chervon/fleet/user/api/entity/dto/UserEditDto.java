package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户表
 * <AUTHOR>
 * @since 2023/7/5
 */
@Data
@Accessors(chain = true)
@ApiModel("用户编辑Dto")
public class UserEditDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户ID")
    private Long id;
    @ApiModelProperty("名")
    private String firstName;
    @ApiModelProperty("姓")
    private String lastName;
    @ApiModelProperty("用户类型：1-超级管理员、2-管理员  3-staff员工")
    private Integer userType;
}
