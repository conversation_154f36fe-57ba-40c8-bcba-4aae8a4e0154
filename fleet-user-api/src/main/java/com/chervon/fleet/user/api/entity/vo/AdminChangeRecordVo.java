package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/2 17:34
 */
@Data
@Accessors(chain = true)
public class AdminChangeRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("管理员id")
    private Long adminId;

    @ApiModelProperty("管理员邮箱")
    private String adminEmail;

    @ApiModelProperty("管理员姓名1")
    private String adminFirstName;

    @ApiModelProperty("管理员姓名2")
    private String adminLastName;

    @ApiModelProperty("变更时间")
    private Long changeTime;
}
