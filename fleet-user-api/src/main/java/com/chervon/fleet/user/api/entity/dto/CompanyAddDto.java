package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 企业注册Dto
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class CompanyAddDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    private String companyName;
}
