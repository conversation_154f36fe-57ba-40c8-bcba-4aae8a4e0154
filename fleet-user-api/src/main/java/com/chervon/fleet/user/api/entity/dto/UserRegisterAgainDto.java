package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 重新邀请注册DTO
 * <AUTHOR>
 * @date 2023/8/24
 */
@Data
@Accessors(chain = true)
public class UserRegisterAgainDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    @ApiModelProperty("用户ID")
    private Long id;
}
