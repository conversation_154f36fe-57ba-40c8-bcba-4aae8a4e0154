package com.chervon.fleet.user.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户注册激活返回对象
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class UserRegisterActiveCheckVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("已有密码")
    private boolean hadPassword;
}
