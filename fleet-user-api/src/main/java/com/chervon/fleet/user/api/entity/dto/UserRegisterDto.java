package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户注册信息载体
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class UserRegisterDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 名字
     */
    @ApiModelProperty("名字")
    private String firstName;
    /**
     * 姓
     */
    @ApiModelProperty("姓")
    private String lastName;
    /**
     * 邮箱地址
     */
    @ApiModelProperty("邮箱地址")
    private String email;
    /**
     * 用户密码
     */
    @ApiModelProperty("密码")
    private String password;
    /**
     * 用户角色类型
     */
    @ApiModelProperty("用户角色类型： 0超管 1管理员 2普通用户")
    private Integer userType;

}
