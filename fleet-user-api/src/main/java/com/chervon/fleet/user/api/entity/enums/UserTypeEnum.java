package com.chervon.fleet.user.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;
import java.util.Arrays;

/**
 * 用户固定角色枚举：1-admin  2-manager 3-employee
 * <AUTHOR>
 */
public enum UserTypeEnum implements TypeEnum {
    //管理员不验证角色权限信息，加载所有资源
    /**
     * ADMIN 租户超级管理员
     * MANAGER 租户管理者
     * EMPLOYEE 普通员工
     */
    ADMIN(1, "Admin"),
    MANAGER(2,"Manager"),
    EMPLOYEE(3,"Employee"),
    ;

    /**
     * 用户类型id
     */
    private int type;
    /**
     * 用户类型描述
     */
    private String desc;

    UserTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(UserTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static UserTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
