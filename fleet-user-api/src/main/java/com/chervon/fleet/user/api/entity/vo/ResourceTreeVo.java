package com.chervon.fleet.user.api.entity.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**

 * 资源树对象

 *
 * <AUTHOR> @since 2020-08-12 18:45:03
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceTreeVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增长Id
     */
    private Long id;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 父资源id
     */
    private Long parentId;

    /**
     * 父资源名称
     */
    private String parentName;

    /**
     * 所属系统应用:
     */
    private Integer application;

    /**
     * 资源类型：菜单-页面-页签-按钮
     */
    private Integer type;

    /**
     * 资源级别：运营-客户-工作区
     */
    private Integer level;

    /**
     * 资源主体：pc，pda
     */
    private Integer device;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * url
     */
    private String url;

    /**
     * 子资源列表
     */
    @TableField(exist = false)
    private List<ResourceTreeVo> childList;

}
