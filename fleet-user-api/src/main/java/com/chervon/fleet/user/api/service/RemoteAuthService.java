package com.chervon.fleet.user.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.dto.LoginDto;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;

/**
 * fleet用户登录注销服务接口
 *
 * <AUTHOR>
 * @description 用户远程登录校验接口服务
 * @since 2022-09-20 14:28:36
 */
public interface RemoteAuthService {

    /**
     * 获取公钥
     *
     * @return 公钥字符串
     */
    String getPublicKey();

    /**
     * 用户登录接口 *
     *
     * @param loginDto 登录参数
     * @return 登录结果
     * @throws ServiceException 服务内容异常
     */
    LoginResultVo login(LoginDto loginDto) throws ServiceException;

    /**
     * * 注销用户登录状态
     *
     * @return 注销结果
     */
    Boolean logout();

    /**
     * 请求黑白名单验证
     *
     * @param method 方法
     * @param url URL
     * @return 是否在黑白名单中
     * @throws ServiceException 服务内部异常
     */
    Boolean checkRequestList(String method, String url) throws ServiceException;

    /**
     * 获取用户登录信息(解析JWT)
     *
     * @param token 登录token
     * @return 登录信息
     * @throws ServiceException 服务内部异常
     */
    LoginResultVo getUserInfoByJwt(String token) throws ServiceException;

    /**
     * 获取用户登录信息(查库)
     *
     * @param token 登录token
     * @return 登录信息
     */
    LoginResultVo getUserInfoByDb(String token);

    /**
     * 刷洗用户Token
     *
     * @param token 当前用户登录token
     * @return 用户信息
     * @throws ServiceException 服务内部异常
     */
    LoginResultVo refresh(String token) throws ServiceException;
}
