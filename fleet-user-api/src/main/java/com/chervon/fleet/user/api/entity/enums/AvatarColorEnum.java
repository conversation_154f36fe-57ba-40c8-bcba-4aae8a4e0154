package com.chervon.fleet.user.api.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.security.SecureRandom;
import java.util.Random;

/**
 * avatar color enum 头像随机颜色枚举
 * <AUTHOR>
 * @since 2023-08-25 16:05
 **/
@AllArgsConstructor
@Getter
public enum AvatarColorEnum {
    /**
     * #FFC736 #00CE84 #8D92FF #FFB4F7 #A0C1B5 #FFD8C2
     */
    YELLOW("#FFC736"),
    CYAN("#00CE84"),
    BLUE("#8D92FF"),
    PINK("#FFB4F7"),
    LIGHT_GREEN("#A0C1B5"),
    ORANGE("#FFD8C2");

    /**
     * 颜色色号
     */
    private String colorValue;

    /**
     * 随机获取颜色值
     *
     * @return 颜色值
     */
    public static String randomColor() {
        Random random =new SecureRandom();
        return AvatarColorEnum.values()[random.nextInt(AvatarColorEnum.values().length)].getColorValue();
    }
}
