package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 隐私政策和用户须知枚举
 * @Date: 2023/5/28 13:34
 */
public enum AgreementTypeEnum {
    /**
     * 用户须知
     */
    USER_NOTICE(1, "user"),
    /**
     * 隐私政策
     */
    PRIVACY_POLICY(2, "secret");
    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    AgreementTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(AgreementTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static AgreementTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}