package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户协议验证结果对象
 */
@Data
@ApiModel("用户协议撤销对象")
@Accessors(chain = true)
public class AgreementWithdrawDto {

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 撤销的用户协议Id
     */
    @ApiModelProperty("最新版用户协议Id")
    private Long userAgreementId;

    /**
     * 撤销的隐私政策Id
     */
    @ApiModelProperty("隐私政策Id")
    private Long privacyAgreementId;
}
