package com.chervon.fleet.user.api.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: 用户分页查询对象
 * <AUTHOR>
 * @date 2023/8/2 16:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FleetUserPageDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 注册开始时间
     */
    @ApiModelProperty("注册开始时间")
    private Long registerStartTime;
    /**
     * 注册结束时间
     */
    @ApiModelProperty("注册结束时间")
    private Long registerEndTime;
    /**
     * 激活开始时间
     */
    @ApiModelProperty("激活开始时间")
    private Long activeStartTime;
    /**
     * 激活结束时间
     */
    @ApiModelProperty("激活结束时间")
    private Long activeEndTime;
    /**
     * 账户id
     */
    @ApiModelProperty("账户id")
    private String userId;
    /**
     * 员工邮箱
     */
    @ApiModelProperty("员工邮箱")
    private String userEmail;
    /**
     * 员工姓名
     */
    @ApiModelProperty("员工姓名")
    private String userName;
    /**
     * 员工角色
     */
    @ApiModelProperty("员工角色")
    private List<String> userRoles;
    /**
     * 所属租户id
     */
    @ApiModelProperty("所属租户id")
    private String companyId;
    /**
     * 所属租户名称
     */
    @ApiModelProperty("所属租户名称")
    private String companyName;
    /**
     * 员工账户来源
     */
    @ApiModelProperty("员工账户来源")
    private List<String> userSourceTypes;
    /**
     * 邀请人邮箱
     */
    @ApiModelProperty("邀请人邮箱")
    private String invitationEmail;
    /**
     * 用户状态
     */
    @ApiModelProperty("用户状态")
    private List<String> userStates;
    /**
     * 启用状态 0 未启用 1 启用
     */
    @ApiModelProperty("启用状态 0 未启用 1 启用")
    private Integer isEnabled;

}
