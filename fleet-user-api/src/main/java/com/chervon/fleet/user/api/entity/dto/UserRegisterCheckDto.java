package com.chervon.fleet.user.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户注册验证载体
 * <AUTHOR>
 * @date 2023/6/27
 */
@Data
@Accessors(chain = true)
public class UserRegisterCheckDto implements Serializable {
    private static final long serialVersionUID = 8317991274011459205L;
    /**
     * 邮箱地址
     */
    @ApiModelProperty("邮箱地址")
    private String email;
    /**
     * 验证码
     */
    @ApiModelProperty("验证码")
    private String verificationCode;
    @ApiModelProperty("姓名")
    private String firstName;
    @ApiModelProperty("姓氏")
    private String lastName;
    @ApiModelProperty("公司ID")
    private Long companyId;
}
