package com.chervon.fleet.user.api.entity.enums;

import java.util.Arrays;

/**

 * 状态

 *
 * <AUTHOR> 
 */
public enum ResourceTypeEnum {
    /**
     * 菜单
     */
    MENU(1, "菜单"),
    /**
     * 页面
     */
    PAGE(3, "页面"),
    TAB(5, "页签"),
    BUTTON(7, "按钮"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    ResourceTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(ResourceTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static ResourceTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
