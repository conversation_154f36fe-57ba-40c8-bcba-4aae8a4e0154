package com.chervon.fleet.user.api.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 操作日志查询对象
 * * <AUTHOR> 2022/10/30
 */
@Data
public class OperateLogQuery extends PageQuery {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日志编号")
    private Long id;

    @ApiModelProperty(value = "用户列表")
    private List<Long> userIds;

    @ApiModelProperty(value = "用户名/邮箱")
    private String userName;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "操作日志类型")
    private Integer operateType;

    @ApiModelProperty(value = "操作开始时间")
    private Long timeBegin;

    @ApiModelProperty(value = "操作结束时间")
    private Long timeEnd;

    @ApiModelProperty(value = "组织")
    private String regionName;

    @ApiModelProperty(value = "角色Id")
    private Long roleId;
}
