package com.chervon.fleet.user.api.service;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.dto.*;
import com.chervon.fleet.user.api.entity.query.UserPageQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.entity.vo.UserRegisterActiveCheckVo;
import com.chervon.fleet.user.api.entity.vo.UserSyncVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;

import java.util.List;

/**
 * fleet远程访问用户服务接口
 *
 * <AUTHOR>
 * @description 用户远程登录校验接口服务
 * @since 2022-09-20 14:28:36
 */
public interface RemoteUserService {
    /**
     * 根据查询条件查询用户信息
     *
     * @param userQuery 查询条件
     * @return 用户列表
     * @throws ServiceException 服务内部异常
     */
    List<UserVo> getList(UserQuery userQuery) throws ServiceException;

    /**
     * 忘记密码等根据邮箱获取有效的用户信息
     * @param userQuery 查询条件
     * @return 用户对象
     * @throws ServiceException 服务内部异常
     */
    UserVo getValidUser(UserQuery userQuery);

    /**
     * * power-bi看板权限验证同步最近三天修改的用户信息
     *
     * @return 用户列表
     * @throws ServiceException 服务内部异常
     */
    List<UserSyncVo> getUserSyncList() throws ServiceException;

    /**
     * * web用户分页列表
     *
     * @param query 查询条件：名字，角色，状态
     * @param companyId 公司id
     * @return 分页列表结果
     * @throws ServiceException 服务内部异常
     */
    PageResult<UserVo> page(UserPageQuery query, Long companyId) throws ServiceException;

    /**
     * 根据查询条件查询用户信息
     *
     * @param userQuery 查询条件
     * @return 用户对象
     * @throws ServiceException 服务内部异常
     */
    UserVo getDetail(UserQuery userQuery) throws ServiceException;

    /**
     * * 查询用户是否存在
     *
     * @param userQuery 查询条件
     * @return 是否存在
     * @throws ServiceException 服务内部异常
     */
    Boolean userExist(UserQuery userQuery) throws ServiceException;

    /**
     * * 修改密码验证旧密码接口
     *
     * @param modifyPasswordDto 修改密码对象
     * @return 返回校验结果
     * @throws ServiceException 服务内部异常
     */
    Boolean modifyPasswordCheck(ModifyPasswordDto modifyPasswordDto) throws ServiceException;

    /**
     * 修改密码设置新密码
     *
     * @param modifyPasswordDto 修改密码对象
     * @return 返回修改结果
     * @throws ServiceException 服务异常,用于RPC正常输出Exception
     */
    Boolean modifyPassword(ModifyPasswordDto modifyPasswordDto) throws ServiceException;

    /**
     * * 忘记密码检查验证码是否过期
     *
     * @param forgetPasswordRequestDto 忘记密码请求对象
     * @return 返回验证结果
     * @throws ServiceException 服务内部异常
     */
    Boolean forgetPasswordCheck(ForgetPasswordRequestDto forgetPasswordRequestDto) throws ServiceException;

    /**
     * * 忘记密码设置新密码
     *
     * @param forgetPasswordRequestDto 忘记密码请求对象
     * @return 返回设置结果
     * @throws ServiceException 服务内部异常
     */
    Boolean forgetPasswordSetNewPassword(ForgetPasswordRequestDto forgetPasswordRequestDto) throws ServiceException;

    /**
     * 管理员自助注册
     *
     * @param registerDto 注册对象
     * @return 用户对象
     * @throws ServiceException 服务内部异常
     */
    UserVo register(UserRegisterDto registerDto) throws ServiceException;

    /**
     * 激活用户
     *
     * @param activityDto 激活对象
     * @throws ServiceException 服务内部异常
     */
    void activityUser(UserActivityDto activityDto) throws ServiceException;

    /**
     * 邀请员工注册1：发送邀请邮件并创建用户
     *
     * @param registerDto 注册对象
     * @return 返回创建结果
     * @throws ServiceException 服务内部异常
     */
    Boolean inviteRegister(UserRegisterDto registerDto) throws ServiceException;

    /**
     * 邀请员工注册1.5: 重新发送邀请注册邮件
     *
     * @param id 用户ID
     * @throws ServiceException 服务内部异常
     */
    void inviteRegisterAgain(Long id) throws ServiceException;

    /**
     * 邀请员工注册2：点击邮件中的跳转链接进行激活的验证码检查是否过期
     *
     * @param userActivityCheckDto 请求对象
     * @return 激活的用户的ID
     * @throws ServiceException 服务内部异常
     */
    UserRegisterActiveCheckVo activityCheck(UserRegisterCheckDto userActivityCheckDto) throws ServiceException;

    /**
     * * 点击邮件中的跳转链接进行注册的验证码检查是否过期
     *
     * @param userRegisterCheckDto 请求对象
     * @return 返回验证结果
     * @throws ServiceException 服务内部异常
     */
    Boolean registerCheck(UserRegisterCheckDto userRegisterCheckDto) throws ServiceException;

    /**
     * * 编辑用户
     *
     * @param userDto 请求对象
     * @return 返回编辑结果
     * @throws ServiceException 服务内部异常
     */
    Boolean edit(UserEditDto userDto) throws ServiceException;

    /**
     * * 逻辑删除用户
     *
     * @param userId 用户id
     * @return 返回删除结果
     * @throws ServiceException 服务内部异常
     */
    Boolean delete(Long userId) throws ServiceException;
    /**
     * 用户转为游离状态（解除租户绑定关系和未激活状态）
     * @param userId 用户id
     * @throws ServiceException 服务内部异常
     */
    void freeUser(Long userId) throws ServiceException;
    /**
     * * 管理员角色转移
     *
     * @param request 请求参数
     * @return 转移结果
     * @throws ServiceException 服务内部异常
     */
    Boolean transferAdmin(TransferAdminDto request) throws ServiceException;

    /**
     * 注册用户-1：注册管理员用户发送邮件
     *
     * @param email 邮箱
     * @throws ServiceException 服务内部异常
     */
    void registerSendMail(String email) throws ServiceException;

    /**
     * 获取用户登录结果
     * @param userId 用户id
     * @return 登录结果
     * @throws ServiceException 服务自定义异常
     */
    LoginResultVo getLoginResultVoById(Long userId) throws ServiceException;

}
