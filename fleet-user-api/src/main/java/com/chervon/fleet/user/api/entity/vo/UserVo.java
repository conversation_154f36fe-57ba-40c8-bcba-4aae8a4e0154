package com.chervon.fleet.user.api.entity.vo;

import com.chervon.fleet.user.api.entity.enums.UserTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet用户信息
 * <AUTHOR>
 * @date 2023/6/11
 */
@Data
@Accessors(chain = true)
@ApiModel("用户信息")
public class UserVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户Id")
    private Long id;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("名")
    private String firstName;
    @ApiModelProperty("姓")
    private String lastName;
    @ApiModelProperty("用户全名")
    private String name;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("用户类型: 1超级管理员 2普通管理员 3员工")
    private Integer userType;
    @ApiModelProperty("公司id(租户Id)")
    private Long companyId;
    @ApiModelProperty("邀请人邮箱")
    private String inviterAddress;
    @ApiModelProperty("邀请时间")
    private Long invitationTime;
    @ApiModelProperty("激活时间")
    private Long activationTime;
    @ApiModelProperty("账号状态: 0未激活 1已激活(正常)")
    private Integer accountStatus;
    @ApiModelProperty("是否能重新邀请: 0否 1是")
    private Integer canInviteAgain = 0;
    @ApiModelProperty("头像颜色号")
    private String avatarColor;
}
