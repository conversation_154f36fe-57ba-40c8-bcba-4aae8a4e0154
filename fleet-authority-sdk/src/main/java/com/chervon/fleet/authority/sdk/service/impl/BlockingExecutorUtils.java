package com.chervon.fleet.authority.sdk.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.chervon.fleet.user.api.config.GatewayProperties;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import java.util.concurrent.*;

/**
 * 阻塞执行线程池工具
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
@Component
public class BlockingExecutorUtils {
    private static ExecutorService EXECUTOR_POOL = null;
    GatewayProperties properties;
    public BlockingExecutorUtils() {
        properties=new GatewayProperties();
        if (EXECUTOR_POOL == null) {
            EXECUTOR_POOL = new ThreadPoolExecutor(this.properties.getBlocking().getInitialSize()
                    , this.properties.getBlocking().getMaxActive()
                    , this.properties.getBlocking().getMaxIdleTime()
                    , TimeUnit.MILLISECONDS
                    , new LinkedBlockingQueue<>(this.properties.getBlocking().getMaxWaitingTask())
                    , ThreadUtil.newNamedThreadFactory("AuthBlockingExecutor",false)
                    , new ThreadPoolExecutor.AbortPolicy());
            Runtime.getRuntime().addShutdownHook(new Thread(() -> EXECUTOR_POOL.shutdown()));
        }
    }

    public void execute(Runnable task) {
        EXECUTOR_POOL.execute(task);
    }

    @SneakyThrows
    public <E> E execute(Callable<E> task) {
        return EXECUTOR_POOL.submit(task).get();
    }
}
