package com.chervon.fleet.authority.sdk.service.impl;

import com.chervon.common.core.utils.SpringUtils;
import com.chervon.fleet.authority.sdk.service.AuthProcessService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 认证服务工厂，支持多个系统服务认证选择
 * <AUTHOR> 2022/10/28
 */
@Component
public class AuthServiceFactory {

    private static Map<String, AuthProcessService> handleCollection=new ConcurrentHashMap<>();

    /**
     * 如果有多个系统鉴权可以从请求头中读取参数：HeaderUtils.getSystem();
     * @return
     */
    public static AuthProcessService getInstance(){
        final String system = "fleet";
        if(CollectionUtils.isEmpty(handleCollection)){
            initInstance();
        }
        return handleCollection.get(system);
    }

    private static synchronized void initInstance() {
        final Map<String, AuthProcessService> beansOfMap = SpringUtils.getBeansOfType(AuthProcessService.class);
        for (AuthProcessService bean : beansOfMap.values()) {
            handleCollection.put(bean.getInstanceCode(), bean);
        }
    }

}
