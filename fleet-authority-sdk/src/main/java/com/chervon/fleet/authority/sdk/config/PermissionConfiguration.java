package com.chervon.fleet.authority.sdk.config;

import com.chervon.fleet.authority.sdk.interceptor.PermissionInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.MappedInterceptor;

import java.util.Arrays;
import java.util.List;

/**
 * 权限安全拦截器注册
 * <AUTHOR>
 */
@Configuration
public class PermissionConfiguration implements WebMvcConfigurer {
    /**
     * *.html"是放行所有static下面的html文件
     * 排除的扩展名
     */
    private static final List<String> EXCLUDE_PATH = Arrays.asList("/","/doc.html","/v3/api-docs","/favicon.ico","css/**", "js/**", "img/**", "json/**", "fonts/**","/*.html","/druid/**","/webjars/**","/swagger/**","/v2/**","/swagger-ui.html/**","/swagger-resources/**");

    @Autowired
    private PermissionInterceptor permissionInterceptor;

    /**
     * 这个方法是用来配置静态资源的，比如html，js，css，等等
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /*静态资源的位置*/
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("/templates/**").addResourceLocations("classpath:/templates/");
        /*放行swagger*/
        registry.addResourceHandler("swagger-ui.html","doc.html","/favicon.ico","/v3/api-docs")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    /**
     * 跨域访问设置
     * @param registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 添加映射路径
        registry.addMapping("/**")
                // 是否发送Cookie
                .allowCredentials(true)
                // 设置放行哪些原始域 SpringBoot2.4.4下低版本使用.allowedOrigins("*")
                .allowedOriginPatterns("*")
                // 放行哪些请求方式
                .allowedMethods(new String[] { "GET", "POST", "PUT", "DELETE" })
                // .allowedMethods("*") //或者放行全部
                // 放行哪些原始请求头部信息
                .allowedHeaders("*");
    }

    /**
     * 拦截器注册
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(permissionInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(EXCLUDE_PATH);
    }

}
