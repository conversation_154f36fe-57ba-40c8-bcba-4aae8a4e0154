package com.chervon.fleet.authority.sdk.interceptor;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.web.entity.ClientInfo;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.authority.sdk.service.AuthProcessService;
import com.chervon.fleet.authority.sdk.service.impl.AuthServiceFactory;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.service.RemoteAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * 权限认证拦截器
 * <AUTHOR>
 * @date 2023/6/5 15:55
 */
@Component
@Slf4j
public class PermissionInterceptor implements HandlerInterceptor {

     @DubboReference
     private RemoteAuthService remoteAuthService;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest req, @NotNull HttpServletResponse rep, @NotNull Object handler) {
        String path = req.getRequestURI();
        String method = req.getMethod();//POST,GET
        //跨域请求处理
        if (HttpMethod.OPTIONS.name().equals(method)) {
            return true;
        }
        inputCheck();

        ClientInfo clientInfo = UserContext.getClientInfo();
        clientInfo.setDeviceId(HeaderUtils.getDeviceId());
        clientInfo.setDeviceType(HeaderUtils.getDeviceType());
        clientInfo.setLanguage(HeaderUtils.getLanguage());
        //黑白名单验证
        final Boolean result = remoteAuthService.checkRequestList(method, path);
        if(result){//白名单直接通过
            return true;
        }
        final String token = userCheck(clientInfo);

        //根据请求系统参数获取处理鉴权处理器实例
        AuthProcessService processService= AuthServiceFactory.getInstance();
        R<LoginResultVo> loginResult = processService.process(token);
        final LoginResultVo entry = loginResult.getEntry();

        UserContext.setUserName(entry.getUserName());
        UserContext.setName(entry.getName());
        UserContext.setUserType(entry.getUserType());
        UserContext.setCompanyId(entry.getCompanyId());
        //登录失败返回错误码
        if (loginResult.isStatus()) {
            return true;
        }
        return false;
    }

    @org.jetbrains.annotations.NotNull
    private static String userCheck(ClientInfo clientInfo) {
        //验签需要userId获取盐值
        Long userId = HeaderUtils.getUserId();
        if (Objects.isNull(userId) || userId.equals(0L)) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"uid");
        }
        clientInfo.setUserId(userId);
        //读取token验签
        String token = HeaderUtils.getToken();
        if (!StringUtils.hasText(token)) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"token");
        }
        return token;
    }

    private static void inputCheck() {
        //设备类型必填
        if (Objects.isNull(HeaderUtils.getDeviceType())) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"deviceType");
        }
        //语言必填
        if (!StringUtils.hasText(HeaderUtils.getLanguage())) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"lang");
        }
    }
}
