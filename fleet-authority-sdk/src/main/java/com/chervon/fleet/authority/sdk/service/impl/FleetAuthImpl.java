package com.chervon.fleet.authority.sdk.service.impl;

import com.chervon.common.core.domain.R;
import com.chervon.fleet.authority.sdk.service.AuthProcessService;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.service.RemoteAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * fleet授权认证
 * <AUTHOR> 2022/10/28
 */
@Slf4j
@Service
public class FleetAuthImpl implements AuthProcessService<LoginResultVo> {
    private static final String FLEET_SYS_HEADER="fleet";
    @DubboReference
    RemoteAuthService remoteAuthService;

    @Override
    public String getInstanceCode() {
        return FLEET_SYS_HEADER;
    }

    /**
     * 执行dubbo调用认证
     * @param token
     * @return
     */
    @Override
    public R<LoginResultVo> process(String token) {
        final LoginResultVo loginInfo = remoteAuthService.getUserInfoByJwt(token);
        return R.ok(loginInfo);
    }
}
