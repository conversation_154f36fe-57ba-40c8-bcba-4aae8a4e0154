package com.chervon.fleet.web.entity.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description:维保导出
 * <AUTHOR>
 * @date 2023/7/25 14:06
 */
@Data
@Accessors(chain = true)
public class ReportMaintenanceExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    @Alias("Name")
    private String name;

    @Alias("SN")
    private String sn;

    @Alias("Category")
    private String category;

    @Alias("Model#")
    private String model;

    @Alias("Service Type")
    private String serviceType;

    @Alias("Maintenance Date")
    private String maintenanceDate;

    @Alias("Runtime")
    private Integer runtime;

    @Alias("Labor Time")
    private Integer laborTime;

    @Alias("Expenses")
    private String expenses;

    @Alias("Performed By")
    private String performedBy;

    @Alias("Contents")
    private String contents;

    public String getName() {
        return CsvUtil.format(this.name);
    }

    public String getSn() {
        return CsvUtil.format(this.sn);
    }

    public String getCategory() {
        return CsvUtil.format(this.category);
    }

    public String getModel() {
        return CsvUtil.format(this.model);
    }

    public String getServiceType() {
        return CsvUtil.format(this.serviceType);
    }

    public String getMaintenanceDate() {
        return CsvUtil.format(this.maintenanceDate);
    }

    public String getRuntime() {
        return CsvUtil.format(this.runtime);
    }

    public String getLaborTime() {
        return CsvUtil.format(this.laborTime);
    }

    public String getExpenses() {
        return CsvUtil.format(this.expenses);
    }

    public String getPerformedBy() {
        return CsvUtil.format(this.performedBy);
    }

    public String getContents() {
        return CsvUtil.format(this.contents);
    }
}
