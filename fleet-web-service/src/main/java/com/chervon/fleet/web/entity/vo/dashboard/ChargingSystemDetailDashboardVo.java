package com.chervon.fleet.web.entity.vo.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充电系统详情看板Vo
 * <AUTHOR>
 * @since 2023-07-31 14:54
 **/
@Data
@ApiModel("充电系统详情看板Vo")
public class ChargingSystemDetailDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;
    /**
     * hub设备id(父设备id)
     */
    @ApiModelProperty("hub设备id(父设备id)")
    private String hubDeviceId;
    /**
     * 仓位号，UI界面中根据该字段展示格子的顺序
     */
    @ApiModelProperty("仓位号，UI界面中根据该字段展示格子的顺序")
    private String portNumber;
    /**
     * 充电仓适配器名称，UI界面中根据该字段展示格子的标题
     */
    @ApiModelProperty("充电仓适配器名称，UI界面中根据该字段展示格子的标题")
    private String adaptorName;
    /**
     * 充电仓适配器类别编码
     */
    @ApiModelProperty("充电仓适配器类别编码")
    private String adaptorCategoryCode;
    /**
     * 仓位状态：1:正常充电(绿色)  2等待充电(灰色) 3:电池过温(橙色) 4:电池故障(红色) -8:Legacy Battery(蓝色)
     */
    @ApiModelProperty("仓位状态：1:正常充电(绿色)  2等待充电(灰色) 3:电池过温(橙色) 4:电池故障(红色) -8:Legacy Battery(蓝色)")
    private Integer portStatus;
    /**
     * 电池设备id，UI界面中根据该字段展示格子内部设备的顺序
     */
    @ApiModelProperty("电池设备id，UI界面中根据该字段展示格子内部设备的顺序")
    private String batteryDeviceId;
    /**
     *  仓位电池类型：8Ah、40Ah、Legacy Battery
     */
    @ApiModelProperty("仓位电池类型：8Ah、40Ah、Legacy Battery")
    private String batteryType;
    /**
     * 充电百分比
     */
    @ApiModelProperty("充电百分比")
    private BigDecimal chargingPercentage;
    /**
     * 已充电安时数
     */
    @ApiModelProperty("已充电安时数")
    private BigDecimal chargingEnergyAh;
    /**
     * 是否有故障：0无   1有
     */
    @ApiModelProperty("是否有故障：0无   1有")
    private Integer isFault;
    /**
     * 充电仓适配器状态: 0保留 1正常充电 2等待充电 3电池过温 4电池故障
     */
    @ApiModelProperty("充电仓适配器状态: 0保留 1正常充电 2等待充电 3电池过温 4电池故障")
    private Integer adaptorState;
}
