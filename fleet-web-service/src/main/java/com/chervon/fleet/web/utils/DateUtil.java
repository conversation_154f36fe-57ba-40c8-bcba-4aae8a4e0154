package com.chervon.fleet.web.utils;

import com.alibaba.csp.sentinel.util.function.Tuple2;
import com.chervon.fleet.web.entity.consts.NumberConst;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date :2022/12/15 14:19
 * @description :
 * @modyified By:
 */
@Slf4j
public class DateUtil {
	/**
	 * 获取12小时前的时间
	 * @param hours
	 * @return
	 */
	public static Date getHoursBefore(int hours) {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime twelveHoursAgo = now.minus(Duration.ofHours(hours));
		return Date.from(twelveHoursAgo.atZone(ZoneId.systemDefault()).toInstant());
	}
	/**
	 * 时间戳转成LocalDate
	 * @param time
	 * @return
	 */
	public static LocalDate longToLocalDate(Long time) {
		return Instant.ofEpochMilli(time).atZone(ZoneId.systemDefault()).toLocalDate();

	}
	/**
	 * LocalDate转时间戳
	 * @param date
	 * @return
	 */
	public static Long localDateToLong(LocalDate date) {
		return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
	}

	/**
	 * LocalDate转时间戳
	 * @param dateTime
	 * @return
	 */
	public static Long localDateTimeToLong(LocalDateTime dateTime) {
		return dateTime.toInstant(ZoneOffset.ofHours(NumberConst.TIME_ZONE_8)).toEpochMilli();
	}

	/**
	 * LocalDateTime转Date
	 * @param localDateTime
	 * @return
	 */
	public static Date getDate(LocalDateTime localDateTime) {
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * * 字符串转日期
	 * @param strDate
	 * @return
	 */
	public static Date getDateByStr(String strDate){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			return sdf.parse(strDate);
		} catch (ParseException e) {
			log.error("parse string to date error:{}",e);
			return null;
		}
	}

	/**
	 * 获取当前日期的开始时间和结束时间Ts
	 * @param strDate
	 * @return
	 */
	public static Tuple2<Long,Long> getDayBeginEndTs(String strDate) {
		LocalDate date = LocalDate.parse(strDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		// 获取当日开始时间
		LocalDateTime startOfDay = date.atTime(LocalTime.MIN);
		long startOfDayMillis = startOfDay.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

		// 获取当日结束时间
		LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
		long endOfDayMillis = endOfDay.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
		return Tuple2.of(startOfDayMillis, endOfDayMillis);
	}
}