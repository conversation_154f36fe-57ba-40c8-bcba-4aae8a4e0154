package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiCategoryCount;
import com.chervon.fleet.web.entity.vo.dashboard.CategoryCountDashboardVo;
import com.chervon.fleet.web.mapper.BiCategoryCountMapper;
import com.chervon.fleet.web.service.BiCategoryCountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备分类数量统计表（看板：Machine Category）服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiCategoryCountServiceImpl extends ServiceImpl<BiCategoryCountMapper, BiCategoryCount>
    implements BiCategoryCountService {

    @Override
    public List<CategoryCountDashboardVo> categoryCount(Long companyId) {
        List<BiCategoryCount> biCategoryCountList = this.list(new LambdaQueryWrapper<BiCategoryCount>()
            .eq(BiCategoryCount::getCompanyId, companyId)
            .gt(BiCategoryCount::getCategoryCount, 0)
            .orderByAsc(BiCategoryCount::getCategoryName));
        if (CollectionUtils.isEmpty(biCategoryCountList)) {
            return new ArrayList<>();
        }
        return ConvertUtil.convertList(biCategoryCountList, CategoryCountDashboardVo.class);
    }
}