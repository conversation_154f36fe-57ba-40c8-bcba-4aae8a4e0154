package com.chervon.fleet.web.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25 14:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportInventoryDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 区分客户端环境区域：欧洲还是北美：na eu
     */
    @ApiModelProperty("区域：欧洲还是北美：na eu")
    private String region;
}
