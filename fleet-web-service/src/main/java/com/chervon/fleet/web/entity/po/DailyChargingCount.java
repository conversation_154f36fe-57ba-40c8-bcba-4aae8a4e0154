package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 统计租户Power HUB在统计时段内每天给电池充电的总电量(t_data_daily_charging)实体类
 *
 * <AUTHOR>
 * @since 2023-08-30 10:34:00
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class DailyChargingCount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 日期：2023-07-12
     */
    private String date;
    /**
     * 当日总充电能量：2027：总充电能量
     */
    private Integer chargingEnergy;
}