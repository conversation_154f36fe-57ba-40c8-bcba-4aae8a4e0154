package com.chervon.fleet.web.controller;

import com.chervon.fleet.web.api.entity.dto.GatewayScanReportingDto;
import com.chervon.fleet.web.api.entity.enums.GatewayTypeEnum;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.entity.dto.WarehouseStatusCache;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.RuleEngineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 设备库存相关接口
 * <AUTHOR> 2023/10/10
 */
@Slf4j
@RestController
@RequestMapping("/inventory")
@Api(tags = "库存状态测试接口")
public class InventoryController {

    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private RuleEngineService ruleEngineService;

    /**
     * 测试方法:"topic": "aws/fleet/gateway/2/1721367539448885250/scanStatus",
     * @param gatewayScanReportingDto
      {
        "devices": [
          {
            "deviceId": "NCH16233600024X",
            "reportTime": 1699241634877,
            "rssi": -71,
            "status": 1
          },{
            "deviceId": "XLM256330458042",
            "reportTime": 1699241634877,
            "rssi": -60,
            "status": 1
          }
        ],
        "gateway": {
          "gatewayType": 2,
          "location": "31.9300022,118.8021983",
          "reportTime": 1699241634876
        }
      }

    {"payLoad":{"messageBody":{"gatewayId":"1721367539448885250","gatewayType":2,"listDevice":[{"deviceId":"NCH16233600024X","rssi":-71,"warehouseStatus":2},{"deviceId":"XLM256330458042","rssi":-60,"warehouseStatus":2}]},"messageId":"21f9cf1ef0f44d87a58485ebd5931cda","messageType":1},"qos":1,"topic":"aws/fleet/gateway/scanStatus/get/accepted"}
     */
    @ApiOperation("刷新设备库存状态-test")
    @PostMapping(value = "/refreshStatus")
    public void refreshWarehouseStatus(@RequestBody GatewayScanReportingDto gatewayScanReportingDto) {
        // 1硬件网关 2软网关
        Integer type = GatewayTypeEnum.SOFT.getType();
        // 网关id
        String gatewayId = "";
        if (Objects.isNull(gatewayScanReportingDto)
                || Objects.isNull(gatewayScanReportingDto.getGateway())
                || CollectionUtils.isEmpty(gatewayScanReportingDto.getDevices())) {
            log.error("刷新库存状态请求参数为空！");
            return;
        }
        //数据上报处理主逻辑
        ruleEngineService.refreshWarehouseStatus(gatewayScanReportingDto, type, gatewayId);
    }
    /**
     * 测试方法
     * @param query
     */
    @ApiOperation("查询设备库存状态-test")
    @PostMapping(value = "/getStatus")
    public List<DeviceStatusVo> getWarehouseStatus(@RequestBody InventoryStatusQuery query) {
        return ruleEngineService.getWarehouseStatusByCache(query);
    }

    @ApiOperation("清除设备库存状态缓存-test")
    @GetMapping(value = "/delCache")
    public Integer deleteWarehouseStatusCache(@RequestParam("companyId") Long companyId) {
        return companyDeviceService.clearWarehouseStatus(companyId);
    }
}
