package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-14 16:55
 **/
@Data
@TableName("t_data_total_tool_usage")
public class TotalToolUsage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备总使用时长（单位：秒 不受绑定解绑影响）
     */
    private Integer usageDuration;
    /**
     * 设备总开机次数（不受绑定解绑影响）
     */
    private Integer numberTimes;
    /**
     * 设备一级分类编号
     */
    private String categoryCode;
    /**
     * 更新时间
     */
    private Long modifyTime;
}
