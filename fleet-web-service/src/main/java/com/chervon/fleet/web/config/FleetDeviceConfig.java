package com.chervon.fleet.web.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 配置类
 * <AUTHOR>
 * @date 2022/7/22 16:33
 */
@Data
@Component
@ConfigurationProperties(prefix = "device")
@RefreshScope
public class FleetDeviceConfig {
    /**
     * 12小时
     */
    public static final int HOUR12 = 12;

    private Sub sub;
    /**
     * 12小时毫秒数
     */
    private static final Long REAL_TIME_DATA_EXPIRATION = 43200000L;
    /**
     * 5分钟毫秒数
     */
    private static final Long DURATION_5_MINUTES = 300000L;

    @Data
    public static class Sub implements Serializable {

        private static final long serialVersionUID = 1;

        private List<String> exclude = new ArrayList<>();
    }

    /**
     * 大数据实时数据是否过期：
     * dc-dc dc模式充电器详情及挂载电池包是否过期
     * @param time 数据最后刷新时间
     * @return 过期
     */
    public static boolean isExpired12Hours(Date time){
        if(Objects.isNull(time)){
            return true;
        }
        if(System.currentTimeMillis()-time.getTime() > REAL_TIME_DATA_EXPIRATION){
            return true;
        }
        return false;
    }

    /**
     * 大数据实时数据是否过期：
     * dc-dc dc模式充电器详情及挂载电池包是否过期
     * @param time 数据最后刷新时间
     * @return 过期
     */
    public static boolean isExpired5Minutes(Date time){
        if(Objects.isNull(time)){
            return true;
        }
        if(System.currentTimeMillis()-time.getTime() > DURATION_5_MINUTES){
            return true;
        }
        return false;
    }

    /**
     * 设备详情中使用：工具，电池包是否过期（超12小时）
     * @param timeStamp
     * @return
     */
    public boolean isExpired12Hours(Long timeStamp){
        if(Objects.isNull(timeStamp)){
            return true;
        }
        if(System.currentTimeMillis()-timeStamp > this.REAL_TIME_DATA_EXPIRATION){
            return true;
        }
        return false;
    }

}
