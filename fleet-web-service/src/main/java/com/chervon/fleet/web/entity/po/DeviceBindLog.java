package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 租户绑、解绑设备记录表
 *
 * <AUTHOR>
 * @description
 * @since 2022-09-20 14:28:36
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_device_bind_log")
public class DeviceBindLog extends Model<DeviceBindLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 绑定时间
     */
    private Long bindTime;
    /**
     * 解绑时间
     */
    private Long unbindTime;
    /**
     * 绑定人
     */
    private Long bindOperator;
    /**
     * 解绑人
     */
    private Long unbindOperator;
    /**
     * 绑定状态 1 已绑定 2 已解绑
     */
    private Integer status;

}