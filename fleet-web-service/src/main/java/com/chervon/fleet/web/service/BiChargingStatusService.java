package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiChargingStatus;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingStatusDashboardVo;

import java.util.List;

/**
 * 充电状态统计表（看板顶端Charging Status的4个title图和Power Availability图）服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
public interface BiChargingStatusService extends IService<BiChargingStatus> {

    /**
     * 根据CompanyId获取PO
     * @param companyId ID
     * @return PO
     */
    BiChargingStatus getByCompanyId(Long companyId);

    /**
     * 查询充电状态看板
     *
     * @param companyId 公司ID
     * @return 查询结果
     */
    ChargingStatusDashboardVo chargingStatus(Long companyId);
}
