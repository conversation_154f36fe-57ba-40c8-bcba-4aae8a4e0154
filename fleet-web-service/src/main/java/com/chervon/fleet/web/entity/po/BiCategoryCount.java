package com.chervon.fleet.web.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 设备分类数量统计表（看板：Machine Category）(t_bi_category_count)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_category_count")
public class BiCategoryCount extends Model<BiCategoryCount> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 分类编号
     */
    private String categoryCode;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 当前分类数量
     */
    private Integer categoryCount;
    /**
     * 更新时间
     */
    private Date modifyTime;

}