package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.entity.po.BiMaintenance;
import com.chervon.fleet.web.entity.vo.dashboard.MaintenanceDashboardVo;
import com.chervon.fleet.web.mapper.BiMaintenanceMapper;
import com.chervon.fleet.web.service.BiMaintenanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备维保状态数量统计表(看板：Maintenance & Service)服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiMaintenanceServiceImpl extends ServiceImpl<BiMaintenanceMapper, BiMaintenance>
    implements BiMaintenanceService {

    @Override
    public List<MaintenanceDashboardVo> maintenance(Long companyId) {
        List<BiMaintenance> list = this.list(new LambdaQueryWrapper<BiMaintenance>()
            .eq(BiMaintenance::getCompanyId, companyId)
            .orderByAsc(BiMaintenance::getServiceType));
        if (CollectionUtils.isEmpty(list)) {
            log.debug("租户({})看板维保服务数据为空", companyId);
            return new ArrayList<>();
        }
        return ConvertUtil.convertList(list, MaintenanceDashboardVo.class);
    }
}