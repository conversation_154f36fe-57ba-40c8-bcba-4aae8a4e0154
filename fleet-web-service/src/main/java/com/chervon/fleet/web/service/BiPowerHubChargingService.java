package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.dashboard.ChargingSystemDetailDashboardQuery;
import com.chervon.fleet.web.entity.po.BiPowerHubCharging;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsChargerVo;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingSystemOverviewDashboardVo;

import java.util.List;

/**
 * power-hub充电状态统计表（看板：Charging System Overview图）服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
public interface BiPowerHubChargingService extends IService<BiPowerHubCharging> {

    /**
     * 根据companyId获取PO列表
     * @param companyId 租户ID
     * @return PO列表
     */
    List<BiPowerHubCharging> listByCompanyId(Long companyId);

    /**
     * 概览列表
     *
     * @param companyId 公司ID
     * @return 查询结果
     */
    List<ChargingSystemOverviewDashboardVo> getPowerHubChargingList(Long companyId);

    /**
     * 概览详情
     *
     * @param query 查询条件
     * @return 查询结果
     */
    ChargingSystemOverviewDashboardVo powerHubLeftDetail(ChargingSystemDetailDashboardQuery query);

    /**
     * PowerHub设备详情-statistics-充电器
     *
     * @param deviceId 设备id
     * @return 看板数据
     */
    EquipmentStatisticsChargerVo detailStatisticsCharger(String deviceId);
}
