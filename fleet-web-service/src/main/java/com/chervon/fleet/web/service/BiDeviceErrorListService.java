package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.dashboard.DeviceErrorListDashboardQuery;
import com.chervon.fleet.web.api.entity.vo.InventoryInfoVo;
import com.chervon.fleet.web.entity.dto.HubDeviceIdDto;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.vo.EquipmentVo;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingStatusDashboardVo;
import com.chervon.fleet.web.entity.vo.dashboard.DeviceErrorListDashboardVo;

import java.util.List;
import java.util.Map;

/**
 * 设备故障列表（看板：Error List）服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
public interface BiDeviceErrorListService extends IService<BiDeviceErrorList> {

    /**
     * 获取inventory错误列表
     *
     * @param companyId 租户ID
     * @return PO列表
     */
    List<BiDeviceErrorList> listInventoryErrorsByCompanyId(Long companyId);

    /**
     * 通用获取设备最后一条故障信息
     * @param companyId 租户
     * @param deviceId 设备id
     * @param hourExpire 过期小时时间（查询最近n个小时内的故障）
     * @return 故障列表
     */
    BiDeviceErrorList getLastDeviceError(Long companyId,String deviceId,Integer hourExpire);
    /**
     * 设备故障列表看板查询
     * deviceId非必传,如果deviceId为空则返回当前companyId下所有数据
     *
     * @param query companyId，deviceId
     * @return 查询结果
     */
    DeviceErrorListDashboardVo inventoryErrorList(DeviceErrorListDashboardQuery query);

    /**
     * 获取设备错误数量
     *
     * @param companyId 租户ID
     * @return 数量
     */
    Integer inventoryErrorCount(Long companyId);

    /**
     * 查询t_bi_power_hub_detail表，获取充电桩下电池，充电仓适配器的deviceId
     * @param listHubId 多个hub设备id
     * @return 所有充电桩, 电池, 充电仓适配器的deviceId列表
     */
    List<HubDeviceIdDto> getMoreHubLoadAllDeviceIds(Long companyId,List<String> listHubId);
    /**
     * 为电源看板充电overview页面查询最近错误信息
     *
     * @param companyId 公司ID
     * @param deviceId  设备ID,非必填
     * @return PO
     */
    BiDeviceErrorList getLatestErrorForPowerHub(Long companyId, String deviceId);

    /**
     * 批量获取最近错误信息
     * @param companyId 公司ID
     * @param hubDeviceIds 设备ID列表
     * @return Map<String, BiDeviceErrorList>
     */
    Map<String, BiDeviceErrorList> batchGetLatestErrorForPowerHub(Long companyId, List<String> hubDeviceIds);

    /**
     * 根据companyId获取错误数量以及最近一条错误的设备名称
     *
     * @param companyId 公司ID
     * @return ChargingStatusDashboardVo, 但是只赋值errors, latestErrorDevice两个字段
     */
    ChargingStatusDashboardVo getErrorCountAndLatestDeviceName(Long companyId,List<String> listDeiceId);

    /**
     * 根据companyId获取错误数量以及最近一条错误的设备名称
     *
     * @param companyId 公司ID
     * @return ChargingStatusDashboardVo, 但是只赋值errors, latestErrorDevice两个字段
     */
    int getChargerErrorCount(Long companyId,List<String> listDeiceId);

    /**
     * 设置错误列表
     * @param e 租户设备信息
     * @param companyId 租户
     * @param vo 返回对象
     */
    void setErrorList(CompanyDevice e, Long companyId, EquipmentVo vo);

    /**
     * 设置错误信息
     * @param biDeviceErrorList 错误列表
     * @param vo 返回对象
     * @param e 设备信息
     */
    void setErrorInfo(List<BiDeviceErrorList> biDeviceErrorList, EquipmentVo vo, CompanyDevice e);

    /**
     * 设置设备错误列表
     * @param deviceId 设备ID
     * @param companyDevice 设备信息
     * @param res 返回对象
     */
    void setDeviceErrorList(String deviceId, CompanyDevice companyDevice, InventoryInfoVo res);
}
