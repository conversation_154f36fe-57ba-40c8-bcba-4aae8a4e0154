package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 充电状态统计表（看板顶端Charging Status的4个title图和Power Availability图）(t_bi_charging_status)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_charging_status")
public class BiChargingStatus extends Model<BiChargingStatus> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 当前租户账号下的所有充电器上的各类电池都充满的所需时长: xxx Hxxx min
     */
    private String remainingChargingTime;
    /**
     * 当前租户账号下的所有充电器上的各类电池都充满的所需时长: (分钟)
     */
    private Integer remainingChargingTimestamp;
    /**
     * 当前租户账号下所有的充电器上的电池都充满的预计完成时间
     */
    private String estimatedCompletionTime;
    /**
     * 当前租户账号下所有的充电器上的电池都充满的预计完成时间
     */
    private Long estimatedCompletionTimestamp;
    /**
     * 租户下所有插在充电座上的已充满的电池的总数量
     */
    private Integer readyBatteryCount;
    /**
     * 租户账号下所有插在充电座上的已充满的电池的总标称电量
     */
    private BigDecimal readyBatteryAh;
    /**
     * 租户账号下所有插在充电座上的充电中和等待充电的电池的总数量
     */
    private Integer chargingBatteryCount;
    /**
     * 租户账号下所有插在充电座上的充电中和等待充电的电池已有的总电量
     */
    private BigDecimal chargingBatteryAh;
    /**
     * 充电状态：0保留  1 正常充电 2等待充电 3电池过温 4电池故障 6全部充电完成  取值从设备影子:2016
     * 租户下充电器汇总状态：
     * 全部故障：-4
     * 全部都充满：6
     * 存在故障就是故障：4
     * 没故障存在过温就是过温：3
     * 剩下所有情况都是：1 Normal
     */
    private Integer chargerState;
    /**
     * (仅CH700使用)充电进度百分比: 整数：0-100
     */
    private Integer chargingPercentage;
    /**
     * 更新时间
     */
    private Date modifyTime;

}