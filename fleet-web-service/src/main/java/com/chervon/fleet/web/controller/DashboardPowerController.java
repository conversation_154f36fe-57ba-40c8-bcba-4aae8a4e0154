package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import com.chervon.fleet.web.api.entity.query.dashboard.BatteryUsageDashboardQuery;
import com.chervon.fleet.web.api.entity.query.dashboard.ChargingSystemDetailDashboardQuery;
import com.chervon.fleet.web.api.entity.query.dashboard.EnergyUsageDashboardQuery;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.HubDeviceIdDto;
import com.chervon.fleet.web.entity.vo.FleetCategoryFilterVo;
import com.chervon.fleet.web.entity.vo.dashboard.*;
import com.chervon.fleet.web.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 电源看板相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@Api(tags = "充电器看板")
@RestController
@Slf4j
@RequestMapping("/dashboard/power")
public class DashboardPowerController {
    @Autowired
    private BiChargerCountService biChargerCountService;
    @Autowired
    private BiBatteryCountService biBatteryCountService;
    @Autowired
    private BiChargingStatusService biChargingStatusService;
    @Autowired
    private BiPowerHubChargingService biPowerHubChargingService;
    @Autowired
    private BiPowerHubDetailService biPowerHubDetailService;
    @Autowired
    private BiDailyBatteryUsageService biDailyBatteryUsageService;
    @Autowired
    private BiDailyEnergyChargedService biDailyEnergyChargedService;
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;
    @Autowired
    private CompanyDeviceService companyDeviceService;

    @ApiOperation(value = "充电器数量统计")
    @PostMapping(value = "/charger/count")
    @WebApiHeaders
    public R<List<ChargerCountDashboardVo>> chargerCount() {
        List<ChargerCountDashboardVo> result = biChargerCountService.chargerCount(UserContext.getCompanyId());
        return R.ok(result);
    }

    /**
     * web端 充电器上挂载的电池包数量统计
     * @return
     */
    @ApiOperation(value = "电池数量统计")
    @PostMapping(value = "/battery/count")
    @WebApiHeaders
    public R<List<BatteryCountDashboardVo>> batteriesCount() {
        List<BatteryCountDashboardVo> result = biBatteryCountService.batteryCount(UserContext.getCompanyId());
        return R.ok(result);
    }

    /**
     * power web端看板顶部充电状态栏（租户级别）
     * @return
     */
    @ApiOperation(value = "充电状态")
    @PostMapping(value = "/charging/status")
    @WebApiHeaders
    public R<ChargingStatusDashboardVo> chargingStatus() {
        Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        final int chargerCount = biChargerCountService.countChargerByCompanyId(companyId);
        if(chargerCount == 0){
            return R.ok(new ChargingStatusDashboardVo());
        }
        ChargingStatusDashboardVo result = biChargingStatusService.chargingStatus(companyId);
        result.setChargerCount(chargerCount);
        if(StringUtils.isEmpty(result.getRemainingChargingTime())){
            return R.ok(result);
        }
        final List<String> hubOnlineList = companyDeviceService.getHubOnlineList(companyId);
        final List<HubDeviceIdDto> hubLoadAllDeviceIds = biDeviceErrorListService.getMoreHubLoadAllDeviceIds(companyId, hubOnlineList);
        final List<String> allOnlineDeviceId = hubLoadAllDeviceIds.stream().flatMap(a -> a.getAllDevice().stream()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(allOnlineDeviceId)){
            return R.ok(result);
        }
        //故障统计：统计租户下t_bi_device_error_list列表最后一条及数量
        ChargingStatusDashboardVo errorInfo = biDeviceErrorListService.getErrorCountAndLatestDeviceName(companyId,allOnlineDeviceId);
        result.setErrors(errorInfo.getErrors());
        result.setLatestErrorDevice(errorInfo.getLatestErrorDevice());
        result.setChargerCount(chargerCount);
        return R.ok(result);
    }

    @ApiOperation(value = "充电系统概览列表：powerHub设备列表")
    @PostMapping(value = "/charging/system/overview")
    @WebApiHeaders
    public R<List<ChargingSystemOverviewDashboardVo>> powerHubChargingList() {
        Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED,StringConst.COMPANY_ID);
        List<ChargingSystemOverviewDashboardVo> result = biPowerHubChargingService.getPowerHubChargingList(companyId);
        return R.ok(result);
    }

    @ApiOperation(value = "充电系统概览详情:powerHub详情左侧详情")
    @PostMapping(value = "/charging/system/overview/get")
    @WebApiHeaders
    public R<ChargingSystemOverviewDashboardVo> powerHubChargingDetailLeft(@RequestBody ChargingSystemDetailDashboardQuery query) {
        query.setCompanyId(UserContext.getCompanyId());
        Assert.hasText(query.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        ChargingSystemOverviewDashboardVo result = biPowerHubChargingService.powerHubLeftDetail(query);
        return R.ok(result);
    }

    @ApiOperation(value = "充电系统详情：powerHub详情右侧拓扑图")
    @PostMapping(value = "/charging/system/detail")
    @WebApiHeaders
    public R<List<ChargingSystemDetailDashboardVo>> powerHubChargingDetailRight(@RequestBody ChargingSystemDetailDashboardQuery query) {
        query.setCompanyId(UserContext.getCompanyId());
        Assert.hasText(query.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        List<ChargingSystemDetailDashboardVo> result = biPowerHubDetailService.powerHubDetailRight(query);
        return R.ok(result);
    }

    @ApiOperation(value = "每日电池使用情况")
    @PostMapping(value = "/battery/usage")
    @WebApiHeaders
    public R<List<BatteryUsageDashboardVo>> batteryUsage(@RequestBody BatteryUsageDashboardQuery query) {
        query.setCompanyId(UserContext.getCompanyId());
        Assert.isId(query.getCompanyId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        Assert.notNull(query.getStartTime(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.START_TIME);
        Assert.notNull(query.getEndTime(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.END_TIME);
        if (!StringConst.YMD_REG.matcher(query.getStartTime()).matches()) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, query.getStartTime());
        }
        if (!StringConst.YMD_REG.matcher(query.getEndTime()).matches()) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, query.getEndTime());
        }
        List<BatteryUsageDashboardVo> result = biDailyBatteryUsageService.batteryUsage(query);
        return R.ok(result);
    }

    @ApiOperation(value = "battery下拉框")
    @GetMapping(value = "/all/battery")
    public List<FleetCategoryFilterVo> allBatteries() {
        List<FleetCategoryFilterVo> batteryCategoryList = biDailyBatteryUsageService.getBatteryTypeList();
        return batteryCategoryList;
    }

    @ApiOperation(value = "每日充电电量统计")
    @PostMapping(value = "/energy/charged")
    @WebApiHeaders
    public R<List<EnergyUsageDashboardVo>> energyCharged(@RequestBody EnergyUsageDashboardQuery query) {
        query.setCompanyId(UserContext.getCompanyId());
        Assert.notNull(query.getStartTime(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.START_TIME);
        Assert.notNull(query.getEndTime(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.END_TIME);
        if (!StringConst.YMD_REG.matcher(query.getStartTime()).matches()) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, query.getStartTime());
        }
        if (!StringConst.YMD_REG.matcher(query.getEndTime()).matches()) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, query.getEndTime());
        }
        List<EnergyUsageDashboardVo> result = biDailyEnergyChargedService.energyCharged(query);
        return R.ok(result);
    }
}
