package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12 15:17
 */
@Data
@ApiModel(description = "打标签操作对象")
public class TagSettingDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备id集合")
    private List<String> deviceIds;

    @ApiModelProperty("标签id")
    private Long tagId;

    @ApiModelProperty("操作类型 1 新增 2 删除")
    private Integer addOrRemove;
}
