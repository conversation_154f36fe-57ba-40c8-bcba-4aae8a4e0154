package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.dto.DeviceEditDto;
import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.fleet.web.api.entity.error.DeviceErrorCodeEnum;
import com.chervon.fleet.web.api.entity.query.GatewayQuery;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.EquipmentSearchDto;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.ShadowStatus;
import com.chervon.fleet.web.entity.vo.EquipmentSearchVo;
import com.chervon.fleet.web.entity.vo.EquipmentVo;
import com.chervon.fleet.web.entity.vo.MapGatewayVo;
import com.chervon.fleet.web.entity.vo.WebFilterConditionVo;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.utils.EquipmentSettingUtil;
import com.chervon.fleet.web.utils.GeoCoordinateUtil;
import com.chervon.iot.middle.api.service.RemoteIotDataService;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.RemoteDeviceManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备服务实现类
 * <AUTHOR>
 * &#064;date  2023/7/12 11:34
 */
@Service
@Slf4j
public class EquipmentServiceImpl implements EquipmentService {
    private static final String SQL_LIMIT = "limit 1";
    private static final String TIME_STAMP="ts";
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private RemoteFleetDeviceService remoteFleetDeviceService;
    @Autowired
    private WebFilterConditionService webFilterConditionService;
    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference
    private RemoteIotDataService remoteIotDataService;
    @Autowired
    private RuleEngineService ruleEngineService;
    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;
    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;
    @Autowired
    private AppGatewayService appGatewayService;
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;
    @Autowired
    private ShadowStatusService shadowStatusService;
    @Autowired
    private EquipmentUtil equipmentUtil;

    @Override
    public WebFilterConditionVo filterCondition(String search) {
        return webFilterConditionService.filterCondition(UserContext.getCompanyId(), true, true, true, true, true, search);
    }

    @Override
    public EquipmentSearchVo search(EquipmentSearchDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED,"companyId");
        EquipmentSearchVo res = new EquipmentSearchVo();
        // 查出租户下的所有设备
        List<CompanyDevice> list = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getIsDeleted, 0)
                .orderByDesc(CompanyDevice::getBindingTime));
        res.setTotalCount(list.size());
        //根据查询条件过滤
        equipmentUtil.deviceFilterByCondition(req, list, companyId);
        //查询设备缓存状态
        InventoryStatusQuery query = new InventoryStatusQuery();
        query.setCompanyId(companyId)
                .setListDeviceId(list.stream().map(CompanyDevice::getDeviceId)
                        .distinct().collect(Collectors.toList()));
        List<DeviceStatusVo> deviceStatus = ruleEngineService.getWarehouseStatusOnly(query);
        Map<String, DeviceStatusVo> deviceStatusMap = deviceStatus.stream().collect(Collectors.toMap(DeviceStatusVo::getDeviceId, Function.identity()));
        // 装配数据
        handleInventorySearch(res, list, deviceStatusMap, req);
        return res;
    }



    /**
     * 装配数据
     * @param res
     * @param list
     * @param deviceStatusMap
     * @param req
     */
    private void handleInventorySearch(EquipmentSearchVo res, List<CompanyDevice> list, Map<String, DeviceStatusVo> deviceStatusMap, EquipmentSearchDto req) {
        // 是否有过滤条件
        if (checkFilter(res, list, req)) {
            return;
        }
        // 获取产品数据
        List<ProductCache> products = remoteOperationCacheService.listProducts(list.stream().map(CompanyDevice::getProductId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productMap = products.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity(), (k1, k2) -> k2));

        // 获取fleet品类信息
        final Map<String, String> fleetCategoryMap = getFleetCategoryMap(list);

        List<String> deviceIds = list.stream().map(CompanyDevice::getDeviceId).filter(StringUtils::hasText).collect(Collectors.toList());
        List<ShadowStatus> shadowStatuses = new ArrayList<>();
        List<BiDeviceErrorList> deviceErrorList = new ArrayList<>();
        Long companyId = UserContext.getCompanyId();
        if (!deviceIds.isEmpty()) {
            shadowStatuses = shadowStatusService.listByDeviceIds(deviceIds,companyId);
            deviceErrorList = biDeviceErrorListService.list(new LambdaQueryWrapper<BiDeviceErrorList>()
                    .eq(BiDeviceErrorList::getCompanyId, companyId)
                    .in(BiDeviceErrorList::getDeviceId, deviceIds)
                    .orderByDesc(BiDeviceErrorList::getModifyTime));
        }
        Map<String, Integer> shadowStatusMap = shadowStatuses.stream().collect(HashMap::new, (map, e) -> map.put(e.getDeviceId(), e.getTotalUsageTime()), HashMap::putAll);
        Map<String, List<BiDeviceErrorList>> deviceErrorMap = deviceErrorList.stream().collect(Collectors.groupingBy(BiDeviceErrorList::getDeviceId, LinkedHashMap::new, Collectors.toList()));

        // 设备集合
        List<EquipmentVo> equipments = new ArrayList<>();
        for(CompanyDevice e : list){
            final EquipmentVo vo = getEquipmentVo(deviceStatusMap, e, productMap, deviceErrorMap, fleetCategoryMap, shadowStatusMap);
            equipments.add(vo);
        }
        res.setEquipments(equipments);

        List<EquipmentVo> listDeviceWithGateway = equipments.stream().filter(e -> e.getGatewayId() != null).collect(Collectors.toList());
        Map<String, AppGateway> gatewayAllMap;
        if (!listDeviceWithGateway.isEmpty()) {
            gatewayAllMap = buildAppGatewayMap(res, listDeviceWithGateway);
        } else {
            gatewayAllMap = new HashMap<>();
        }
        //设置网关名称
        for(EquipmentVo vo : res.getEquipments()){
            if(StringUtils.hasText(vo.getGatewayId()) && !StringUtils.hasText(vo.getGatewayName())){
                AppGateway gateway = gatewayAllMap.get(vo.getGatewayId());
                if(!Objects.isNull(gateway)) {
                    EquipmentSettingUtil.setGatewayLocation(vo, gateway);
                }
            }
        }
    }

    @NotNull
    private Map<String, AppGateway> buildAppGatewayMap(EquipmentSearchVo res, List<EquipmentVo> listDeviceWithGateway) {
        Map<String, AppGateway> gatewayAllMap;
        List<String> listGatewayId= listDeviceWithGateway.stream().map(EquipmentVo::getGatewayId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<AppGateway> gatewayList = appGatewayService.getList(new GatewayQuery().setListGatewayId(listGatewayId));
        gatewayAllMap = gatewayList.stream().collect(HashMap::new, (map, e) -> map.put(e.getId(), e), HashMap::putAll);

        List<MapGatewayVo> mapGatewayVos = new ArrayList<>();
        List<Map<String, BigDecimal>> points = new ArrayList<>();

        final List<MapGatewayVo> listMapGatewayVo = listDeviceWithGateway.stream().map(e -> {
            return EquipmentSettingUtil.getMapGatewayVo(e, gatewayAllMap);
        }).filter(e -> e.getGatewayId() != null).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(listMapGatewayVo)) {
            listMapGatewayVo.stream().collect(Collectors.groupingBy(MapGatewayVo::getGatewayId)).forEach((k, v) -> {
                MapGatewayVo vo = new MapGatewayVo();
                BeanUtils.copyProperties(v.get(0), vo);
                //统计网关下设备数量
                vo.setDeviceNumber(v.size() == 0 ? 0 : (int) v.stream().filter(a -> !Objects.isNull(a.getGatewayId()) && !a.getGatewayId().equals(a.getDeviceId())).count());
                //设置中心坐标
                EquipmentSettingUtil.setCenterCoordinate(v, points);
                //添加结果集
                mapGatewayVos.add(vo);
            });
        }
        res.setMapGateways(mapGatewayVos);
        Map<String, BigDecimal> centerPoint = GeoCoordinateUtil.getCenterPoint(points);
        res.setCenterLatitude(centerPoint.get("lat"));
        res.setCenterLongitude(centerPoint.get("lon"));
        return gatewayAllMap;
    }

    @NotNull
    private Map<String, String> getFleetCategoryMap(List<CompanyDevice> list) {
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCodes(list.stream().map(CompanyDevice::getSecondCategoryCode).collect(Collectors.toList()));
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        Map<String, String> fleetCategoryMap = fleetCategoryList.stream().collect(HashMap::new, (map, e) -> map.put(e.getCode(), e.getCategoryName()), HashMap::putAll);
        return fleetCategoryMap;
    }

    private static boolean checkFilter(EquipmentSearchVo res, List<CompanyDevice> list, EquipmentSearchDto req) {
        if (EquipmentSettingUtil.isFilter(req)) {
            res.setIsFilter(1);
            res.setSearchCount(list.size());
        } else {
            res.setIsFilter(0);
        }
        if (list.isEmpty()) {
            return true;
        }
        return false;
    }

    @NotNull
    private EquipmentVo getEquipmentVo(Map<String, DeviceStatusVo> deviceStatusMap, CompanyDevice e, Map<Long, ProductCache> productMap, Map<String, List<BiDeviceErrorList>> deviceErrorMap, Map<String, String> fleetCategoryMap, Map<String, Integer> shadowStatusMap) {
        EquipmentVo vo = new EquipmentVo();
        vo.setDeviceId(e.getDeviceId());
        vo.setDeviceSn(e.getDeviceSn());
        vo.setDeviceName(e.getDeviceName());
        vo.setCategoryCode(e.getSecondCategoryCode());
        vo.setProductId(e.getProductId());
        //自身是硬网关
        if(e.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode())){
            vo.setGatewayId(e.getDeviceId());
            vo.setHardGateway(true);
        }else{
            vo.setGatewayId(e.getGatewayId());
        }
        //设置网关坐标
        EquipmentSettingUtil.setCoordinate(deviceStatusMap, e, vo);
        // 设置产品信息
        equipmentUtil.setProductInfo(e, productMap, vo);
        // 库存状态
        vo.setInventoryStatus(e.getWarehouseStatus());
        // 在线状态
        vo.setOnlineStatus(e.getOnlineStatus());
        // 库存状态
        vo.setMaintenanceStatus(e.getMaintenanceStatus());
        // 设置错误信息
        biDeviceErrorListService.setErrorInfo(deviceErrorMap.get(e.getDeviceId()), vo, e);
        // 获取fleet品类信息
        vo.setCategoryName(fleetCategoryMap.get(e.getSecondCategoryCode()));
        // 设置详情类型
        vo.setDetailType(e.getCustomType());
        // 设置使用时长
        vo.setUsageDuration(shadowStatusMap.get(e.getDeviceId()));
        return vo;
    }

    /**
     * 设备详情
     * @param deviceId 设备id
     * @return 设备详情
     */
    @Override
    public EquipmentVo detail(String deviceId) {
        EquipmentVo equipmentVo = new EquipmentVo();
        Long companyId = UserContext.getCompanyId();
        // 设置库存状态信息
        equipmentUtil.setInventoryStatus(deviceId, companyId, equipmentVo);
        // 获取租户设备基础信息
        CompanyDevice companyDevice = getCompanyDeviceInfo(deviceId, companyId);
        // 设置产品信息
        equipmentUtil.setProductInfo(companyDevice, equipmentVo);
        // 库存状态
        equipmentVo.setInventoryStatus(companyDevice.getWarehouseStatus());
        //设置租户设备表原始信息
        equipmentUtil.setCompanyDeviceInfo(equipmentVo, companyDevice);
        // 查询BiDeviceErrorList设置最新故障码信息
        biDeviceErrorListService.setErrorList(companyDevice, companyId, equipmentVo);
        // 获取fleet品类信息
        equipmentUtil.setFleetCategoryInfo(companyDevice, equipmentVo);
        // 设备最后一次上报数据时间
        try {
            Map<String,Object> mapShadowData=remoteIotDataService.getLatestColumnLog(companyDevice.getProductId().toString(),companyDevice.getDeviceId(),"ts",null);
            if(!Objects.isNull(mapShadowData) && mapShadowData.get(TIME_STAMP)!=null){
                final Timestamp ts1 = (Timestamp) mapShadowData.get(TIME_STAMP);
                if(companyDevice.getBindingTime() > ts1.getTime()){
                    equipmentVo.setLastSyncedOn(null);
                }else{
                    equipmentVo.setLastSyncedOn(ts1.getTime());
                }
            }
        }catch (NullPointerException ignored) {
            log.error("获取时序数据库设备最新上报时间发生异常：{}",ignored);
        }catch (Exception e) {
            log.error("获取时序数据库设备最新上报时间发生异常：{}",e);
        }

        //设置设备影子实时状态信息
        shadowStatusService.setShadowStatusValue(companyDevice, equipmentVo);
        return equipmentVo;
    }

    private CompanyDevice getCompanyDeviceInfo(String deviceId, Long companyId) {
        List<CompanyDevice> companyDevices = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getDeviceId, deviceId)
                .orderByDesc(CompanyDevice::getModifyTime)
                .last(SQL_LIMIT));
        return companyDevices.isEmpty() ? new CompanyDevice() : companyDevices.get(0);
    }

    /**
     * 编辑名称
     * @param req 操作对象
     */
    @Override
    public void editName(DeviceEditDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "equipmentEditNameDto");
        Assert.hasText(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        Assert.hasText(req.getNewDeviceName(), ErrorCode.PARAMETER_NOT_PROVIDED, "newDeviceName");
        CompanyDevice companyDevice = companyDeviceService.getOne(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getDeviceId, req.getDeviceId())
                .eq(CompanyDevice::getCompanyId, companyId));
        if (companyDevice == null) {
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_BOUND_NOT_FOUND);
        }
        long count = companyDeviceService.count(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getDeviceName, req.getNewDeviceName())
                .ne(CompanyDevice::getDeviceId, req.getDeviceId()));
        if (count > 0) {
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_NAME_EXISTED);
        }
        CompanyDevice cd = new CompanyDevice();
        cd.setId(companyDevice.getId());
        cd.setDeviceName(req.getNewDeviceName());
        companyDeviceService.updateById(cd);
        // Async 回调更新device表昵称
        remoteDeviceManageService.callBackAfterEditDevice(req.getNewDeviceName(), req.getDeviceId(), companyId);
        // 修改缓存设备信息
        ruleEngineService.updateDeviceInfo(companyDevice);
    }

    /**
     * 删除设备
     * @param deviceId 设备id
     */
    @Override
    public void delete(String deviceId) {
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        remoteFleetDeviceService.unbind(deviceId);
    }
}
