package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.error.GroupTagErrorCodeEnum;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.TagDto;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.entity.po.Group;
import com.chervon.fleet.web.entity.po.Tag;
import com.chervon.fleet.web.mapper.TagMapper;
import com.chervon.fleet.web.service.DeviceTagService;
import com.chervon.fleet.web.service.GroupService;
import com.chervon.fleet.web.service.TagService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * &#064;date  2023/7/11 15:28
 */
@Service
@Slf4j
@AllArgsConstructor
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    @Lazy
    private final GroupService groupService;

    @Lazy
    private final DeviceTagService deviceTagService;

    @Override
    public List<TagVo> listByGroupId(Long groupId) {
        Assert.notNull(groupId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GROUP_ID);
        List<Tag> list = this.list(new LambdaQueryWrapper<Tag>().eq(Tag::getGroupId, groupId).orderByDesc(Tag::getCreateTime));
        return list.stream().map(e -> {
            TagVo vo = new TagVo();
            vo.setGroupId(e.getGroupId()).setTagId(e.getId()).setTagName(e.getName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void add(TagDto req) {
        check(req, true);
        Long companyId = UserContext.getCompanyId();
        long count = this.count(new LambdaQueryWrapper<Tag>().eq(Tag::getCompanyId, companyId).eq(Tag::getGroupId, req.getGroupId()));
        if (count >= TAG_MAX) {
            throw new ServiceException(GroupTagErrorCodeEnum.TAG_EXCEEDS_100);
        }
        Tag tag = new Tag();
        tag.setCompanyId(companyId).setName(req.getTagName()).setGroupId(req.getGroupId());
        this.save(tag);
    }

    private void check(TagDto req, boolean create) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "tagDto");
        if (!create) {
            // 编辑模式，标签id必填
            Assert.notNull(req.getTagId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.TAG_ID);
        }
        Assert.hasText(req.getTagName(), ErrorCode.PARAMETER_NOT_PROVIDED, "tagName");
        Assert.notNull(req.getGroupId(), ErrorCode.PARAMETER_NOT_PROVIDED, "groupId");
        LambdaQueryWrapper<Tag> wrapper = new LambdaQueryWrapper<Tag>()
                .eq(Tag::getName, req.getTagName())
                .eq(Tag::getCompanyId, UserContext.getCompanyId());
        if (!create) {
            wrapper.ne(Tag::getId, req.getTagId());
        }
        Tag exist = this.getOne(wrapper);
        if (exist != null) {
            // 存在名称相同的分组
            throw new ServiceException(GroupTagErrorCodeEnum.TAG_NAME_EXISTED);
        }
        Group group = groupService.getById(req.getGroupId());
        if (group == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_NOT_EXIST);
        }
    }

    @Override
    public TagVo detail(Long tagId) {
        Assert.notNull(tagId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.TAG_ID);
        Tag tag = this.getById(tagId);
        if (tag == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.TAG_NOT_EXIST);
        }
        Group group = groupService.getById(tag.getGroupId());
        if (group == null) {
            group = new Group();
        }
        TagVo res = new TagVo();
        res.setGroupId(group.getId()).setGroupName(group.getName()).setTagId(tag.getId()).setTagName(tag.getName());
        return res;
    }

    @Override
    public void edit(TagDto req) {
        check(req, false);
        Tag tag = this.getById(req.getTagId());
        if (tag == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.TAG_NOT_EXIST);
        }
        Tag newTag = new Tag();
        newTag.setId(req.getTagId()).setName(req.getTagName()).setGroupId(req.getGroupId());
        this.updateById(newTag);
        // 编辑设备标签中的标签分组
        if (!tag.getGroupId().equals(req.getGroupId())) {
            deviceTagService.update(new DeviceTag(), new LambdaUpdateWrapper<DeviceTag>()
                    .eq(DeviceTag::getTagId, req.getTagId()).set(DeviceTag::getGroupId, req.getGroupId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long tagId) {
        Assert.notNull(tagId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.TAG_ID);
        Tag tag = this.getById(tagId);
        if (tag == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.TAG_NOT_EXIST);
        }
        this.removeById(tagId);
        // 删除设备绑定标签记录
        deviceTagService.remove(new LambdaQueryWrapper<DeviceTag>().eq(DeviceTag::getTagId, tagId));
    }

    @Override
    public Map<String, List<TagVo>> getTagVoMap(Long companyId, List<String> deviceIds) {
        Map<String, List<TagVo>> target = new ConcurrentHashMap<>(CommonConstant.SIXTEEN);
        if(CollectionUtils.isEmpty(deviceIds)){
            return target;
        }
        // 1.查关联表
        List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                .eq(DeviceTag::getCompanyId, companyId)
                .in(DeviceTag::getDeviceId, deviceIds)
                .orderByDesc(DeviceTag::getCreateTime));
        if (CollectionUtils.isEmpty(deviceTags)) {
            log.warn("getTagVoMap -> 设备标签列表为空,companyId: {}", companyId);
            return target;
        }
        Set<String> deviceIdSet = deviceTags.stream().map(DeviceTag::getDeviceId).collect(Collectors.toSet());
        Map<String, List<DeviceTag>> tagGroupByDeviceIdMap = deviceTags.stream().collect(Collectors.groupingBy(DeviceTag::getDeviceId));
        // 2.查tag表获取tag_name字段
        List<Tag> tagByCompanyList = this.list(new LambdaQueryWrapper<Tag>()
                .in(Tag::getId, deviceTags.stream().map(DeviceTag::getTagId).distinct().collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(tagByCompanyList)) {
            log.warn("getTagVoMap -> tagByCompanyList列表为空,companyId: {}", companyId);
            return target;
        }
        Map<Long, String> tagNameMap = tagByCompanyList.stream().distinct().collect(Collectors.toMap(Tag::getId, Tag::getName, (key1, key2) -> key2));
        // 3.拼装target
        for (String deviceId : deviceIdSet) {
            List<TagVo> tags = new ArrayList<>();
            List<DeviceTag> deviceTagList = tagGroupByDeviceIdMap.getOrDefault(deviceId, new ArrayList<>());
            deviceTagList.forEach(deviceTag -> {
                TagVo tagVo = new TagVo();
                tagVo.setTagId(deviceTag.getTagId());
                tagVo.setGroupId(deviceTag.getGroupId());
                tagVo.setTagName(tagNameMap.get(deviceTag.getTagId()));
                tags.add(tagVo);
            });
            target.put(deviceId, tags);
        }
        return target;
    }
}
