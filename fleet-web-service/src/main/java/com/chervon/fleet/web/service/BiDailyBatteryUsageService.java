package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.dashboard.BatteryUsageDashboardQuery;
import com.chervon.fleet.web.entity.po.BiDailyBatteryUsage;
import com.chervon.fleet.web.entity.vo.FleetCategoryFilterVo;
import com.chervon.fleet.web.entity.vo.dashboard.BatteryUsageDashboardVo;

import java.util.List;

/**
 * 电池使用量统计表(看板：Battery Usage图)服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
public interface BiDailyBatteryUsageService extends IService<BiDailyBatteryUsage> {

    /**
     * 获取电池类型列表
     * @return 电池包列表类型
     */
    List<FleetCategoryFilterVo> getBatteryTypeList();
    /**
     * 电池使用情况看板查询
     *
     * @param query 查询条件
     * @return 查询结果
     */
    List<BatteryUsageDashboardVo> batteryUsage(BatteryUsageDashboardQuery query);
}
