package com.chervon.fleet.web.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**

 * 访问名单配置

 *
 * <AUTHOR>
 * 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "RequestList查询对象", description = "访问名单配置")
public class RequestListQuery extends PageQuery<RequestListQuery> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 访问路径
     */
    @ApiModelProperty(value = "访问路径")
    private String path;
    /**
     * 匹配方法
     */
    @ApiModelProperty(value = "匹配方法")
    private String matchMethod;
    /**
     * 来源ip地址
     */
    @ApiModelProperty(value = "来源ip地址")
    private String sourceIp;
    /**
     * mac地址
     */
    @ApiModelProperty(value = "mac地址")
    private String mac;
    /**
     * 限制类型（1.白名单，2正常，3黑名单）
     */
    @ApiModelProperty(value = "限制类型（1.白名单，2正常，3黑名单）")
    private Integer type;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
}
