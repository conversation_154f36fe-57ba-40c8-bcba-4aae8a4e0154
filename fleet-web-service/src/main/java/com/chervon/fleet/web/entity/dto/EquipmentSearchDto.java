package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12 13:58
 */
@Data
@ApiModel(description = "设备查询条件对象")
public class EquipmentSearchDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备名称，支持模糊搜索")
    private String deviceName;

    @ApiModelProperty("选中的二级品类id集合，如果所属一级品类id被选中，则传一级下的所有二级品类id")
    private List<String> categoryCodes;

    @ApiModelProperty("选中的库存状态集合")
    private List<Integer> inventoryStatus;

    @ApiModelProperty("选中的在线状态集合")
    private List<Integer> onlineStatus;

    @ApiModelProperty("选中的维保状态集合")
    private List<Integer> maintenanceStatus;

    @ApiModelProperty("选中的标签id集合")
    private List<Long> tagIds;

    @ApiModelProperty("网关id")
    private String gatewayId;

}
