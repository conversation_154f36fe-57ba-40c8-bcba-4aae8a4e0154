package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.web.enums.LanguageEnum;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.BizCategoryTypeEnum;
import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusEnum;
import com.chervon.fleet.web.api.entity.query.dashboard.DeviceErrorListDashboardQuery;
import com.chervon.fleet.web.api.entity.vo.InventoryInfoVo;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.HubDeviceIdDto;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.BiPowerHubDetail;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.ProductFault;
import com.chervon.fleet.web.entity.vo.EquipmentVo;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingStatusDashboardVo;
import com.chervon.fleet.web.entity.vo.dashboard.DeviceErrorListDashboardVo;
import com.chervon.fleet.web.mapper.BiDeviceErrorListMapper;
import com.chervon.fleet.web.service.BiDeviceErrorListService;
import com.chervon.fleet.web.service.BiPowerHubDetailService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.ProductFaultService;
import com.chervon.fleet.web.service.translate.TranslateUtils;
import com.chervon.fleet.web.utils.DataUtils;
import com.chervon.fleet.web.utils.DateUtil;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 设备故障列表（看板：Error List）服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiDeviceErrorListServiceImpl extends ServiceImpl<BiDeviceErrorListMapper, BiDeviceErrorList>
    implements BiDeviceErrorListService {
    @Autowired
    private BiPowerHubDetailService biPowerHubDetailService;
    @Autowired
    private TranslateUtils translateUtils;

    @Override
    public List<BiDeviceErrorList> listInventoryErrorsByCompanyId(Long companyId) {
        final Date hoursBefore = DateUtil.getHoursBefore(FleetDeviceConfig.HOUR12);
        List<BiDeviceErrorList> biDeviceErrorList = this.list(new LambdaQueryWrapper<BiDeviceErrorList>()
            .eq(BiDeviceErrorList::getCompanyId, companyId)
            .gt(BiDeviceErrorList::getModifyTime,hoursBefore)
            .ne(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.CHARGER.getCategoryCode())
            .ne(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.BATTERY.getCategoryCode())
            .orderByDesc(BiDeviceErrorList::getModifyTime));
        if (CollectionUtils.isEmpty(biDeviceErrorList)) {
            return new ArrayList<>();
        }
        translateUtils.translateListForeach(biDeviceErrorList);
        return biDeviceErrorList;
    }
    /**
     * 通用获取设备最后一条故障
     * @param companyId 租户
     * @param deviceId 设备列表
     * @param hourExpire 过期小时时间（查询最近n个小时内的故障）
     * @return 故障列表
     */
    @Override
    public BiDeviceErrorList getLastDeviceError(Long companyId,String deviceId,Integer hourExpire){
        LambdaQueryWrapper<BiDeviceErrorList> query= new LambdaQueryWrapper<BiDeviceErrorList>()
                .eq(BiDeviceErrorList::getCompanyId, companyId)
                .eq(BiDeviceErrorList::getDeviceId,deviceId)
                .orderByDesc(BiDeviceErrorList::getModifyTime).last(" limit 1");
        if(!Objects.isNull(hourExpire)){
            final Date hoursBefore = DateUtil.getHoursBefore(hourExpire);
            query.gt(BiDeviceErrorList::getModifyTime,hoursBefore);
        }
        BiDeviceErrorList biDeviceErrorList = this.getOne(query);
        if (Objects.isNull(biDeviceErrorList)) {
            return null;
        }
        translateUtils.translateListForeach(Arrays.asList(biDeviceErrorList));
        return biDeviceErrorList;
    }

    @Override
    public DeviceErrorListDashboardVo inventoryErrorList(DeviceErrorListDashboardQuery query) {
        LambdaQueryWrapper<BiDeviceErrorList> queryWrapper = new LambdaQueryWrapper<BiDeviceErrorList>()
            .eq(BiDeviceErrorList::getCompanyId, query.getCompanyId()) //租户必填
            .eq(org.springframework.util.StringUtils.hasText(query.getDeviceId()), BiDeviceErrorList::getDeviceId, query.getDeviceId());
        if (!StringUtils.isEmpty(query.getFirstCategoryCode())) {
            queryWrapper.eq(BiDeviceErrorList::getDeviceCategory, query.getFirstCategoryCode());
        } else if (query.isQueryToolCategory()) {
            queryWrapper.ne(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.CHARGER.getCategoryCode())
                .ne(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.BATTERY.getCategoryCode());
            //设置过期时间：工具类12小时后的故障有效
            final Date hoursBefore = DateUtil.getHoursBefore(FleetDeviceConfig.HOUR12);
            queryWrapper.gt(BiDeviceErrorList::getModifyTime,hoursBefore);
        }else{
            //nothing to do
        }
        queryWrapper.orderByDesc(BiDeviceErrorList::getModifyTime);
        List<BiDeviceErrorList> biDeviceErrorLists = this.list(queryWrapper);
        translateUtils.translateListForeach(biDeviceErrorLists);
        List<DeviceErrorListDashboardVo.DeviceError> deviceErrors =
            ConvertUtil.convertList(biDeviceErrorLists, DeviceErrorListDashboardVo.DeviceError.class);
        DeviceErrorListDashboardVo result = new DeviceErrorListDashboardVo();
        result.setCount(deviceErrors.size());
        result.setDeviceErrorList(deviceErrors);
        return result;
    }

    @Override
    public Integer inventoryErrorCount(Long companyId) {
        //设置过期时间：工具类12小时后的故障有效
        final Date hoursBefore = DateUtil.getHoursBefore(FleetDeviceConfig.HOUR12);
        return (int) this.count(new LambdaQueryWrapper<BiDeviceErrorList>()
            .eq(BiDeviceErrorList::getCompanyId, companyId)
            .gt(BiDeviceErrorList::getModifyTime,hoursBefore)
            .ne(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.CHARGER.getCategoryCode())
            .ne(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.BATTERY.getCategoryCode()));
    }

    /**
     * 查询t_bi_power_hub_detail表，获取充电桩下电池，充电仓适配器的deviceId
     * @param hubDeviceId deviceId
     * @return 所有充电桩, 电池, 充电仓适配器的deviceId列表
     */
    public HubDeviceIdDto getHubLoadAllDeviceIds(Long companyId, String hubDeviceId) {
        HubDeviceIdDto hubDeviceIdDto = new HubDeviceIdDto().setHubDeviceId(hubDeviceId);
        hubDeviceIdDto.getListChargerId().add(hubDeviceId);

        if(StringUtils.isEmpty(hubDeviceId)){
            return hubDeviceIdDto;
        }
        List<BiPowerHubDetail> biPowerHubDetails = biPowerHubDetailService.list(new LambdaQueryWrapper<BiPowerHubDetail>()
                .eq(BiPowerHubDetail::getHubDeviceId, hubDeviceId)
                .eq(BiPowerHubDetail::getCompanyId, companyId)
                .select(BiPowerHubDetail::getAdaptorName,BiPowerHubDetail::getBatteryDeviceId));
        final List<String> listAdaptor = biPowerHubDetails.stream()
                .map(BiPowerHubDetail::getAdaptorName)
                .distinct().collect(Collectors.toList());
        hubDeviceIdDto.getListChargerId().addAll(listAdaptor);

        hubDeviceIdDto.setListBatteryId(biPowerHubDetails.stream()
                .filter(a->!StringUtils.isEmpty(a.getBatteryDeviceId()))
                .map(BiPowerHubDetail::getBatteryDeviceId).distinct()
                .collect(Collectors.toList()));
        return hubDeviceIdDto;
    }

    /**
     * 批量获取hub下挂载的所有设备
     * @param companyId
     * @param listHubId 多个hub设备id
     * @return
     */
    public List<HubDeviceIdDto> getMoreHubLoadAllDeviceIds(Long companyId,List<String> listHubId) {
        if(CollectionUtils.isEmpty(listHubId)){
            return new ArrayList<>();
        }
        List<HubDeviceIdDto> list=new ArrayList<>();
        for(String hubId:listHubId){
            final HubDeviceIdDto hubDeviceIds = getHubLoadAllDeviceIds(companyId, hubId);
            list.add(hubDeviceIds);
        }
        return list;
    }

    @Override
    public BiDeviceErrorList getLatestErrorForPowerHub(Long companyId, String deviceId) {
        HubDeviceIdDto hubDeviceIdDto = getHubLoadAllDeviceIds(companyId,deviceId);
        return this.getOne(new LambdaQueryWrapper<BiDeviceErrorList>()
//            .eq(BiDeviceErrorList::getCompanyId, companyId)
            // 设备ID可以传空
            .in(BiDeviceErrorList::getDeviceId, hubDeviceIdDto.getMountDevice())
            // 设备分类等于CHARGER或BATTERY
            .and(wp -> wp.eq(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.CHARGER.getCategoryCode())
                .or().eq(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.BATTERY.getCategoryCode()))
            // CHARGER优先级高于BATTERY
            .orderByDesc(BiDeviceErrorList::getDeviceCategory)
            .orderByDesc(BiDeviceErrorList::getModifyTime)
            .last(StringConst.SQL_LIMIT));
    }

    @Override
    public Map<String, BiDeviceErrorList> batchGetLatestErrorForPowerHub(Long companyId, List<String> hubDeviceIds) {
        if(CollectionUtils.isEmpty(hubDeviceIds)){
            return new HashMap<>();
        }
        //获取powerHub挂载的适配器、电池包所有设备对象列表
        List<HubDeviceIdDto> listHubDeviceDto = getMoreHubLoadAllDeviceIds(companyId,hubDeviceIds);
        Map<String, BiDeviceErrorList> map = new ConcurrentHashMap<>();
        if(CollectionUtils.isEmpty(listHubDeviceDto)){
            return map;
        }
        //powerHub、适配器、电池包所有设备对象列表
        List<String> allDeviceIds = listHubDeviceDto.stream().map(HubDeviceIdDto::getMountDevice).flatMap(List::stream).collect(Collectors.toList());
        List<BiDeviceErrorList> listError= this.list(new LambdaQueryWrapper<BiDeviceErrorList>()
                // 设备ID可以传空
                .in(BiDeviceErrorList::getDeviceId, allDeviceIds)
                // 过滤设备分类：设备分类等于CHARGER或BATTERY
                .and(wp -> wp.eq(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.CHARGER.getCategoryCode())
                        .or().eq(BiDeviceErrorList::getDeviceCategory, FleetFirstCategoryEnum.BATTERY.getCategoryCode())));
        if(CollectionUtils.isEmpty(listError)){
            return map;
        }
        translateUtils.translateListForeach(listError);
        for(HubDeviceIdDto hubDeviceIdDto:listHubDeviceDto){
            final Optional<BiDeviceErrorList> adaptorList = listError.stream().filter(a -> hubDeviceIdDto.getListChargerId().contains(a.getDeviceId()))
                    .sorted(Comparator.comparing(BiDeviceErrorList::getModifyTime).reversed()).findFirst();
            if(adaptorList.isPresent()){
                map.put(hubDeviceIdDto.getHubDeviceId(),adaptorList.get());
                continue;
            }
            final Optional<BiDeviceErrorList> batteryList = listError.stream().filter(a -> hubDeviceIdDto.getListBatteryId().contains(a.getDeviceId()))
                    .sorted(Comparator.comparing(BiDeviceErrorList::getModifyTime).reversed()).findFirst();
            if(batteryList.isPresent()){
                map.put(hubDeviceIdDto.getHubDeviceId(),batteryList.get());
            }
        }
        return map;
    }

    @Override
    public ChargingStatusDashboardVo getErrorCountAndLatestDeviceName(Long companyId,List<String> listDeiceId) {
        ChargingStatusDashboardVo vo=new ChargingStatusDashboardVo();
        final Integer countErrorNumber = getChargerErrorCount(companyId,listDeiceId);
        if(countErrorNumber==0){
            vo.setLatestErrorDevice("");
            vo.setErrors(countErrorNumber);
            return vo;
        }
        String latestErrorDevice = this.getBaseMapper().selectLatestErrorDevice(companyId,listDeiceId);
        vo.setLatestErrorDevice(latestErrorDevice);
        vo.setErrors(countErrorNumber);
        return vo;
    }
    @Override
    public int getChargerErrorCount(Long companyId,List<String> listDeiceId){
        final Integer countErrorNumber = this.getBaseMapper().countErrorNumber(companyId,listDeiceId);
        return countErrorNumber.intValue();
    }

    /**
     * 设置设备错误信息
     * @param e
     * @param companyId
     * @param vo
     */
    @Override
    public void setErrorList(CompanyDevice e, Long companyId, EquipmentVo vo) {
        final List<BiDeviceErrorList> biDeviceErrorList = list(new LambdaQueryWrapper<BiDeviceErrorList>()
                .eq(BiDeviceErrorList::getDeviceId, e.getDeviceId())
                .eq(BiDeviceErrorList::getCompanyId, companyId)
                .orderByDesc(BiDeviceErrorList::getModifyTime)
                .last(StringConst.SQL_LIMIT));
        if (CollectionUtils.isEmpty(biDeviceErrorList)) {
            return;
        }
        setErrorInfo(biDeviceErrorList, vo,e);
    }

    /**
     * 设置错误信息
     * @param biDeviceErrorList
     * @param vo
     * @param e
     */
    public void setErrorInfo(List<BiDeviceErrorList> biDeviceErrorList, EquipmentVo vo, CompanyDevice e) {
        vo.setErrorStatus(0);
        if(CollectionUtils.isEmpty(biDeviceErrorList)){
            return;
        }
        translateUtils.translateListForeach(biDeviceErrorList);
        //验证设备是否过期
        if (e.getCustomType().intValue() == BizCategoryTypeEnum.CHARGER.getType()
                && e.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode())) {
            if(Objects.isNull(e.getOnlineStatus()) || e.getOnlineStatus().equals(OnlineStatusEnum.OFFLINE.getType())){
                return;
            }
        } else {
            boolean isExpired12Hours = FleetDeviceConfig.isExpired12Hours(biDeviceErrorList.get(0).getModifyTime());
            if (isExpired12Hours) {
                return;
            }
        }
        List<BiDeviceErrorList> errorLists = biDeviceErrorList;
        if (!CollectionUtils.isEmpty(errorLists)) {
            BiDeviceErrorList biDeviceError = errorLists.get(0);
            vo.setErrorStatus(1);
            vo.setErrorCode(biDeviceError.getErrorCode());
            vo.setErrorMessage(biDeviceError.getErrorMessage());
            vo.setSuggestionContent(biDeviceError.getSuggestionContent());
        }
    }

    public void setDeviceErrorList(String deviceId, CompanyDevice companyDevice, InventoryInfoVo res) {
        if(companyDevice.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode()) &&
                DataUtils.isOffline(companyDevice)) {
            res.setErrorStatus(0);
            return;
        }
        final LambdaQueryWrapper<BiDeviceErrorList> queryWrapper = new LambdaQueryWrapper<BiDeviceErrorList>()
                .eq(BiDeviceErrorList::getDeviceId, deviceId);
        final Date hoursBefore = DateUtil.getHoursBefore(FleetDeviceConfig.HOUR12);
        queryWrapper.gt(BiDeviceErrorList::getModifyTime,hoursBefore);
        queryWrapper.last("limit 1");
        List<BiDeviceErrorList> errorLists = list(queryWrapper);
        if (!CollectionUtils.isEmpty(errorLists)) {
            //必须使用foreach方法
            translateUtils.translateListForeach(errorLists);
            BiDeviceErrorList biDeviceErrorList = errorLists.get(0);
            res.setErrorStatus(1);
            res.setErrorCode(biDeviceErrorList.getErrorCode());
            res.setErrorMessage(biDeviceErrorList.getErrorMessage());
            res.setSuggestionContent(biDeviceErrorList.getSuggestionContent());
        } else {
            res.setErrorStatus(0);
        }
    }
}