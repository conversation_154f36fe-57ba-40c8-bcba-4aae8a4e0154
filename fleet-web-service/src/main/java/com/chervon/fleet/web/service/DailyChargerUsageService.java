package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.po.DailyChargerUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsChargerUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetChargerDailyUsageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-31 09:54
 **/
public interface DailyChargerUsageService extends IService<DailyChargerUsage> {
    /**
     * 详情-statistics-充电器-Usage Statistics
     *
     * @param req 请求对象
     * @return 看板数据
     */
    List<EquipmentStatisticsChargerUsageVo> detailStatisticsChargerUsage(StatisticsQueryDto req);

    /**
     * 日统计数据入库
     * @param listChargerDailyUsageVo
     */
    void saveDataDailyChargerUsage(List<FleetChargerDailyUsageVo> listChargerDailyUsageVo);
}
