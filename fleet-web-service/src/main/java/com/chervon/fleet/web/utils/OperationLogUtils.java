package com.chervon.fleet.web.utils;

import com.chervon.fleet.web.entity.po.LogOperate;
import java.util.Objects;

/**
 * 操作日志帮助类
 */
public class OperationLogUtils {

    private final static ThreadLocal<LogOperate> LOG_OPERATE_THREAD_LOCAL = new InheritableThreadLocal<>();

    public static LogOperate get() {
        LogOperate operationLog = LOG_OPERATE_THREAD_LOCAL.get();
        if(Objects.isNull(operationLog)){
            operationLog=new LogOperate();
            append(operationLog);
        }
        return operationLog;
    }

    public static LogOperate append(LogOperate operationLog) {
        LOG_OPERATE_THREAD_LOCAL.set(operationLog);
        return operationLog;
    }

    public static void remove() {
        LOG_OPERATE_THREAD_LOCAL.remove();
    }
}
