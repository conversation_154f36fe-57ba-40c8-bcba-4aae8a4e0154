package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 电池使用量统计表(看板：Battery Usage图)(t_bi_daily_battery_usage)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_daily_battery_usage")
public class BiDailyBatteryUsage extends Model<BiDailyBatteryUsage> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 电池分类
     */
    private String batteryType;
    /**
     * 日期：2023-07-12
     */
    private Date date;
    /**
     * 日期字符串: yyyy-MM-dd
     */
    private String strDate;
    /**
     * 每日平均使用时长=设备日总使用时长÷设备数量，统计单位为分钟
     */
    private Integer dailyAverageUsageTime;
    /**
     * 每日总使用次数
     */
    private Integer dailyUsageTimes;
    /**
     * 每日总使用时长：分钟
     */
    private Integer dailyUsageDuration;
    /**
     * 当日电池总使用数量（当前分类下）
     */
    private Integer dailyDeviceUsageCount;
    /**
     * 更新时间
     */
    private Date modifyTime;

    public static Boolean propertyNotNull(BiDailyBatteryUsage biDailyBatteryUsage) {
        return biDailyBatteryUsage.getDailyDeviceUsageCount() != null &&
            biDailyBatteryUsage.getDailyUsageTimes() != null &&
            biDailyBatteryUsage.getDailyAverageUsageTime() != null;
    }

    public void setId(){
        this.id=(long)(this.companyId.toString().concat(batteryType).concat(this.strDate)).hashCode();
    }
}