package com.chervon.fleet.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.constant.StringPool;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.UUIDUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.fleet.web.api.entity.dto.*;
import com.chervon.fleet.web.api.entity.enums.*;
import com.chervon.fleet.web.api.entity.query.DeviceShadowQuery;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.DeviceShadowVo;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.*;
import com.chervon.fleet.web.entity.enums.MqttMessageTypeEnum;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.service.AppGatewayService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.RuleEngineService;
import com.chervon.iot.middle.api.dto.device.IotPublishDto;
import com.chervon.iot.middle.api.service.RemoteDeviceShadowService;
import com.chervon.technology.api.toruleengine.IotUpdateOnlineStatusDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 规则引擎回调服务逻辑
 *
 * <AUTHOR> 2023/7/12
 */
@Slf4j
@Service
public class RuleEngineServiceImpl implements RuleEngineService {
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private AppGatewayService appGatewayService;
    @DubboReference
    private RemoteDeviceShadowService remoteDeviceShadowService;
    @Autowired
    private FleetDeviceConfig fleetDeviceConfig;

    @Override
    public void refreshWarehouseStatus(GatewayScanReportingDto gatewayScanReportingDto, Integer softHardGatewayType, String gatewayId) {
        //读取网关信息
        final ReportGatewayDto reportGatewayDto = gatewayScanReportingDto.getGateway();
        //取服务器当前时间，避免各个手机时间不一致
        reportGatewayDto.setReportTime(System.currentTimeMillis());
        //读取设备列表状态
        List<ReportDeviceDto> reportDeviceList = gatewayScanReportingDto.getDevices();
        //过滤无效设备id
        reportDeviceList.removeIf(a->a==null || a.getDeviceId()==null || a.getDeviceId().equals("null"));
        //验证网关是否存在
        final GatewayCache gatewayCache = appGatewayService.getByCache(gatewayId);
        if (Objects.isNull(gatewayCache)) {
            log.error("refreshWarehouseStatus: gateway info is not exist! gatewayId:{}", gatewayId);
            return;
        }
        // 刷新网关信息：地址，坐标，最后上报时间，更新人，更新时间
        refreshGatewayLocation(gatewayId, reportGatewayDto, gatewayCache);
        // 过滤非租户下绑定的设备，过滤网关类型设备的库存上报
        if (deviceFilter(reportDeviceList, gatewayCache.getCompanyId())) {
            return;
        }
        //更新库存状态redis缓存
        Integer warehouseStatus = getWarehouseStatusByFixGatewayType(gatewayCache.getGatewayType());
        List<String> usefulDeviceIds = new ArrayList<>();
        //redis缓存刷新处理
        cacheProcess(gatewayId, reportDeviceList, warehouseStatus, gatewayCache, reportGatewayDto, usefulDeviceIds);
        //批量刷新设备表库存状态和对应网关id
        updateCompanyDevice(gatewayId, usefulDeviceIds, gatewayCache, warehouseStatus);
        //发布mqtt消息通知android端更新设备库存状态
        try {
            //上报的坐标为空，从缓存中赋值（固定网关不上报坐标）
            if (StringUtils.isEmpty(reportGatewayDto.getLocation())) {
                reportGatewayDto.setLocation(gatewayCache.getCoordinate());
            }
            publishDeviceWarehouseStatus(reportGatewayDto, reportDeviceList, gatewayId);
        } catch (IOException e) {
            log.error("发布mqtt消息通知android端更新设备库存状态发生异常", e);
        }
    }

    private void updateCompanyDevice(String gatewayId, List<String> usefulDeviceIds, GatewayCache appGateway, Integer warehouseStatus) {
        if (!CollectionUtils.isEmpty(usefulDeviceIds)) {
            companyDeviceService.update(new CompanyDevice(), new LambdaUpdateWrapper<CompanyDevice>()
                    .eq(CompanyDevice::getCompanyId, appGateway.getCompanyId())
                    .in(CompanyDevice::getDeviceId, usefulDeviceIds)
                    .set(CompanyDevice::getWarehouseStatus, warehouseStatus)
                    .set(CompanyDevice::getGatewayId, gatewayId)
                    .set(CompanyDevice::getModifier, "update-warehouse-status")
                    .set(CompanyDevice::getModifyTime, System.currentTimeMillis()));
        }
    }

    private static void cacheProcess(String gatewayId, List<ReportDeviceDto> reportDeviceList, Integer warehouseStatus, GatewayCache gatewayCache, ReportGatewayDto reportGatewayDto, List<String> usefulDeviceIds) {
        for (ReportDeviceDto deviceDto : reportDeviceList) {
            if (deviceDto.getStatus() == ScanStatusEnum.FOUND.getType()) {
                // 扫描到
                WarehouseStatusCache warehouseStatusCache = new WarehouseStatusCache(deviceDto.getDeviceId())
                        .setWarehouseStatus(warehouseStatus)
                        .setRssi(deviceDto.getRssi())
                        .setReportTime(deviceDto.getReportTime())
                        .setCompanyId(gatewayCache.getCompanyId())
                        .setGatewayId(gatewayId)
                        .setCoordinate(gatewayCache.getCoordinate())
                        .setLocation(gatewayCache.getLocation());
                //读取设备状态缓存key
                final String deviceWarehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(deviceDto.getDeviceId());
                //刷新设备库存状态
                RedisUtils.setCacheObject(deviceWarehouseStatusKey, warehouseStatusCache);
                //读取租户网关关系map大key
                final String companyGatewayKey = RedisConst.getCompanyGatewayMapKey(gatewayCache.getCompanyId(), gatewayCache.getId());
                //刷新网关-设备关系
                RedisUtils.setCacheMapValue(companyGatewayKey, deviceDto.getDeviceId(), reportGatewayDto.getReportTime());
                usefulDeviceIds.add(deviceDto.getDeviceId());
            } else{
                // 未扫描到
                //读取租户网关关系map大key
                final String companyGatewayKey = RedisConst.getCompanyGatewayMapKey(gatewayCache.getCompanyId(), gatewayCache.getId());
                RedisUtils.delCacheMapValue(companyGatewayKey, deviceDto.getDeviceId());
            }
        }
    }

    /**
     * 按租户过滤非租户下的设备，过滤网关设备的库存上报
     * @param reportDeviceList
     * @param companyId
     * @return
     */
    private boolean deviceFilter(List<ReportDeviceDto> reportDeviceList, Long companyId) {
        List<String> deviceIds = reportDeviceList.stream().map(ReportDeviceDto::getDeviceId).filter(StringUtils::hasLength).collect(Collectors.toList());
        if (deviceIds.isEmpty() || companyId == null) {
            return true;
        }
        List<CompanyDevice> companyDevices = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .in(CompanyDevice::getDeviceId, deviceIds)
                .select(CompanyDevice::getDeviceId));
        List<String> restDeviceIds = companyDevices.stream().map(CompanyDevice::getDeviceId).collect(Collectors.toList());
        reportDeviceList.removeIf(e -> !restDeviceIds.contains(e.getDeviceId()));
        if (CollectionUtils.isEmpty(reportDeviceList)) {
            return true;
        }
        // 过滤网关设备上报
        fleetDeviceConfig.getSub().getExclude().forEach(e -> reportDeviceList.removeIf(i -> com.chervon.common.core.utils.StringUtils.containsIgnoreCase(i.getDeviceId(), e)));
        return false;
    }

    private void refreshGatewayLocation(String gatewayId, ReportGatewayDto reportGatewayDto, GatewayCache gatewayCache) {
        // 更新网关最新更新时间
        LambdaUpdateWrapper<AppGateway> updateWrapper = new LambdaUpdateWrapper<AppGateway>()
                .eq(AppGateway::getId, gatewayId)
                .set(AppGateway::getLastConnectedTime, reportGatewayDto.getReportTime())
                .set(AppGateway::getModifier, gatewayId)
                .set(AppGateway::getModifyTime, System.currentTimeMillis());
        //更新坐标和地址(移动网关)
        if (StringUtils.hasText(reportGatewayDto.getLocation())) {
            updateWrapper.set(AppGateway::getCoordinate, reportGatewayDto.getLocation());
            gatewayCache.setCoordinate(reportGatewayDto.getLocation());
        }
        if (StringUtils.hasText(reportGatewayDto.getAddress())) {
            updateWrapper.set(AppGateway::getLocation, reportGatewayDto.getAddress());
            gatewayCache.setLocation(reportGatewayDto.getAddress());
        }
        gatewayCache.setLastConnectedTime(reportGatewayDto.getReportTime());
        //刷新网关缓存：地址，坐标，最后上报时间，更新人，更新时间
        RedisUtils.setWithExpire(RedisConst.getGatewayKey(gatewayId), gatewayCache, RedisConst.GATEWAY_INFO_EXPIRE_SECOND);
        //刷新网关表：地址，坐标，最后上报时间，更新人，更新时间
        appGatewayService.update(updateWrapper);
    }

    /**
     * 设备数据上报完成通知
     *
     * @param request
     */
    @Override
    public void dataReportComplete(DataReportCompleteDto request, String gatewayId, String deviceId) {
//        //验证网关是否存在
//        final AppGateway appGateway = appGatewayService.get(new GatewayQuery().setGatewayId(gatewayId));
//        if (Objects.isNull(appGateway)) {
//            log.error("dataReportComplete: gateway info is not exist! gatewayId:{}", gatewayId);
//            return;
//        }
        //读取设备租户绑定关系
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getDeviceId,deviceId)
                .select(CompanyDevice::getCompanyId);
        final List<CompanyDevice> listCompanyDevice = companyDeviceService.list(queryWrapper);
        if (CollectionUtils.isEmpty(listCompanyDevice)) {
            log.error("dataReportComplete: companyDevice info is not exist! deviceId:{}", deviceId);
            return;
        }
        Long companyId = listCompanyDevice.get(listCompanyDevice.size()-1).getCompanyId();
        if(Objects.isNull(companyId)){
            return;
        }
        //读取租户设备数据上报完成时间缓存key
        final String dataReportCompleteKey = RedisConst.getDataReportCompleteKey(companyId);
        //刷新设备数据上报完成时间缓存
        RedisUtils.setCacheMapValue(dataReportCompleteKey, deviceId, request.getReportCompleteTime());
        //发布mqtt消息通知android端刷新设备数据上报完成云朵状态标记
        try {
            //内部可能抛出IO异常进行捕捉
            publishDataReportCompleteTime(request, deviceId, gatewayId);
        } catch (IOException e) {
            log.error("发布mqtt消息通知android端刷新设备数据上报完成云朵状态标记发生异常", e);
        }
    }


    /**
     * 处理app端上报的软网关上报在线离线状态更新
     * @param clientId clientId ：gatewayId+deviceId/ts
     * @param status 状态:connected/disconnected
     */
    @Override
    public void gatewayOnlineStatusReport(String clientId, String status) {
        if (!com.chervon.common.core.utils.StringUtils.startsWith(clientId, StringConst.GATEWAY_ID)
                || !Arrays.asList(OnlineStatusEnum.ONLINE.getValue(), OnlineStatusEnum.OFFLINE.getValue()).contains(status)) {
            log.warn("param error.[clientId]:{}, [status]:{}", clientId, status);
            return;
        }
        String gatewayId = clientId.substring(CommonConstant.NINE);
        if(gatewayId.contains(StringPool.DIVIDE)){
            gatewayId=gatewayId.split(StringPool.DIVIDE)[0];
        }
        if(StringUtils.isEmpty(gatewayId)){
            log.error("客户端id错误！"+clientId);
        }
        GatewayCache gatewayCache = appGatewayService.getByCache(gatewayId);
        if (gatewayCache == null) {
            log.warn("gateway not found by gatewayId:{}", gatewayId);
            return;
        }
        AppGateway appGateway = new AppGateway();
        appGateway.setId(gatewayId);
        appGateway.setOnlineStatus(OnlineStatusEnum.getTypeByDesc(status));
        appGatewayService.updateById(appGateway);
    }

    /**
     * 网关扫描设备状态上报发布mqtt消息给前端Android订阅刷新设备库存状态
     *
     * @param reportGatewayDto 网关上报结构
     * @param reportDeviceList 网关扫描的设备结构
     */
    public void publishDeviceWarehouseStatus(ReportGatewayDto reportGatewayDto, List<ReportDeviceDto> reportDeviceList, String gatewayId) throws IOException {
        //构造消息载荷对象
        WarehouseStatusBody messageBody = BeanCopyUtils.copy(reportGatewayDto, WarehouseStatusBody.class);
        //构造网关子设备的库存状态列表
        Integer warehouseStatus = getWarehouseStatusByFixGatewayType(reportGatewayDto.getGatewayType());
        List<DeviceStatusSubBody> deviceStatusSubBodies = reportDeviceList.stream().filter(a->a.getStatus().equals(ScanStatusEnum.FOUND.getType()))
                .map(a -> new DeviceStatusSubBody()
                        .setDeviceId(a.getDeviceId())
                        .setRssi(a.getRssi())
                        .setWarehouseStatus(warehouseStatus))
                .collect(Collectors.toList());
        //赋值设备状态子body
        messageBody.setListDevice(deviceStatusSubBodies);
        messageBody.setGatewayId(gatewayId);
        //发布mqtt消息主题：通知android端设备库存状态变化
        publishMqttMessage(MqttMessageTypeEnum.WAREHOUSE_STATUS_NOTICE.getType(), messageBody);
    }

    /**
     * 发布mqtt消息通知android端刷新设备数据上报完成云朵状态标记
     *
     * @param request 设备数据上报完成请求对象
     * @throws IOException
     */
    @Override
    public void publishDataReportCompleteTime(DataReportCompleteDto request, String deviceId, String gatewayId) throws IOException {
        //构造消息载荷对象
        DataReportCompleteBody messageBody = new DataReportCompleteBody()
                .setDeviceId(deviceId)
                .setGatewayId(gatewayId)
                .setStatus(ReportCompleteStatusEnum.REPORT_COMPLETE.getType());
        //发布mqtt消息通知android端刷新设备数据上报完成云朵状态标记
        publishMqttMessage(MqttMessageTypeEnum.DATA_REPORT_COMPLETE_NOTICE.getType(), messageBody);
    }

    /**
     * 发布mqtt消息通知android端设备有绑定和解绑的变化
     *
     * @param request 设备绑定解绑请求对象
     */
    @Override
    public void publishMsgDeviceBindAndUnBind(DeviceBindChangeDto request) {
        //构造消息载荷对象
        DeviceBindNoticeBody messageBody = BeanCopyUtils.copy(request, DeviceBindNoticeBody.class);
        //发布mqtt消息通知android端设备有绑定和解绑的变化
        try {
            publishMqttMessage(MqttMessageTypeEnum.DEVICE_BIND_NOTICE.getType(), messageBody);
        } catch (IOException e) {
            log.error("publishMsgDeviceBindAndUnBind error", e);
        }
    }

    /**
     * 发布mqtt消息通知android端设备在线离线状态的变化
     *
     * @param request 设备在线离线状态请求参数
     * @throws IOException
     */
    @Override
    public void publishMsgDeviceOnlineStatusChange(IotUpdateOnlineStatusDto request) throws IOException {
        //转换在线状态为数字类型
        final Integer onlineStatus = OnlineStatusEnum.getTypeByDesc(request.getStatus());
        //构造消息载荷对象
        DeviceOnlineBody messageBody = new DeviceOnlineBody()
                .setDeviceId(request.getClientId())
                .setStatus(onlineStatus);
        //发布mqtt消息通知android端设备有在线离线状态的变化
        publishMqttMessage(MqttMessageTypeEnum.DEVICE_ONLINE_NOTICE.getType(), messageBody);
    }

    @Override
    public void publishMsgDeviceOnlineStatusChange(String deviceId, Integer onlineStatus) throws IOException {
        //构造消息载荷对象
        DeviceOnlineBody messageBody = new DeviceOnlineBody()
                .setDeviceId(deviceId)
                .setStatus(onlineStatus);
        //发布mqtt消息通知android端设备有在线离线状态的变化
        publishMqttMessage(MqttMessageTypeEnum.DEVICE_ONLINE_NOTICE.getType(), messageBody);
    }

    private void publishMqttMessage(Integer messageType, Object messageBody) throws IOException {
        //构造发布消息的消息对象
        IotPublishDto iotPublishDto = new IotPublishDto();
        //消息topic主题
        iotPublishDto.setTopic(StringConst.DEVICE_STATUS_REFRESH_GET_ACCEPTED);
        //构造消息主体
        MqttMessageDto messageDto = new MqttMessageDto();
        messageDto.setMessageId(UUIDUtils.randomUUID());
        messageDto.setMessageType(messageType);
        messageDto.setMessageBody(messageBody);
        //消息载荷对象赋值
        iotPublishDto.setPayLoad(messageDto);
        //发布mqtt消息通知android端设备有绑定和解绑的变化
        log.info("发送MQTT设备影子：" + JSON.toJSONString(iotPublishDto));
        remoteDeviceShadowService.publish(iotPublishDto);
    }

    /**
     * 设备绑定和解绑的数据上报处理：
     * 绑定：初始化设备库存状态缓存map列表，置为初始状态；发布mqtt通知前端刷新库存列表
     * 解绑：移除设备基础信息缓存；移除设备库存状态缓存map；移除网关下关联的设备关系；
     *
     * @param request
     */
    @Override
    public void deviceBindOrUnBindProcess(DeviceBindChangeDto request) {
        //解绑设备操作
        if (request.getStatus().equals(ChangeTypeEnum.DELETED.getType())) {
            unBindDevice(request);
        } else if (request.getStatus().equals(ChangeTypeEnum.ADD.getType())) {
            //添加绑定设备
            CompanyDevice companyDevice = companyDeviceService.getOne(new LambdaQueryWrapper<CompanyDevice>()
                    .eq(CompanyDevice::getCompanyId, request.getCompanyId()).eq(CompanyDevice::getDeviceId, request.getDeviceId()));
            if (companyDevice != null) {
                bindDevice(request, companyDevice);
            }
        }else{
            //nothing to do
        }
        publishMsgDeviceBindAndUnBind(request);
    }

    private static void bindDevice(DeviceBindChangeDto request, CompanyDevice companyDevice) {
        // 设置设备基本信息
        DeviceBasicCache deviceBasicCache = new DeviceBasicCache();
        BeanUtils.copyProperties(companyDevice, deviceBasicCache);
        String deviceInfoKey = RedisConst.getDeviceInfoKey(request.getDeviceId());
        RedisUtils.setWithExpire(deviceInfoKey, deviceBasicCache, RedisConst.DEVICE_INFO_EXPIRE_SECOND);
        // 设置租户与设备状态关系缓存
        WarehouseStatusCache warehouseStatusCache = new WarehouseStatusCache();
        warehouseStatusCache.setDeviceId(companyDevice.getDeviceId());
        warehouseStatusCache.setCompanyId(companyDevice.getCompanyId());
        warehouseStatusCache.setWarehouseStatus(companyDevice.getWarehouseStatus());
        String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(request.getDeviceId());
        RedisUtils.setCacheObject(warehouseStatusKey, warehouseStatusCache);
        // 设置租户与设备集合关系
        String deviceListKey = RedisConst.getCompanyDeviceListKey(companyDevice.getCompanyId());
        List<String> companyDeviceIds = RedisUtils.getCacheObject(deviceListKey);
        if (companyDeviceIds == null) {
            companyDeviceIds = new ArrayList<>();
        }
        if (!companyDeviceIds.contains(companyDevice.getDeviceId())) {
            companyDeviceIds.add(companyDevice.getDeviceId());
        }
        request.setGatewayId(companyDevice.getGatewayId());
        RedisUtils.setWithExpire(deviceListKey, companyDeviceIds, RedisConst.DEVICE_LIST_EXPIRE_SECOND);
    }

    private static void unBindDevice(DeviceBindChangeDto request) {
        //移除设备基础信息缓存
        final String deviceInfoKey = RedisConst.getDeviceInfoKey(request.getDeviceId());
        RedisUtils.deleteObject(deviceInfoKey);
        //移除租户与设备状态关系缓存
        final String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(request.getDeviceId());
        final WarehouseStatusCache cacheMapValue = RedisUtils.getCacheObject(warehouseStatusKey);
        //移除网关与设备关系缓存
        if (cacheMapValue != null && !Objects.isNull(cacheMapValue.getGatewayId())) {
            final String companyGatewayMapKey = RedisConst.getCompanyGatewayMapKey(request.getCompanyId(), cacheMapValue.getGatewayId());
            RedisUtils.delCacheMapValue(companyGatewayMapKey, request.getDeviceId());
        }
        RedisUtils.deleteObject(warehouseStatusKey);
        //移除租户与设备id列表关系缓存
        final String deviceListKey = RedisConst.getCompanyDeviceListKey(request.getCompanyId());
        List<String> companyDeviceIds = RedisUtils.getCacheObject(deviceListKey);
        if (companyDeviceIds != null) {
            companyDeviceIds.remove(request.getDeviceId());
            RedisUtils.setWithExpire(deviceListKey, companyDeviceIds, RedisConst.DEVICE_LIST_EXPIRE_SECOND);
        }
        request.setGatewayId(cacheMapValue == null ? "0" : cacheMapValue.getGatewayId());
    }

    /**
     * * 保存设备上报的故障信息
     *
     * @param deviceId 设备id
     * @param reported 上报日志
     */
    @Override
    public void saveFaultData(String deviceId, Object reported) {

    }

    @Override
    public void updateDeviceInfo(CompanyDevice companyDevice) {
        // 修改设备基本信息
        String deviceInfoKey = RedisConst.getDeviceInfoKey(companyDevice.getDeviceId());
        DeviceBasicCache deviceBasicCache = RedisUtils.getCacheObject(deviceInfoKey);
        if (deviceBasicCache == null) {
            deviceBasicCache = new DeviceBasicCache();
        }
        deviceBasicCache.setDeviceId(companyDevice.getDeviceId());
        deviceBasicCache.setDeviceSn(companyDevice.getDeviceSn());
        deviceBasicCache.setDeviceName(companyDevice.getDeviceName());
        deviceBasicCache.setCompanyId(companyDevice.getCompanyId());
        RedisUtils.setWithExpire(deviceInfoKey, deviceBasicCache, RedisConst.DEVICE_INFO_EXPIRE_SECOND);
    }

    @Override
    public void updateGatewayInfo(AppGateway gateway) {
        String gatewayKey = RedisConst.getGatewayKey(gateway.getId());
        GatewayCache gatewayCache = RedisUtils.getCacheObject(gatewayKey);
        if (gatewayCache == null) {
            gatewayCache = new GatewayCache();
        }
        BeanUtils.copyProperties(gateway, gatewayCache);
        RedisUtils.setWithExpire(gatewayKey, gatewayCache, RedisConst.GATEWAY_INFO_EXPIRE_SECOND);
        // 如果是硬件网关，要找到硬件设备，设置地理位置
        if (gateway.getType() == GatewayTypeEnum.HARD.getType() && StringUtils.hasText(gateway.getDeviceId())) {
            String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(gateway.getDeviceId());
            WarehouseStatusCache warehouseStatusCache = RedisUtils.getCacheObject(warehouseStatusKey);
            if (warehouseStatusCache != null) {
                warehouseStatusCache.setLocation(gateway.getLocation());
                warehouseStatusCache.setCoordinate(gateway.getCoordinate());
                RedisUtils.setCacheObject(warehouseStatusKey, warehouseStatusCache);
            }
        }
        // 修改该网关先的设备信息
        final String companyGatewayMapKey = RedisConst.getCompanyGatewayMapKey(gateway.getCompanyId(), gateway.getId());
        Map<String, Object> companyGatewayDevices = RedisUtils.getCacheMap(companyGatewayMapKey);
        if (!CollectionUtils.isEmpty(companyGatewayDevices)) {
            companyGatewayDevices.forEach((k, v) -> {
                String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(k);
                WarehouseStatusCache warehouseStatusCache = RedisUtils.getCacheObject(warehouseStatusKey);
                if (warehouseStatusCache != null) {
                    warehouseStatusCache.setCoordinate(gateway.getCoordinate());
                    warehouseStatusCache.setLocation(gateway.getLocation());
                    RedisUtils.setCacheObject(warehouseStatusKey, warehouseStatusCache);
                }
            });
        }
    }

    /**
     * 移除网关的数据上报处理：
     * 添加：初始化设备库存状态缓存map列表，置为初始状态；发布mqtt通知前端刷新库存列表
     * 移除：移除租户网关关系缓存map
     *
     * @param request
     */
    @Override
    public void addRemoveGatewayDataProcess(GatewayChangeDto request) {
        //移除网关操作
        if (request.getStatus().equals(ChangeTypeEnum.DELETED.getType())) {
            removeGateway(request);
        } else if (request.getStatus().equals(ChangeTypeEnum.ADD.getType())) {
            //添加网关
            AppGateway gateway = appGatewayService.getById(request.getGatewayId());
            if (gateway != null) {
                addGateway(gateway);
            }
        }else{
            //nothing to do
        }
    }

    private static void addGateway(AppGateway gateway) {
        GatewayCache gatewayCache = new GatewayCache();
        BeanUtils.copyProperties(gateway, gatewayCache);
        String gatewayKey = RedisConst.getGatewayKey(gateway.getId());
        RedisUtils.setWithExpire(gatewayKey, gatewayCache, RedisConst.GATEWAY_INFO_EXPIRE_SECOND);
        // 如果是硬件网关，要找到硬件设备，设置地理位置
        if (gateway.getType() == GatewayTypeEnum.HARD.getType() && StringUtils.hasText(gateway.getDeviceId())) {
            String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(gateway.getDeviceId());
            WarehouseStatusCache warehouseStatusCache = RedisUtils.getCacheObject(warehouseStatusKey);
            if (warehouseStatusCache != null) {
                warehouseStatusCache.setLocation(gateway.getLocation());
                warehouseStatusCache.setCoordinate(gateway.getCoordinate());
                RedisUtils.setCacheObject(warehouseStatusKey, warehouseStatusCache);
            }
        }
    }

    private static void removeGateway(GatewayChangeDto request) {
        //移除网关详情缓存
        final String gatewayKey = RedisConst.getGatewayKey(request.getGatewayId());
        RedisUtils.deleteObject(gatewayKey);
        //移除网关与设备关系缓存
        final String companyGatewayMapKey = RedisConst.getCompanyGatewayMapKey(request.getCompanyId(), request.getGatewayId());
        Map<String, Object> companyGatewayDevices = RedisUtils.getCacheMap(companyGatewayMapKey);
        if (!CollectionUtils.isEmpty(companyGatewayDevices)) {
            companyGatewayDevices.forEach((k, v) -> {
                // 根据deviceId，修改设备状态
                if (k != null) {
                    final String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(k);
                    WarehouseStatusCache warehouseStatusCache = RedisUtils.getCacheObject(warehouseStatusKey);
                    if (warehouseStatusCache != null) {
                        warehouseStatusCache.setGatewayId(null);
                        RedisUtils.setCacheObject(warehouseStatusKey, warehouseStatusCache);
                    }
                }
            });
        }
        RedisUtils.deleteObject(companyGatewayMapKey);
    }

    /**
     * 异步批量更新设备库存状态 *
     *
     * @param reportGatewayDto 上报的网关信息
     * @param reportDeviceList 上报的设备信息
     * @param appGateway       数据库网关信息
     */
    private void asyncUpdateWarehouseStatus(ReportGatewayDto reportGatewayDto, List<ReportDeviceDto> reportDeviceList, GatewayCache appGateway) {


        //异步批量更新设备库存状态
        List<String> notFoundDevice = reportDeviceList.stream().filter(a -> a.getStatus().equals(ScanStatusEnum.NOT_FOUND.getType())).map(ReportDeviceDto::getDeviceId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notFoundDevice)) {
            companyDeviceService.updateDeviceStatus(new CompanyDeviceDto().setCompanyId(appGateway.getCompanyId())
                    .setDeviceIds(notFoundDevice)
                    .setGatewayId(appGateway.getId()));
        }
        List<String> foundDevice = reportDeviceList.stream().filter(a -> a.getStatus().equals(ScanStatusEnum.FOUND.getType())).map(ReportDeviceDto::getDeviceId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(foundDevice)) {
            companyDeviceService.updateDeviceStatus(new CompanyDeviceDto().setCompanyId(appGateway.getCompanyId())
                    .setWarehouseStatus(getOnlineWarehouseStatus(reportGatewayDto))
                    .setDeviceIds(foundDevice)
                    .setGatewayId(appGateway.getId()));
        }
    }

    /**
     * * 网关扫描在线，返回库存状态
     *
     * @param gatewayDto
     * @return
     */
    private static int getOnlineWarehouseStatus(ReportGatewayDto gatewayDto) {
        return gatewayDto.getGatewayType().equals(SoftGatewayTypeEnum.FIXED.getType())
                ? WarehouseStatusEnum.IN_WAREHOUSE.getType() : WarehouseStatusEnum.OUT_FOR_WORK.getType();
    }

    private static int getWarehouseStatusByFixGatewayType(Integer fixMobileGatewayType) {
        if (fixMobileGatewayType == SoftGatewayTypeEnum.FIXED.getType()) {
            return WarehouseStatusEnum.IN_WAREHOUSE.getType();
        }
        return WarehouseStatusEnum.OUT_FOR_WORK.getType();
    }

    /**
     * * 根据设备id或网关id或租户Id读取设备的库存状态及数据上报完成状态
     *
     * @param query 设备id、网关id、租户Id
     * @return 设备的库存状态及数据上报完成状态
     */
    @Override
    public List<DeviceStatusVo> getWarehouseStatusByCache(InventoryStatusQuery query) {
        Assert.isId(query.getCompanyId(), ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        //设备列表为空，代表根据网关或者租户查询库存状态
        if (CollectionUtils.isEmpty(query.getListDeviceId())) {
            //根据租户查询设备库存列表
            final List<String> listDeviceId = companyDeviceService.getDeviceListByCache(query.getCompanyId());
            query.setListDeviceId(listDeviceId);
        }
        //没有找到任何关联设备返回空结果
        if (CollectionUtils.isEmpty(query.getListDeviceId())) {
            return Collections.emptyList();
        }
        //读取设备数据上报完成时间缓存key
        final String dataReportCompleteKey = RedisConst.getDataReportCompleteKey(query.getCompanyId());
        List<DeviceStatusVo> list = new ArrayList<>();
        //查询的设备id，用于批量查询map结构缓存
        Set<String> deviceIdSet = new HashSet<>(query.getListDeviceId());
        //批量读取数据上报完成时间缓存内容
        final Map<String, Long> dataReportCompleteMap = RedisUtils.getMultiCacheMapValue(dataReportCompleteKey, deviceIdSet);
        //构造返回各个状态设备对象
        for (String deviceId : query.getListDeviceId()) {
            DeviceStatusVo deviceStatusVo;
            //追加库存状态
            //读取设备状态缓存
            final WarehouseStatusCache warehouseStatusCache = companyDeviceService.getWarehouseStatusById(deviceId);
            if (!Objects.isNull(warehouseStatusCache)) {
                deviceStatusVo = BeanCopyUtils.copy(warehouseStatusCache, DeviceStatusVo.class);
            } else {//设备信息为空，跳过
                continue;
            }
            //状态过滤
            if (!CollectionUtils.isEmpty(query.getWarehouseStatus()) &&
                    !query.getWarehouseStatus().contains(warehouseStatusCache.getWarehouseStatus())) {
                continue;
            }
            // 判断网关
            if (StringUtils.hasText(query.getGatewayId()) &&
                    !Objects.equals(query.getGatewayId(), warehouseStatusCache.getGatewayId())) {
                continue;
            }
            //追加数据上报完成时间戳和状态
            final Long reportTime = dataReportCompleteMap.get(deviceId);
            deviceStatusVo.setDataReportCompleteTime(Objects.isNull(reportTime) ? 0L : reportTime);
            deviceStatusVo.setReportCompleteStatus(deviceStatusVo.calcCompleteStatus());
            list.add(deviceStatusVo);
        }
        return list;
    }

    @Override
    public List<DeviceStatusVo> getWarehouseStatusOnly(InventoryStatusQuery query) {
        Assert.isId(query.getCompanyId(), ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        //设备列表为空，代表根据网关或者租户查询库存状态
        if (CollectionUtils.isEmpty(query.getListDeviceId())) {
            //根据租户查询设备库存列表
            final List<String> listDeviceId = companyDeviceService.getDeviceListByCache(query.getCompanyId());
            query.setListDeviceId(listDeviceId);
        }
        //没有找到任何关联设备返回空结果
        if (CollectionUtils.isEmpty(query.getListDeviceId())) {
            return Collections.emptyList();
        }
        List<DeviceStatusVo> list = new ArrayList<>();
        //构造返回各个状态设备对象
        for (String deviceId : query.getListDeviceId()) {
            DeviceStatusVo deviceStatusVo;
            //追加库存状态
            //读取设备状态缓存
            final WarehouseStatusCache warehouseStatusCache = companyDeviceService.getWarehouseStatusById(deviceId);
            if (!Objects.isNull(warehouseStatusCache)) {
                deviceStatusVo = BeanCopyUtils.copy(warehouseStatusCache, DeviceStatusVo.class);
            } else {//设备信息为空，跳过
                continue;
            }
            //状态过滤
            if (!CollectionUtils.isEmpty(query.getWarehouseStatus()) &&
                    !query.getWarehouseStatus().contains(warehouseStatusCache.getWarehouseStatus())) {
                continue;
            }
            // 判断网关
            if (StringUtils.hasText(query.getGatewayId()) &&
                    !Objects.equals(query.getGatewayId(), warehouseStatusCache.getGatewayId())) {
                continue;
            }
            list.add(deviceStatusVo);
        }
        return list;
    }

    /**
     * * 根据设备id或网关id或租户Id读取设备的库存状态及数据上报完成状态
     *
     * @param query 设备id、网关id、租户Id
     * @return 设备的库存状态及数据上报完成状态
     */
    @Override
    public List<DeviceStatusVo> getReportCompleteStatus(InventoryStatusQuery query) {
        Assert.isId(query.getCompanyId(), ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        //设备列表为空，代表根据网关或者租户查询库存状态
        if (CollectionUtils.isEmpty(query.getListDeviceId())) {
            //根据租户查询设备库存列表
            final List<String> listDeviceId = companyDeviceService.getDeviceListByCache(query.getCompanyId());
            query.setListDeviceId(listDeviceId);
        }
        //没有找到任何关联设备返回空结果
        if (CollectionUtils.isEmpty(query.getListDeviceId())) {
            return Collections.emptyList();
        }
        //读取设备数据上报完成时间缓存key
        final String dataReportCompleteKey = RedisConst.getDataReportCompleteKey(query.getCompanyId());
        //查询的设备id，用于批量查询map结构缓存
        Set<String> deviceIdSet = new HashSet<>(query.getListDeviceId());
        //批量读取数据上报完成时间缓存内容
        final Map<String, Long> dataReportCompleteMap = RedisUtils.getMultiCacheMapValue(dataReportCompleteKey, deviceIdSet);
        //构造返回各个状态设备对象
        List<DeviceStatusVo> list = new ArrayList<>();
        for (String deviceId : query.getListDeviceId()) {
            DeviceStatusVo deviceStatusVo=new DeviceStatusVo();
            deviceStatusVo.setCompanyId(query.getCompanyId());
            deviceStatusVo.setDeviceId(deviceId);
            //追加数据上报完成时间戳和状态
            final Long reportTime = dataReportCompleteMap.get(deviceId);
            deviceStatusVo.setDataReportCompleteTime(Objects.isNull(reportTime) ? 0L : reportTime);
            deviceStatusVo.setReportCompleteStatus(deviceStatusVo.calcCompleteStatus());
            list.add(deviceStatusVo);
        }
        return list;
    }

    /**
     * * 根据缓存获取设备影子和故障信息
     *
     * @param query
     * @return 设备影子信息
     */
    @Override
    public List<DeviceShadowVo> getDeviceShadowVoByCache(DeviceShadowQuery query) {
        return Collections.emptyList();
    }
}
