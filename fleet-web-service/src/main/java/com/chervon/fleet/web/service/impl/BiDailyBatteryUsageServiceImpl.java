package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.query.dashboard.BatteryUsageDashboardQuery;
import com.chervon.fleet.web.entity.po.BiDailyBatteryUsage;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.vo.FleetCategoryFilterVo;
import com.chervon.fleet.web.entity.vo.dashboard.BatteryUsageDashboardVo;
import com.chervon.fleet.web.mapper.BiDailyBatteryUsageMapper;
import com.chervon.fleet.web.service.BiDailyBatteryUsageService;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 电池使用量统计表(看板：Battery Usage图)服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiDailyBatteryUsageServiceImpl extends ServiceImpl<BiDailyBatteryUsageMapper, BiDailyBatteryUsage>
    implements BiDailyBatteryUsageService {

    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    /**
     * 获取电池类型列表
     * @return 电池类型列表
     */
    public List<FleetCategoryFilterVo> getBatteryTypeList(){
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCategoryLevel(CommonConstant.TWO);
        fleetCategoryQuery.setParentCategoryCode(FleetFirstCategoryEnum.BATTERY.getCategoryCode());
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        if(CollectionUtils.isEmpty(fleetCategoryList)){
            return new ArrayList();
        }
        return fleetCategoryList.stream().map(item -> {
            return new FleetCategoryFilterVo().setCategoryCode(item.getCode())
                    .setCategoryName(item.getCategoryName());
        }).collect(Collectors.toList());
    }
    /**
     * 电池使用量统计表(看板：Battery Usage图)
     * @param query 查询条件
     * @return
     */
    @Override
    public List<BatteryUsageDashboardVo> batteryUsage(BatteryUsageDashboardQuery query) {
        List<BiDailyBatteryUsage> biPowerHubChargingList = this.list(new LambdaQueryWrapper<BiDailyBatteryUsage>()
            .eq(BiDailyBatteryUsage::getCompanyId, query.getCompanyId())
            .in(!CollectionUtils.isEmpty(query.getBatteryTypeList()),BiDailyBatteryUsage::getBatteryType, query.getBatteryTypeList())
            .ge(BiDailyBatteryUsage::getStrDate, query.getStartTime())
            .le(BiDailyBatteryUsage::getStrDate, query.getEndTime()));
        Map<String, List<BiDailyBatteryUsage>> dateBatteryUsageMap = biPowerHubChargingList
            .stream().collect(Collectors.groupingBy(BiDailyBatteryUsage::getStrDate));
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(query.getStartTime(), query.getEndTime());

        List<BatteryUsageDashboardVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            List<BiDailyBatteryUsage> biDailyBatteryUsages = dateBatteryUsageMap.get(date);
            BatteryUsageDashboardVo vo = new BatteryUsageDashboardVo()
                    .setDate(date)
                    .setIsFullyCalculated(true);
            if (!CollectionUtils.isEmpty(biDailyBatteryUsages)) {
                buildVo(query, biDailyBatteryUsages, vo);
            } else {
                // 实际查库的数量不等于查询的标签数
                vo.setIsFullyCalculated(false);
            }
            result.add(vo);
        }
        return result;
    }

    private static void buildVo(BatteryUsageDashboardQuery query, List<BiDailyBatteryUsage> biDailyBatteryUsages, BatteryUsageDashboardVo vo) {
        Integer dailyUsageTimes = 0;
        Integer dailyDeviceUsageCount = 0;
        Integer dailyUsageDuration = 0;
        for (BiDailyBatteryUsage biDailyBatteryUsage : biDailyBatteryUsages) {
            vo.setBatteryType(vo.getBatteryType() == null ? biDailyBatteryUsage.getBatteryType() :
                    (vo.getBatteryType() + "|" + biDailyBatteryUsage.getBatteryType()));
            if (BiDailyBatteryUsage.propertyNotNull(biDailyBatteryUsage)) {
                dailyUsageTimes += biDailyBatteryUsage.getDailyUsageTimes();
                dailyDeviceUsageCount += biDailyBatteryUsage.getDailyDeviceUsageCount();
                dailyUsageDuration += biDailyBatteryUsage.getDailyUsageDuration();
            } else {
                vo.setIsFullyCalculated(false);
            }
        }
        vo.setDailyUsageTimes(dailyUsageTimes);
        if (dailyDeviceUsageCount != 0) {
            vo.setDailyAverageUsageTime(new BigDecimal(dailyUsageDuration).divide(new BigDecimal(dailyDeviceUsageCount),0,RoundingMode.HALF_UP).intValue());
        }
        if (biDailyBatteryUsages.size() != query.getBatteryTypeList().size()) {
            // 实际查库的数量不等于查询的标签数
            vo.setIsFullyCalculated(false);
        }
    }
}