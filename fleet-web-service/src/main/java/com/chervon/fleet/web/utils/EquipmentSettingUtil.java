package com.chervon.fleet.web.utils;

import com.chervon.common.core.constant.StringPool;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.entity.dto.EquipmentSearchDto;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.vo.EquipmentVo;
import com.chervon.fleet.web.entity.vo.MapGatewayVo;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备赋值工具类
 * <AUTHOR> 2024/2/19
 */
public class EquipmentSettingUtil {
    /**
     * 设置设备位置
     * @param equipmentVo 设备返回对象
     * @param deviceStatusVo 设备状态对象
     */
    public static void setEquipmentLocation(EquipmentVo equipmentVo, DeviceStatusVo deviceStatusVo) {
        equipmentVo.setLocation(deviceStatusVo.getLocation());
        String coordinate = deviceStatusVo.getCoordinate();
        if (StringUtils.hasText(coordinate) && coordinate.contains(StringPool.COMMA)) {
            String[] split = coordinate.split(StringPool.COMMA);
            try {
                equipmentVo.setLatitude(new BigDecimal(split[0]));
                equipmentVo.setLongitude(new BigDecimal(split[1]));
            } catch (NumberFormatException formatException) {
                //坐标设置不合法
            } catch (Exception e) {
                //坐标设置不合法
            }
        }
    }

    public static void setGatewayLocation(EquipmentVo vo, String location, String coordinate) {
        vo.setLocation(location);
        if (StringUtils.hasText(coordinate)) {
            try {
                String[] split = coordinate.split(",");
                vo.setLatitude(new BigDecimal(split[0]));
                vo.setLongitude(new BigDecimal(split[1]));
            } catch (NullPointerException ignored) {
                //坐标初始值可能为空，忽略处理
            } catch (IllegalArgumentException ignored) {
                //坐标初始值可能为空，忽略处理
            } catch (Exception ignored) {

            }
        }
    }
    public static void setCenterCoordinate(List<MapGatewayVo> listVo, List<Map<String, BigDecimal>> points) {
        final Optional<MapGatewayVo> first = listVo.stream().filter(vo -> vo.getLatitude() != null && vo.getLongitude() != null
                && !vo.getLatitude().equals(BigDecimal.ZERO) && !vo.getLongitude().equals(BigDecimal.ZERO)).findFirst();
        if (first.isPresent()) {
            Map<String, BigDecimal> p = new ConcurrentHashMap<>();
            p.put("lat", first.get().getLatitude());
            p.put("lon", first.get().getLongitude());
            points.add(p);
        }
    }
    public static void setGatewayLocation(EquipmentVo vo, AppGateway gateway) {
        vo.setGatewayName(gateway.getName());
        vo.setLocation(gateway.getLocation());
        if(vo.getLatitude()==null || vo.getLatitude().equals(BigDecimal.ZERO)){
            try {
                String[] split = gateway.getCoordinate().split(",");
                vo.setLatitude(new BigDecimal(split[0]));
                vo.setLongitude(new BigDecimal(split[1]));
            }catch (NullPointerException ignored) {
                //坐标初始值可能为空，忽略处理
            } catch (IllegalArgumentException ignored) {
                //坐标初始值可能为空，忽略处理
            }
            catch (Exception ignored) {
                //坐标初始值可能为空，忽略处理
            }
        }
    }

    /**
     * 设置坐标
     * @param deviceStatusMap 设备状态map
     * @param e 租户设备
     * @param vo 设备vo
     */
    public static void setCoordinate(Map<String, DeviceStatusVo> deviceStatusMap, CompanyDevice e, EquipmentVo vo) {
        DeviceStatusVo status = deviceStatusMap.get(e.getDeviceId());
        if (status != null) {
            EquipmentSettingUtil.setGatewayLocation(vo, status.getLocation(), status.getCoordinate());
        }
    }

    public static void filterMaintenanceStatus(EquipmentSearchDto req, List<CompanyDevice> list) {
        if (!list.isEmpty() && !CollectionUtils.isEmpty(req.getMaintenanceStatus())) {
            list.removeIf(e -> e.getMaintenanceStatus() == null
                    || e.getFirstCategoryCode().equals(FleetFirstCategoryEnum.CHARGER.getCategoryCode())
                    ||  !req.getMaintenanceStatus().contains(e.getMaintenanceStatus()));
        }
    }

    public static void filterOnlineStatus(EquipmentSearchDto req, List<CompanyDevice> list) {
        if (!list.isEmpty() && !CollectionUtils.isEmpty(req.getOnlineStatus())) {
            list.removeIf(e -> e.getOnlineStatus() == null || !req.getOnlineStatus().contains(e.getOnlineStatus()));
        }
    }

    public static void filterGateway(EquipmentSearchDto req, List<CompanyDevice> list) {
        if (!list.isEmpty() && StringUtils.hasText(req.getGatewayId())) {
            list.removeIf(e -> !Objects.isNull(req.getGatewayId()) &&  !req.getGatewayId().equals(e.getGatewayId()));
        }
    }

    public static void filterInventoryStatus(EquipmentSearchDto req, List<CompanyDevice> list) {
        if (!list.isEmpty() && !CollectionUtils.isEmpty(req.getInventoryStatus())) {
            list.removeIf(e -> e.getWarehouseStatus() == null || !req.getInventoryStatus().contains(e.getWarehouseStatus()));
        }
    }

    /**
     * 是否有过滤条件生效
     * @param req
     * @return
     */
    public static boolean isFilter(EquipmentSearchDto req) {
        if (StringUtils.hasText(req.getDeviceName())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(req.getCategoryCodes())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(req.getInventoryStatus())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(req.getOnlineStatus())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(req.getMaintenanceStatus())) {
            return true;
        }
        if (!CollectionUtils.isEmpty(req.getTagIds())) {
            return true;
        }
        return StringUtils.hasText(req.getGatewayId());
    }

    @NotNull
    public static MapGatewayVo getMapGatewayVo(EquipmentVo e, Map<String, AppGateway> gatewayAllMap) {
        MapGatewayVo gatewayVo = new MapGatewayVo();
        gatewayVo.setDeviceId(e.getDeviceId());
        gatewayVo.setGatewayId(e.getGatewayId());
        gatewayVo.setLocation(e.getLocation());
        gatewayVo.setLatitude(e.getLatitude());
        gatewayVo.setLongitude(e.getLongitude());
        final AppGateway appGateway = gatewayAllMap.get(e.getGatewayId());
        gatewayVo.setName(appGateway == null ? "" : appGateway.getName());
        return gatewayVo;
    }
}
