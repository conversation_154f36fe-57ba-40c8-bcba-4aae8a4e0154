package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.po.DailyBatteryUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsBatteryEnergyVo;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsBatteryTimeVo;
import com.chervon.fleet.web.mapper.DailyBatteryUsageMapper;
import com.chervon.fleet.web.service.DailyBatteryUsageService;
import com.chervon.iot.middle.api.vo.usage.FleetBatteryDailyUsageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.chervon.fleet.web.utils.ParseUtil.getDiffValue;
import static com.chervon.fleet.web.utils.ParseUtil.parseTup2;

/**
 * <AUTHOR>
 * @since 2023-08-15 17:29
 **/
@Service
@Slf4j
public class DailyBatteryUsageServiceImpl extends ServiceImpl<DailyBatteryUsageMapper, DailyBatteryUsage>
implements DailyBatteryUsageService {
    /**
     * 电量统计
     * @param req 请求对象
     * @return
     */
    @Override
    public List<EquipmentStatisticsBatteryEnergyVo> detailStatisticsBatteryEnergy(StatisticsQueryDto req) {
        Map<String, DailyBatteryUsage> dateBatteryUsageMap = getStringDailyBatteryUsageMap(req);
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(req.getStart(), req.getEnd());

        List<EquipmentStatisticsBatteryEnergyVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            DailyBatteryUsage dailyBatteryUsage = dateBatteryUsageMap.get(date);
            EquipmentStatisticsBatteryEnergyVo vo = new EquipmentStatisticsBatteryEnergyVo();
            vo.setDate(date);
            if (null != dailyBatteryUsage) {
                vo.setChargingEnergy(dailyBatteryUsage.getChargingEnergy());
                vo.setDischargingEnergy(dailyBatteryUsage.getDischargingEnergy());
            }
            result.add(vo);
        }
        return result;
    }
    /**
     * 详细的电量统计
     * @param req 请求对象
     * @return
     */
    @Override
    public List<EquipmentStatisticsBatteryTimeVo> detailStatisticsBatteryTime(StatisticsQueryDto req) {
        Map<String, DailyBatteryUsage> dateBatteryUsageMap = getStringDailyBatteryUsageMap(req);
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(req.getStart(), req.getEnd());

        List<EquipmentStatisticsBatteryTimeVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            DailyBatteryUsage dailyBatteryUsage = dateBatteryUsageMap.get(date);
            EquipmentStatisticsBatteryTimeVo vo = new EquipmentStatisticsBatteryTimeVo();
            vo.setDate(date);
            if (null != dailyBatteryUsage) {
                vo.setChargingTime(dailyBatteryUsage.getChargingTime()/ NumberConst.ONE_MINUTE_SECOND);
                vo.setDischargingTime(dailyBatteryUsage.getDischargingTime()/NumberConst.ONE_MINUTE_SECOND);
            }
            result.add(vo);
        }
        return result;
    }
    /**
     * 查询DailyBatteryUsage并映射为日期的Map
     *
     * @param req 请求体
     * @return key-日期字符串 value-PO
     */
    @Override
    public Map<String, DailyBatteryUsage> getStringDailyBatteryUsageMap(StatisticsQueryDto req) {
        List<DailyBatteryUsage> dailyBatteryUsageList = list(new LambdaQueryWrapper<DailyBatteryUsage>()
                .eq(DailyBatteryUsage::getDeviceId, req.getDeviceId())
                .eq(DailyBatteryUsage::getStatus,0)
                .eq(DailyBatteryUsage::getCompanyId, UserContext.getCompanyId())
                .ge(DailyBatteryUsage::getDate, req.getStart())
                .le(DailyBatteryUsage::getDate, req.getEnd())
                .orderByAsc(DailyBatteryUsage::getDate));
        return dailyBatteryUsageList
                .stream().collect(Collectors.toMap(DailyBatteryUsage::getDate, a -> a));
    }

    /**
     * * 批量保存入库
     * @param listBatteryDailyUsageVo
     */
    public void saveDataDailyBatteryUsage(List<FleetBatteryDailyUsageVo> listBatteryDailyUsageVo) {
        if(CollectionUtils.isEmpty(listBatteryDailyUsageVo)){
            return;
        }
        List<DailyBatteryUsage> list=new ArrayList<>();
        for(FleetBatteryDailyUsageVo vo:listBatteryDailyUsageVo) {
            DailyBatteryUsage dataDailyBatteryUsage=new DailyBatteryUsage();
            dataDailyBatteryUsage.setCompanyId(vo.getCompanyId());
            dataDailyBatteryUsage.setCategoryCode(vo.getFirstCategoryCode());
            dataDailyBatteryUsage.setSecondCategoryCode(vo.getSecondCategoryCode());
            dataDailyBatteryUsage.setDeviceId(vo.getDeviceId());
            dataDailyBatteryUsage.setCreateTime(System.currentTimeMillis());
            dataDailyBatteryUsage.setModifyTime(System.currentTimeMillis());
            dataDailyBatteryUsage.setDate(vo.getDate());
            dataDailyBatteryUsage.setChargingTime(getDiffValue(vo.getMin2026(),vo.getMax2026()));
            Integer disChargingTime=getDiffValue(vo.getMin2031(),vo.getMax2031());
            dataDailyBatteryUsage.setDischargingTime(disChargingTime);
            dataDailyBatteryUsage.setDailyNumberTimes(disChargingTime>=120?1:0);
            dataDailyBatteryUsage.setChargingEnergy(getDiffValue(vo.getMin2027(),vo.getMax2027()));
            dataDailyBatteryUsage.setDischargingEnergy(getDiffValue(vo.getMin2035(),vo.getMax2035()));
            dataDailyBatteryUsage.setDailyUsageTime(disChargingTime);

            dataDailyBatteryUsage.setSpanChargingTime(parseTup2(vo.getMin2026(),vo.getMax2026()));
            dataDailyBatteryUsage.setSpanDischargingTime(parseTup2(vo.getMin2031(),vo.getMax2031()));
            dataDailyBatteryUsage.setSpanChargingEnergy(parseTup2(vo.getMin2027(),vo.getMax2027()));
            dataDailyBatteryUsage.setSpanDischargingEnergy(parseTup2(vo.getMin2035(),vo.getMax2035()));

            dataDailyBatteryUsage.setStatus(0);
            dataDailyBatteryUsage.setId();

            list.add(dataDailyBatteryUsage);
        }

        if(!CollectionUtils.isEmpty(list)){
            saveOrUpdateBatch(list);
        }
    }
}
