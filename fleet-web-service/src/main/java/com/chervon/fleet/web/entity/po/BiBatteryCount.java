package com.chervon.fleet.web.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 电池数量统计（看板：total batteries饼状图）(t_bi_battery_count)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 13:57:59
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_battery_count")
public class BiBatteryCount extends Model<BiBatteryCount> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 电池类型名称
     */
    private String batteryType;
    /**
     * 电池总数
     */
    private Integer batteryCount;
    /**
     * 总安时
     */
    private BigDecimal totalAh;
    /**
     * 更新时间
     */
    private Date modifyTime;

}