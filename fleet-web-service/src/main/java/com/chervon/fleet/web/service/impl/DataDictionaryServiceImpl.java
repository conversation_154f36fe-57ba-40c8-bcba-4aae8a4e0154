package com.chervon.fleet.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.chervon.fleet.web.api.entity.vo.DataDictionaryProductVo;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.po.DataDictionary;
import com.chervon.fleet.web.mapper.DataDictionaryMapper;
import com.chervon.fleet.web.service.DataDictionaryService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataDictionaryServiceImpl extends ServiceImpl<DataDictionaryMapper, DataDictionary> implements DataDictionaryService {

    private static final String DATA_DICTIONARY_GROUP = "DICTIONARY:DATA_DICTIONARY_GROUP_";

    private static final String CATEGORY = "category";
    private static final String PRODUCTSTR = "_2";
    private static final String GROUPNAME = "category-productCategory";

    /**
     * 子订分组
     *
     * @param groupName
     * @return
     */
    private static String getKey(String groupName) {
        return DATA_DICTIONARY_GROUP + groupName;
    }

    @Override
    public List<DataDictionary> getDictionaryByGroup(String groupName) {
        return getDictionaryByGroup(groupName,false);
    }

    @Override
    public List<DataDictionary> getDictionaryByGroup(String groupName, Boolean needDisable) {
        if (Objects.isNull(groupName) || groupName.isEmpty()) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED, "groupName");
        }
        List<DataDictionary> list = new ArrayList<>();
        String key = getKey(groupName);
        String json = RedisUtils.getCacheObject(key);
        if (!StringUtils.isEmpty(json)) {
            list.addAll(JsonUtils.toObject(json, new TypeReference<List<DataDictionary>>() {}));
        }else {
            LambdaQueryWrapper<DataDictionary> lambda = new QueryWrapper<DataDictionary>().lambda();
            lambda.eq(DataDictionary::getDataGroup, groupName)
                    .select(DataDictionary::getId, DataDictionary::getParentId, DataDictionary::getDataName
                            , DataDictionary::getDataValue, DataDictionary::getSortId,DataDictionary::getEnable)
                    .orderByAsc(DataDictionary::getParentId).orderByAsc(DataDictionary::getSortId);
            list.addAll(list(lambda));
        }
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        RedisUtils.setWithExpire(key, JsonUtils.toJson(list), RedisConst.DATA_DICTIONARY_EXPIRE_SECOND);
        //增加启动状态，默认只查询启用的
        if (!needDisable){
            return list.stream().filter(a->(Objects.isNull(a.getEnable()) ? Boolean.TRUE:a.getEnable()).equals(Boolean.TRUE)).collect(Collectors.toList());
        }

        return list;
    }

    @Override
    public List<DataDictionaryProductVo> getProductCategoryDictionary() {
        List<DataDictionaryProductVo> list = new ArrayList<>();
        String key = getKey(GROUPNAME);
        String json = RedisUtils.getCacheObject(key);
        if (!StringUtils.isEmpty((json))) {
            list.addAll(JsonUtils.toObject(json, new TypeReference<List<DataDictionaryProductVo>>() {}));
        }else {
            list = getDataListByDataGroup(CATEGORY);
            if (CollUtil.isEmpty(list)) {
                return new ArrayList<>();
            }
            for (DataDictionaryProductVo dataDictionaryProductVo : list) {
                String newDataGroup = dataDictionaryProductVo.getDataValue() + PRODUCTSTR;
                List<DataDictionaryProductVo> tempList = getDataListByDataGroup(newDataGroup);
                if (CollUtil.isNotEmpty(tempList)) {
                    List<DataDictionaryProductVo> dataDictionaryProductVos = BeanCopyUtils.copyList(tempList,DataDictionaryProductVo.class);
                    dataDictionaryProductVo.setProductCategoryVos(dataDictionaryProductVos);
                }
            }
        }
        RedisUtils.setWithExpire(key, JsonUtils.toJson(list), RedisConst.DATA_DICTIONARY_EXPIRE_SECOND);
        
        return list;
    }
    private List<DataDictionaryProductVo> getDataListByDataGroup(String dataGroup) {
        LambdaQueryWrapper<DataDictionary> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DataDictionary::getDataGroup, dataGroup)
                .select(DataDictionary::getId, DataDictionary::getParentId, DataDictionary::getDataName
                        , DataDictionary::getDataValue, DataDictionary::getSortId,DataDictionary::getEnable)
                .orderByAsc(DataDictionary::getParentId).orderByAsc(DataDictionary::getSortId);
        List<DataDictionary> dataDictionaryList = this.list(lambdaQueryWrapper);
        List<DataDictionaryProductVo> list = BeanCopyUtils.copyList(dataDictionaryList,DataDictionaryProductVo.class);
        return list;
    }

    @Override
    public List<DataDictionary> getDictionaryByValues(List<String> values) {
        if (Objects.isNull(values) || values.isEmpty()) {
            return new ArrayList<>(0);
        }

        List<DataDictionary> list = list( new LambdaQueryWrapper<DataDictionary>().in(DataDictionary::getDataValue, values));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>(0);
        }
        return list;
    }

    @Override
    public boolean setValueByKey(String key,String value) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        LambdaUpdateWrapper<DataDictionary> updateWrapper= new LambdaUpdateWrapper<DataDictionary>()
                .eq(DataDictionary::getDataName,key).set(DataDictionary::getDataValue,value);
        return update(updateWrapper);
    }

    @Override
    public Map<Object, Object> getDictionaryMap(String groupName) {
        List<DataDictionary> dictionaryByGroup = getDictionaryByGroup(groupName);
        return dictionaryByGroup.stream().collect(
                Collectors.toMap(DataDictionary::getDataName, DataDictionary::getDataValue));
    }

    @Override
    public String getDictionaryByDataName(String groupName, String dataName) {
        if (StringUtils.isEmpty(groupName) || StringUtils.isEmpty(dataName)) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED);
        }

        List<DataDictionary> dataDictionaryList = this.getDictionaryByGroup(groupName);
        if (CollectionUtils.isEmpty(dataDictionaryList)) {
            throw new ServiceException(ErrorCode.PARAMETER_ERROR, groupName);
        }

        Optional<DataDictionary> dd = dataDictionaryList.stream().filter(dataDictionary -> dataDictionary.getDataName().equals(dataName)).findFirst();
        return dd.map(DataDictionary::getDataValue).orElse(null);
    }

    @Override
    public String getDictionaryByDataValue(String groupName, String dataValue) {
        if (StringUtils.isEmpty(groupName) || StringUtils.isEmpty(dataValue)) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED);
        }

        List<DataDictionary> dataDictionaryList = this.getDictionaryByGroup(groupName);
        if (CollectionUtils.isEmpty(dataDictionaryList)) {
            throw new ServiceException(ErrorCode.PARAMETER_ERROR, groupName);
        }

        Optional<DataDictionary> dd = dataDictionaryList.stream().filter(dataDictionary -> dataDictionary.getDataValue().equals(dataValue)).findFirst();
        return dd.map(DataDictionary::getDataValue).orElse(null);
    }

    @Override
    public String getValueByKeyNoCache(String dataName) {
        if (Objects.isNull(dataName) || dataName.isEmpty()) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED);
        }
        LambdaQueryWrapper<DataDictionary> lambda = new QueryWrapper<DataDictionary>().lambda();
        lambda.eq(DataDictionary::getDataName, dataName)
                .select(DataDictionary::getId, DataDictionary::getDataName
                        , DataDictionary::getDataValue, DataDictionary::getSortId);
        DataDictionary dictionary = getOne(lambda);
        String value = "";
        if (dictionary != null) {
            value = dictionary.getDataValue();
        }
        return value;
    }

    @Override
    public List<DataDictionary> getDictionaryByName(String groupName, String name) {
        List<DataDictionary> list = getDictionaryByGroup(groupName);
        if (CollectionUtils.isEmpty(list)) {
             return null;
        }
        if (StringUtils.isEmpty(name)) {
            return list;
        }
        //模糊查询特殊符号处理
        String nameNew = StringUtils.symbolFormat(name);
        return list.stream().filter(g -> g.getDataName().contains(nameNew)).collect(Collectors.toList());
    }

}
