package com.chervon.fleet.web.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.fleet.web.api.entity.enums.MaintenancePlanTypeEnum;
import com.chervon.fleet.web.api.entity.enums.MaintenanceStatusEnum;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.MaintenancePlan;
import com.chervon.fleet.web.entity.po.ShadowStatus;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.MaintenancePlanService;
import com.chervon.fleet.web.service.ShadowStatusService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 维保状态刷新JOB任务
 * <AUTHOR>
 * @date 2023/2/25 20:08
 */
@Component
@Slf4j
@AllArgsConstructor
public class MaintenanceJob {

    private MaintenancePlanService maintenancePlanService;

    private ShadowStatusService shadowStatusService;

    private final CompanyDeviceService companyDeviceService;

    /**
     * 维保状态过期刷新JOB任务
     */
    @XxlJob("maintenanceStatusJob")
    public void maintenanceStatusJob() {
        // 扫描维保计划表
        List<MaintenancePlan> listPlanAll = maintenancePlanService.list(new LambdaQueryWrapper<MaintenancePlan>().ne(MaintenancePlan::getPlanType, MaintenancePlanTypeEnum.OFF.getType()));
        if (listPlanAll.isEmpty()) {
            return;
        }
        List<String> powerDeviceIds = listPlanAll.stream().filter(e -> e.getPlanType() == MaintenancePlanTypeEnum.ENGINE_HOURS.getType()).map(MaintenancePlan::getDeviceId).collect(Collectors.toList());
        List<ShadowStatus> shadowStatuses = new ArrayList<>();
        if (!powerDeviceIds.isEmpty()) {
            shadowStatuses = shadowStatusService.listByDeviceIds(powerDeviceIds,null);
        }
        Map<String, Integer> shadowStatusMap = shadowStatuses.stream().collect(HashMap::new, (map, e)->map.put(e.getDeviceId(), e.getTotalUsageTime()), HashMap::putAll);
        for (MaintenancePlan plan : listPlanAll) {
            LambdaUpdateWrapper<CompanyDevice> updateWrapper = new LambdaUpdateWrapper<CompanyDevice>()
                    .eq(CompanyDevice::getCompanyId, plan.getCompanyId())
                    .eq(CompanyDevice::getDeviceId, plan.getDeviceId());
            if (plan.getPlanType() == MaintenancePlanTypeEnum.DATE.getType()) {
                if (System.currentTimeMillis() > plan.getDeadlineTime()) {
                    updateWrapper.set(CompanyDevice::getMaintenanceStatus, MaintenanceStatusEnum.DUE.getType());
                    updateWrapper.set(CompanyDevice::getModifier, StringConst._EVENT_MAINTENANCE+"job_date");
                    companyDeviceService.update(updateWrapper);
                    continue;
                }
            } else {
                if (workHoursUpdate(plan, shadowStatusMap, updateWrapper)) {
                    continue;
                }
            }
            updateWrapper.ne(CompanyDevice::getMaintenanceStatus, MaintenanceStatusEnum.ON.getType());
            updateWrapper.set(CompanyDevice::getMaintenanceStatus, MaintenanceStatusEnum.ON.getType());
            updateWrapper.set(CompanyDevice::getModifier, "job-maintenance");
            companyDeviceService.update(updateWrapper);
        }
    }

    private boolean workHoursUpdate(MaintenancePlan plan, Map<String, Integer> shadowStatusMap, LambdaUpdateWrapper<CompanyDevice> updateWrapper) {
        Integer usageTime = shadowStatusMap.get(plan.getDeviceId());
        if (usageTime == null) {
            usageTime = 0;
        }
        Integer workHoursUsed = plan.getWorkHoursUsed();
        if (workHoursUsed == null) {
            workHoursUsed = 0;
        }
        Integer usageHour = plan.getUsageHour();
        if (usageHour == null) {
            usageHour = 0;
        }
        int hour = (usageTime - workHoursUsed) / NumberConst.ONE_HOUR_SECOND;
        if (hour > usageHour) {
            updateWrapper.set(CompanyDevice::getMaintenanceStatus, MaintenanceStatusEnum.DUE.getType());
            updateWrapper.set(CompanyDevice::getModifier, StringConst._EVENT_MAINTENANCE+"job_hour");
            companyDeviceService.update(updateWrapper);
            return true;
        }
        return false;
    }

}