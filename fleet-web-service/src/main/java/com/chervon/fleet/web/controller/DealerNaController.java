package com.chervon.fleet.web.controller;

import com.chervon.fleet.web.service.DealerNaService;
import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerNaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "经销商管理（北美）")
@RestController
@Slf4j
@RequestMapping("dealer/na")
public class DealerNaController {

    private final DealerNaService dealerNaService;

    public DealerNaController(DealerNaService dealerNaService) {
        this.dealerNaService = dealerNaService;
    }

    @ApiOperation(value = "搜索距离范围内的经销商")
    @PostMapping(value = "search")
    public List<AppDealerNaVo> search(@RequestBody AppDealerSearchDto req) {
        return dealerNaService.search(req == null ? new AppDealerSearchDto() : req);
    }

    @ApiOperation(value = "经销商详情")
    @GetMapping(value = "detail")
    public AppDealerNaVo detail(@RequestParam("dealerId") Long dealerId) {
        return dealerNaService.detail(dealerId);
    }

    @ApiOperation(value = "我收藏的经销商-总数")
    @PostMapping(value = "favorite/count")
    public Integer favoriteCount(@RequestBody AppDealerSearchDto req) {
        return dealerNaService.favoriteCount(req);
    }

    @ApiOperation(value = "我收藏的经销商列表")
    @PostMapping(value = "favorite")
    public List<AppDealerNaVo> favorite(@RequestBody AppDealerSearchDto req) {
        return dealerNaService.favorite(req == null ? new AppDealerSearchDto() : req);
    }

    @ApiOperation(value = "添加收藏")
    @GetMapping(value = "addFavorite")
    public void addFavorite(@RequestParam("dealerId") Long dealerId) {
        dealerNaService.addFavorite(dealerId);
    }

    @ApiOperation(value = "取消收藏")
    @GetMapping(value = "cancelFavorite")
    public void cancelFavorite(@RequestParam("dealerId") Long dealerId) {
        dealerNaService.cancelFavorite(dealerId);
    }

}
