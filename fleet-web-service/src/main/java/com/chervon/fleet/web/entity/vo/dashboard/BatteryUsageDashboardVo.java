package com.chervon.fleet.web.entity.vo.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 电池使用情况看板Vo
 * <AUTHOR>
 * @since 2023-07-31 16:24
 **/
@Data
@Accessors(chain = true)
@ApiModel("电池使用情况看板Vo")
public class BatteryUsageDashboardVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 每日使用总次数
     */
    public BatteryUsageDashboardVo() {
        this.dailyUsageTimes = 0;
        this.dailyAverageUsageTime=0;
    }

    /**
     * 电池分类:如果复选用管道符|分割
     */
    @ApiModelProperty("电池分类:如果复选用管道符|分割")
    private String batteryType;
    /**
     * 日期
     */
    @ApiModelProperty("日期,yyyy-MM-dd字符串")
    private String date;
    /**
     * Daily Average Usage Time时长=设备日总使用时长÷设备数量，统计单位为分钟
     */
    @ApiModelProperty("Daily Average Usage Time时长=设备日总使用时长÷设备数量，统计单位为分钟")
    private Integer dailyAverageUsageTime=0;

    @ApiModelProperty("每日使用总次数")
    private Integer dailyUsageTimes=0;

    @ApiModelProperty("复选的数据是否全部有效,如果选中的某个标签当前为空值,则为false")
    private Boolean isFullyCalculated;
}
