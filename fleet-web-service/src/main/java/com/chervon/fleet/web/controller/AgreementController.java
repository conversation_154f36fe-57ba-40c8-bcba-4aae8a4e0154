package com.chervon.fleet.web.controller;

import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.enums.AgreementTypeEnum;
import com.chervon.fleet.user.api.entity.vo.AppSettingVo;
import com.chervon.fleet.user.api.service.RemoteAppSettingService;
import com.chervon.fleet.user.api.service.RemoteFleetUserCenterService;
import com.chervon.operation.api.RemoteAppAgreementService;
import com.chervon.operation.api.vo.AppAgreementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户协议隐私政策相关接口
 *
 * <AUTHOR>
 * @date 2023/6/27 11:23
 */
@Api(tags = "web协议")
@RestController
@Slf4j
@RequestMapping("/agreement")
public class AgreementController {
    /**
     * FLEET用户类型
     */
    private static final Integer FLEET_USER_TYPE=3;
    @DubboReference
    private RemoteAppAgreementService remoteAppAgreementService;

    @DubboReference
    private RemoteFleetUserCenterService remoteFleetUserCenterService;

    @DubboReference
    private RemoteAppSettingService remoteAppSettingService;

    @ApiOperation("查询最新版本的web用户协议，不需要token")
    @GetMapping("latest/user")
    public AppAgreementVo latestUser() {
        return remoteAppAgreementService.latest(FLEET_USER_TYPE, AgreementTypeEnum.USER_NOTICE.getDesc());
    }

    @ApiOperation("查询最新版本的web用户隐私协议，不需要token")
    @GetMapping("latest/secret")
    public AppAgreementVo latestSecret() {
        return remoteAppAgreementService.latest(FLEET_USER_TYPE, AgreementTypeEnum.PRIVACY_POLICY.getDesc());
    }

    @ApiOperation("查询我同意的最新版本的web用户协议")
    @GetMapping("agreed/user")
    public AppAgreementVo agreedUser() {
        AppSettingVo userSetting = remoteAppSettingService.getUserSetting(UserContext.getClientInfo().getUserId());
        if (userSetting == null || StringUtils.isBlank(userSetting.getWebUserAgreementVersion())) {
            return null;
        }
        return remoteAppAgreementService.agreed(FLEET_USER_TYPE, userSetting.getWebUserAgreementVersion(), AgreementTypeEnum.USER_NOTICE.getDesc());
    }

    @ApiOperation("查询我同意最新版本的web用户隐私协议")
    @GetMapping("agreed/secret")
    public AppAgreementVo agreedSecret() {
        AppSettingVo userSetting = remoteAppSettingService.getUserSetting(UserContext.getClientInfo().getUserId());
        if (userSetting == null || StringUtils.isBlank(userSetting.getWebSecretAgreementVersion())) {
            return null;
        }
        return remoteAppAgreementService.agreed(FLEET_USER_TYPE, userSetting.getWebSecretAgreementVersion(), AgreementTypeEnum.PRIVACY_POLICY.getDesc());
    }

    @ApiOperation("撤销协议授权")
    @GetMapping("withdraw")
    public void withdraw() {
        remoteFleetUserCenterService.userLogoff(UserContext.getClientInfo().getUserId());
    }

}
