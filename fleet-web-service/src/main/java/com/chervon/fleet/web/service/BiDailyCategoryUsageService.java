package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.dashboard.DailyCategoryUsageDashboardQuery;
import com.chervon.fleet.web.entity.po.BiDailyCategoryUsage;
import com.chervon.fleet.web.entity.vo.dashboard.DailyCategoryUsageDashboardVo;

import java.util.List;

/**
 * 设备日使用量统计表（看板：Usage Statistics）服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
public interface BiDailyCategoryUsageService extends IService<BiDailyCategoryUsage> {

    /**
     * 设备分类日使用量
     *
     * @param query 查询条件
     * @return 结果
     */
    List<DailyCategoryUsageDashboardVo> dailyCategoryUsage(DailyCategoryUsageDashboardQuery query);
}
