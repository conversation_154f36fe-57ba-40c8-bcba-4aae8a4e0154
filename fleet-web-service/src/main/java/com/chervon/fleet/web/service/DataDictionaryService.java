package com.chervon.fleet.web.service;


import com.chervon.fleet.web.api.entity.vo.DataDictionaryProductVo;
import com.chervon.fleet.web.entity.po.DataDictionary;

import java.util.List;
import java.util.Map;

public interface DataDictionaryService {

    String getDictionaryByDataName(String groupName, String dataName);

    String getDictionaryByDataValue(String groupName, String dataValue);

    String getValueByKeyNoCache(String dataName);

    List<DataDictionary> getDictionaryByGroup(String groupName);

    List<DataDictionary> getDictionaryByGroup(String groupName, Boolean needDisable);

    List<DataDictionaryProductVo> getProductCategoryDictionary();

    List<DataDictionary> getDictionaryByValues(List<String> values);

    Map<Object, Object> getDictionaryMap(String groupName);

    List<DataDictionary> getDictionaryByName(String groupName, String name);

    boolean setValueByKey(String key,String value);
}
