package com.chervon.fleet.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CompanyDeviceMapper extends BaseMapper<CompanyDevice> {

    /**
     * 查询最大序号的租户设备信息
     *
     * @param companyId   租户id
     * @param cDeviceName 模糊匹配的设备名称
     * @return 租户设备信息
     */
    List<CompanyDevice> selectMaxOrder(@Param("companyId") Long companyId, @Param("cDeviceName") String cDeviceName);

    /**
     * 根据租户id查询绑定设备列表，包含已解绑的设备
     *
     * @param companyId 租户id
     * @return 设备列表
     */
    List<CompanyDevice> selectListWithDeletedByCompanyId(@Param("companyId") Long companyId);

    /**
     * 根据条件查询设备列表
     * @param companyId
     * @return
     */
    List<CompanyDevice> selectDeletedList(@Param("companyId") Long companyId,
                                          @Param("secondCategoryCodes") List<String> secondCategoryCodes,
                                          @Param("tagDeviceIds") List<String> tagDeviceIds,
                                          @Param("excludeDeviceIds") List<String> excludeDeviceIds);

    Integer getDeviceCount(@Param("companyId") Long companyId,@Param("firstCategoryCode") String firstCategoryCode);

    List<Long> getCompanyIdList();
}
