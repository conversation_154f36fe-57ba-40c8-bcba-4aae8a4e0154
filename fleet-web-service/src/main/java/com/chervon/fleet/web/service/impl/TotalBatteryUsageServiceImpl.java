package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.po.TotalBatteryUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsBatteryVo;
import com.chervon.fleet.web.mapper.TotalBatteryUsageMapper;
import com.chervon.fleet.web.service.TotalBatteryUsageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-08-15 18:15
 **/
@Service
@Slf4j
public class TotalBatteryUsageServiceImpl extends ServiceImpl<TotalBatteryUsageMapper, TotalBatteryUsage>
    implements TotalBatteryUsageService {
    private static final String SQL_LIMIT = "limit 1";
    /**
     * 详情电量统计
     * @param deviceId 设备id
     * @return
     */
    @Override
    public EquipmentStatisticsBatteryVo detailStatisticsBattery(String deviceId) {
        TotalBatteryUsage totalBatteryUsage = getOne(new LambdaQueryWrapper<TotalBatteryUsage>()
                .eq(TotalBatteryUsage::getDeviceId, deviceId)
                .last(SQL_LIMIT));
        if (null == totalBatteryUsage) {
            return null;
        }
        EquipmentStatisticsBatteryVo result = new EquipmentStatisticsBatteryVo();
        result.setTotalChargingTime(totalBatteryUsage.getTotalChargingTime()/ NumberConst.ONE_MINUTE_SECOND);
        result.setTotalNumberOfChargingCycles(totalBatteryUsage.getChargingCyclesNumber());
        result.setTotalDischargingTime(totalBatteryUsage.getTotalDischargingTime()/NumberConst.ONE_MINUTE_SECOND);
        return result;
    }
}
