package com.chervon.fleet.web.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * fleet网关上报扫描到的设备缓存结构信息
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@Accessors(chain = true)
@ApiModel("fleet网关维度的设备状态信息")
@NoArgsConstructor
public class WarehouseStatusCache implements Serializable {
    private static final long serialVersionUID = 1L;
    public WarehouseStatusCache(String deviceId) {
        this.deviceId = deviceId;
    }

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty("租户Id")
    private Long companyId;
    /**
     * 设备库存状态：0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location
     * @see com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum
     */
    @ApiModelProperty(value = "设备库存状态：0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location")
    private Integer warehouseStatus;

    @ApiModelProperty(value = "蓝牙信号强度:大约0~-96dBm")
    private Integer rssi;

    @ApiModelProperty("移动网关的坐标，逗号分隔：51.175330,-4.044769")
    private String coordinate;

    @ApiModelProperty("网关地址信息")
    private String location;

    @ApiModelProperty("设备上报的网关Id")
    private String gatewayId;

    @ApiModelProperty("最后上报时间戳")
    private Long reportTime;
}
