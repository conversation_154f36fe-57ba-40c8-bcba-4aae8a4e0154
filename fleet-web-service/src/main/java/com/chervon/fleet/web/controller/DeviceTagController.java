package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.ListInfoReq;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import com.chervon.fleet.web.api.entity.vo.GroupVo;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.*;
import com.chervon.fleet.web.entity.vo.MaintenanceLogVo;
import com.chervon.fleet.web.entity.vo.MaintenanceVo;
import com.chervon.fleet.web.entity.vo.TagSettingTagListVo;
import com.chervon.fleet.web.service.DeviceTagService;
import com.chervon.fleet.web.service.MaintenanceLogService;
import com.chervon.fleet.web.service.MaintenancePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备Tag相关接口
 */
@Api(tags = "设备Tag相关接口")
@RestController
@Slf4j
@RequestMapping("/equipment")
@AllArgsConstructor
public class DeviceTagController {

    private final DeviceTagService deviceTagService;

    /**
     * 根据设备id，查询标签数据
     * @param deviceId
     * @return
     */
    @ApiOperation("根据设备id，查询标签数据")
    @GetMapping("tag/list")
    @WebApiHeaders
    public List<TagVo> tagList(@RequestParam("deviceId") String deviceId) {
        return deviceTagService.tagList(deviceId);
    }

    /**
     * 选中设备-打标签--分组数据查询，支持多个设备id，单个设备也适用
     * @param req
     * @return
     */
    @ApiOperation("选中设备-打标签--分组数据查询，支持多个设备id，单个设备也适用")
    @PostMapping("tagSetting/group/list")
    @WebApiHeaders
    public List<GroupVo> tagSettingGroupList(@RequestBody ListInfoReq<String> req) {
        return deviceTagService.tagSettingGroupList(req.getInfo());
    }

    @ApiOperation("选中设备-打标签--标签数据查询")
    @PostMapping("tagSetting/tag/list")
    @WebApiHeaders
    public TagSettingTagListVo tagSettingTagList(@RequestBody TagSettingTagListDto req) {
        return deviceTagService.tagSettingTagList(req);
    }

    @ApiOperation("选中设备-打标签操作")
    @PostMapping("tagSetting")
    @WebApiHeaders
    public void tagSetting(@RequestBody TagSettingDto req) {
        deviceTagService.tagSetting(req);
    }

}
