package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备对象
 * <AUTHOR>
 * @date 2023/7/12 14:06
 */
@Data
@ApiModel(description = "设备对象")
public class EquipmentVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("产品图片url")
    private String productIconUrl;

    @ApiModelProperty("使用时长，单位秒")
    private Integer usageDuration;
    /**
     * 设备总使用次数（不受绑定解绑影响
     */
    @ApiModelProperty("设备总使用次数")
    private Integer totalUsageNumber;

    @ApiModelProperty("库存状态 0 Never Seen 1 In Warehouse 2 Out for Work 3 Unknown location")
    private Integer inventoryStatus;

    @ApiModelProperty("在线状态 0 offline 1 online")
    private Integer onlineStatus;

    @ApiModelProperty("维保状态 1 due 2 on 3 off")
    private Integer maintenanceStatus;

    @ApiModelProperty("故障状态 0 无故障 1 有故障")
    private Integer errorStatus;

    @ApiModelProperty("有故障：故障code")
    private String errorCode;

    @ApiModelProperty("有故障：故障message")
    private String errorMessage;
    /**
     * 处理建议内容
     */
    @ApiModelProperty("处理建议内容")
    private String suggestionContent;

    @ApiModelProperty("网关id")
    private String gatewayId;

    private boolean hardGateway;

    @ApiModelProperty("网关名称")
    private String gatewayName;

    @ApiModelProperty("网关地址")
    private String location;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("产品id")
    private Long productId;

    @ApiModelProperty("产品型号")
    private String productModel;

    @ApiModelProperty("商品型号")
    private String commodityModel;

    @ApiModelProperty("二级品类编码")
    private String categoryCode;

    @ApiModelProperty("二级品类名称")
    private String categoryName;

    @ApiModelProperty("设备sn")
    private String deviceSn;

    @ApiModelProperty("设备最后一次被上报时间")
    private Long lastSyncedOn;

    @ApiModelProperty("设备被添加时间")
    private Long addTime;

    @ApiModelProperty("详情显示电量百分比或者充电进度，数值0-100")
    private BigDecimal speed;

    @ApiModelProperty("详情类型：1 工具 2 充电器 3 电池")
    private Integer detailType;

    // -1 数据过期值
    @ApiModelProperty("充电器类：充电器状态 1: 充电中 2: 等待充电 3: 过温 4:故障")
    private Integer chargerState;

    @ApiModelProperty("电池包类：电池包状态 1：充电中 2：等待充电  3：过温  4:故障  7：放电")
    private Integer batteryState;

    @ApiModelProperty("电池类：电池健康状态 1：Normal 2：Service Recommended")
    private Integer batteryHealthState;

}
