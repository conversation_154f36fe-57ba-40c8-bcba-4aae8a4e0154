package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.ShadowStatus;
import com.chervon.fleet.web.entity.vo.EquipmentVo;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/21 11:22
 */
public interface ShadowStatusService extends IService<ShadowStatus> {

    /**
     * 批量获取设备影子信息
     *
     * @param deviceIds 设备id集合
     * @return 设备影子信息
     */
    List<ShadowStatus> listByDeviceIds(Collection<String> deviceIds,Long companyId);

    /**
     * 获取单个设备影子信息
     *
     * @param deviceId 设备id
     * @return 设备影子信息
     */
    default ShadowStatus getByDeviceId(String deviceId,Long companyId) {
        List<ShadowStatus> list = this.listByDeviceIds(Collections.singletonList(deviceId),companyId);
        if (list == null) {
            return null;
        }
        return list.stream().filter(e -> StringUtils.equals(deviceId, e.getDeviceId())).findFirst().orElse(null);
    }

    /**
     * 设置设备影子状态值
     * @param companyDevice 设备信息
     * @param vo 设备对象值
     */
    void setShadowStatusValue(CompanyDevice companyDevice, EquipmentVo vo);

}
