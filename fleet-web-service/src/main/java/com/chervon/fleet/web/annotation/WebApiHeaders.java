package com.chervon.fleet.web.annotation;

import com.chervon.common.web.util.HeaderUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

import java.lang.annotation.*;

/**
 * FleetWeb请求头Swagger注解类
 *
 * <AUTHOR>
 * @since 2023-07-27 16:10
 **/
@Target({ElementType.METHOD, ElementType.ANNOTATION_TYPE, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ApiImplicitParams({
    @ApiImplicitParam(name = HeaderUtils.LANGUAGE, value = "语言", paramType = "header", required = true),
    @ApiImplicitParam(name = HeaderUtils.TOKEN, value = "token", paramType = "header", required = true),
    @ApiImplicitParam(name = HeaderUtils.DEVICE_TYPE, value = "设备类型", paramType = "header", required = true),
    @ApiImplicitParam(name = HeaderUtils.USER_ID, value = "当前用户id", paramType = "header", required = true),
    @ApiImplicitParam(name = HeaderUtils.DEVICE_ID, value = "设备id", paramType = "header", required = true),
    @ApiImplicitParam(name = HeaderUtils.TRACE_ID, value = "日志追踪id", paramType = "header", required = true)
})
public @interface WebApiHeaders {
}

