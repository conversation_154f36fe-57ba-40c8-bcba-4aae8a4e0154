package com.chervon.fleet.web.utils;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusEnum;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.api.entity.vo.InventoryInfoVo;
import com.chervon.fleet.web.api.entity.vo.InventoryVo;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据通用逻辑判断工具
 * <AUTHOR> 2024/1/28
 */
public class DataUtils {
    /**
     * 判断网关设备是否离线
     * @param companyDevice
     * @return
     */
    public static boolean isOffline(CompanyDevice companyDevice){
        if(Objects.isNull(companyDevice)){
            return true;
        }
        if(Objects.isNull(companyDevice.getOnlineStatus()) || companyDevice.getOnlineStatus().equals(OnlineStatusEnum.OFFLINE.getType())){
            return true;
        }
        return false;
    }

    /**
     * 根据设备Id获取sn码
     * @param deviceId
     * @return
     */
    public static String getSn(String deviceId){
        if(deviceId.length()< CommonConstant.SIX){
            return "";
        }
        return deviceId.substring(CommonConstant.ONE,CommonConstant.FIVE);
    }

    /**
     * 替换未知电池包类型显示
     * @param batteryType 电池包类型
     * @return 类型
     */
    public static String replaceUnknownBattery(String batteryType){
        if(StringConst.BATTERY_TYPE_0.equals(batteryType)){
            return StringConst.UNKNOWN_BATTERY_TYPE;
        }
        return batteryType;
    }

    /**
     * 设置数据上报状态和位置信息
     * @param deviceStatus
     * @param res
     */
    public static void setDataReportValue(List<DeviceStatusVo> deviceStatus, InventoryInfoVo res) {
        if (CollectionUtils.isEmpty(deviceStatus)) {
            return;
        }
        DeviceStatusVo status = deviceStatus.get(0);
        if (status != null) {
            // 数据上报状态
            res.setReportCompleteStatus(status.getReportCompleteStatus());
            // 设置地理位置
            res.setLocation(status.getLocation());
            res.setCoordinate(status.getCoordinate());
        }
    }

    public static void setDeviceFaultInfo(CompanyDevice e, Map<String, List<BiDeviceErrorList>> deviceFalutMap, InventoryVo vo) {
        vo.setErrorStatus(0);
        if (CollectionUtils.isEmpty(deviceFalutMap.get(e.getDeviceId()))) {
            return;
        }
        if (e.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode())) {
            if (e.getOnlineStatus() != null && e.getOnlineStatus().equals(OnlineStatusEnum.ONLINE.getType())) {
                vo.setErrorStatus(1);
            }
        } else {//工具，电池包类，看过期时间12小时
            List<BiDeviceErrorList> list = deviceFalutMap.get(e.getDeviceId());
            for (BiDeviceErrorList error : list) {
                if (!FleetDeviceConfig.isExpired12Hours(error.getModifyTime())) {
                    vo.setErrorStatus(1);
                    return;
                }
            }
        }
    }
}
