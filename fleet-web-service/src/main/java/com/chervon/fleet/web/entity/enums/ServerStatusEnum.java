package com.chervon.fleet.web.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * 服务状态
 *
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
public enum ServerStatusEnum implements TypeEnum {
    /**
     * 离线
     */
    DOWN(0, "离线"),
    /**
     * 在线
     */
    UP(1, "在线");

    private int type;
    private String desc;

    ServerStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
