package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiCategoryCount;
import com.chervon.fleet.web.entity.vo.dashboard.CategoryCountDashboardVo;

import java.util.List;

/**
 * 设备分类数量统计表（看板：Machine Category）服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
public interface BiCategoryCountService extends IService<BiCategoryCount> {

    /**
     * 设备分类统计看板查询
     *
     * @param companyId 公司ID
     * @return 结果
     */
    List<CategoryCountDashboardVo> categoryCount(Long companyId);
}
