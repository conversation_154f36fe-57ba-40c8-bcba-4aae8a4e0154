package com.chervon.fleet.web.entity.vo.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 电池数量看板Vo
 * <AUTHOR>
 * @since 2023-07-28 17:28
 **/
@Data
@Accessors(chain = true)
@ApiModel("电池数量看板Vo")
public class BatteryCountDashboardVo {
    private static final long serialVersionUID = 1L;
    /**
     * 电池类型名称
     */
    @ApiModelProperty("电池类型名称")
    private String batteryType;
    @ApiModelProperty("电池总数")
    private Integer batteryCount;
    @ApiModelProperty("电池包总安时")
    private BigDecimal totalAh;
}
