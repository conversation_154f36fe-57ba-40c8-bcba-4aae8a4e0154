package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 报表库存
 * <AUTHOR>
 * @date 2023/7/25 11:35
 */
@Data
@Accessors(chain = true)
public class ReportInventoryVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备图片
     */
    @ApiModelProperty("设备图片")
    private String picture;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备sn")
    private String sn;

    @ApiModelProperty("二级品类")
    private String category;

    @ApiModelProperty("商品型号")
    private String model;

    @ApiModelProperty("库存状态")
    private String status;
}
