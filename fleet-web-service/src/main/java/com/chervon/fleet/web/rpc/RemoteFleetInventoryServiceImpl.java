package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.dto.InventorySearchDto;
import com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;
import com.chervon.fleet.web.api.entity.vo.InventoryInfoVo;
import com.chervon.fleet.web.api.entity.vo.InventorySearchVo;
import com.chervon.fleet.web.api.service.RemoteFleetInventoryService;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.utils.DataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * fleet rpc库存服务实现
 * <AUTHOR>
 * @date 2023/7/17 11:25
 */
@Service
@DubboService
@Slf4j
public class RemoteFleetInventoryServiceImpl implements RemoteFleetInventoryService {

    @Autowired
    private CompanyDeviceService companyDeviceService;

    @Autowired
    private DeviceTagService deviceTagService;
    @Autowired
    private RuleEngineService ruleEngineService;
    @Autowired
    private FilterConditionService filterConditionService;

    @Autowired
    private CompanyDeviceUtils companyDeviceUtils;

    @Override
    public FilterConditionVo filterCondition(String deviceName) {
        return filterConditionService.filterCondition(UserContext.getCompanyId(), true, true, true, false, false, deviceName);
    }

    @Override
    public Long countFilterAndSearch(InventorySearchDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "inventorySearchDto");
        Assert.notNull(req.getSearchType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.SEARCH_TYPE);
        if (!Arrays.asList(CommonConstant.ONE,CommonConstant.TWO).contains(req.getSearchType())) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, StringConst.SEARCH_TYPE);
        }
        if (req.getSearchType().equals(CommonConstant.ONE)) {
            // 库存
            return companyDeviceUtils.countFilterAndSearch(companyId, req.getDeviceName(), req.getCategoryCodes(), req.getWarehouseStatus(), req.getTagIds());
        }
        if (!StringUtils.isEmpty(req.getGatewayId())) {
            return companyDeviceUtils.countFilterAndSearchByGatewayId(companyId, req.getGatewayId(), req.getDeviceName(), req.getCategoryCodes(), req.getWarehouseStatus(), req.getTagIds());
        }
        return companyDeviceUtils.countFilterAndSearchByDeviceIds(companyId, req.getDeviceIds(), req.getDeviceName(), req.getCategoryCodes(), req.getWarehouseStatus(), req.getTagIds());
    }



    @Override
    public InventorySearchVo listFilterAndSearch(InventorySearchDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "inventorySearchDto");
        Assert.notNull(req.getSearchType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.SEARCH_TYPE);
        if (!Arrays.asList(CommonConstant.ONE,CommonConstant.TWO).contains(req.getSearchType())) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR,StringConst.SEARCH_TYPE);
        }
        if (req.getSearchType().equals(CommonConstant.ONE)) {
            // 库存
            return listFilterAndSearch(companyId, req.getDeviceName(), req.getCategoryCodes(), req.getWarehouseStatus(), req.getTagIds());
        }
        return listFilterAndSearchByDeviceIds(companyId, req.getGatewayId(), req.getDeviceIds(), req.getDeviceName(), req.getCategoryCodes(), req.getWarehouseStatus(), req.getTagIds());
    }

    /**
     * 库存列表
     *
     * @param companyId       租户id
     * @param deviceName      设备名称
     * @param categoryCodes   二级品类编码集合
     * @param warehouseStatus 库存状态集合
     * @param tagIds          标签id集合
     * @return 库存列表数据
     */
    private InventorySearchVo listFilterAndSearch(Long companyId, String deviceName, List<String> categoryCodes, List<Integer> warehouseStatus, List<Long> tagIds) {
        // 按条件查出租户下的所有设备
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId);
        boolean haveCondition=false;
        // 设备名称条件
        if (StringUtils.isNotEmpty(deviceName)) {
            haveCondition=true;
            queryWrapper.like(CompanyDevice::getDeviceName, deviceName);
        }
        // 品类条件
        if (!CollectionUtils.isEmpty(categoryCodes)) {
            haveCondition=true;
            queryWrapper.in(CompanyDevice::getSecondCategoryCode, categoryCodes);
        }
        // 库存条件
        if (!CollectionUtils.isEmpty(warehouseStatus)) {
            haveCondition=true;
            queryWrapper.in(CompanyDevice::getWarehouseStatus, warehouseStatus);
        }
        queryWrapper.orderByDesc(CompanyDevice::getBindingTime);
        //根据条件进行查询
        List<CompanyDevice> list = companyDeviceService.list(queryWrapper);
        //构造返回对象
        InventorySearchVo res = new InventorySearchVo();

        long count=0;
        //统计租户下设备总数
        if(haveCondition){
            LambdaQueryWrapper<CompanyDevice> countWrapper = new LambdaQueryWrapper<CompanyDevice>()
                    .eq(CompanyDevice::getCompanyId, companyId);
            count = companyDeviceService.count(countWrapper);
        }else{
            count=list.size();
        }
        res.setTotalCount(count);

        // 设备标签条件
        if (!list.isEmpty() && !CollectionUtils.isEmpty(tagIds)) {
            List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getTagId, tagIds)
                    .in(DeviceTag::getDeviceId, list.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList()))
                    .select(DeviceTag::getDeviceId));
            List<String> restDeviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            list.removeIf(e -> !restDeviceIds.contains(e.getDeviceId()));
        }
        InventoryStatusQuery query = new InventoryStatusQuery();
        query.setCompanyId(companyId)
                .setListDeviceId(list.stream().map(CompanyDevice::getDeviceId)
                        .distinct().collect(Collectors.toList()));
        List<DeviceStatusVo> deviceStatus = ruleEngineService.getReportCompleteStatus(query);
        Map<String, DeviceStatusVo> deviceStatusMap = deviceStatus.stream().collect(Collectors.toMap(DeviceStatusVo::getDeviceId, Function.identity()));
        // 装配数据
        companyDeviceUtils.handleInventorySearch(res, list, deviceStatusMap, deviceName, categoryCodes, warehouseStatus, tagIds);
        return res;
    }

    /**
     * 盘点列表
     *
     * @param companyId       租户id
     * @param targetDeviceIds 设备id集合
     * @param deviceName      设备名称
     * @param categoryCodes   二级品类编码集合
     * @param warehouseStatus 库存状态集合
     * @param tagIds          标签id集合
     * @return 盘点列表数据
     */
    private InventorySearchVo listFilterAndSearchByDeviceIds(Long companyId, String gatewayId, List<String> targetDeviceIds, String deviceName, List<String> categoryCodes, List<Integer> warehouseStatus, List<Long> tagIds) {
        // 按条件查出租户下的所有设备
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .in(CompanyDevice::getWarehouseStatus, Arrays.asList(WarehouseStatusEnum.IN_WAREHOUSE.getType(), WarehouseStatusEnum.OUT_FOR_WORK.getType()));
        // 网关和设备条件
        if (StringUtils.isNotEmpty(gatewayId)) {
            //读取网关最近一分钟内上报的设备
            final List<String> deviceIdList = companyDeviceUtils.getDeviceIdListByGateway(companyId, gatewayId);
            if(!CollectionUtils.isEmpty(deviceIdList)){
                queryWrapper.in(CompanyDevice::getDeviceId, deviceIdList);
            }
        }else if(!CollectionUtils.isEmpty(targetDeviceIds)){
            queryWrapper.in(CompanyDevice::getDeviceId, targetDeviceIds);
        }else{
            //nothing to do
        }
        // 设备名称条件
        if (StringUtils.isNotEmpty(deviceName)) {
            queryWrapper.like(CompanyDevice::getDeviceName, deviceName);
        }
        // 品类条件
        if (!CollectionUtils.isEmpty(categoryCodes)) {
            queryWrapper.in(CompanyDevice::getSecondCategoryCode, categoryCodes);
        }
        // 库存条件
        if (!CollectionUtils.isEmpty(warehouseStatus)) {
            queryWrapper.in(CompanyDevice::getWarehouseStatus, warehouseStatus);
        }
        queryWrapper.orderByDesc(CompanyDevice::getBindingTime);
        //根据条件进行查询
        List<CompanyDevice> list = companyDeviceService.list(queryWrapper);
        //构造返回对象
        InventorySearchVo res = new InventorySearchVo();
        //统计租户下设备总数：条件非充电器和电池包
        LambdaQueryWrapper<CompanyDevice> countWrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId);

        final long count = companyDeviceService.count(countWrapper);
        res.setTotalCount(count);

        // 设备标签条件
        if (!list.isEmpty() && !CollectionUtils.isEmpty(tagIds)) {
            List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getTagId, tagIds)
                    .in(DeviceTag::getDeviceId, list.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList()))
                    .select(DeviceTag::getDeviceId));
            List<String> restDeviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            list.removeIf(e -> !restDeviceIds.contains(e.getDeviceId()));
        }
        InventoryStatusQuery query = new InventoryStatusQuery();
        query.setCompanyId(companyId)
                .setListDeviceId(list.stream().map(CompanyDevice::getDeviceId)
                        .distinct().collect(Collectors.toList()));
        List<DeviceStatusVo> deviceStatus = ruleEngineService.getReportCompleteStatus(query);
        Map<String, DeviceStatusVo> deviceStatusMap = deviceStatus.stream().collect(Collectors.toMap(DeviceStatusVo::getDeviceId, Function.identity()));
        // 装配数据
        companyDeviceUtils.handleInventorySearch(res, list, deviceStatusMap, deviceName, categoryCodes, warehouseStatus, tagIds);
        return res;
    }

    @Override
    public InventoryInfoVo detail(String deviceId) {
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
        Long companyId = UserContext.getCompanyId();
        InventoryInfoVo res = new InventoryInfoVo();
        res.setCanEdit(0);
        CompanyDevice companyDevice = companyDeviceService.getOne(new LambdaQueryWrapper<CompanyDevice>().eq(CompanyDevice::getCompanyId, companyId).eq(CompanyDevice::getDeviceId, deviceId));
        if (companyDevice == null) {
            return res;
        }
        InventoryStatusQuery query = new InventoryStatusQuery();
        query.setCompanyId(companyId).setListDeviceId(Collections.singletonList(deviceId));
        List<DeviceStatusVo> deviceStatus = ruleEngineService.getWarehouseStatusByCache(query);
        DataUtils.setDataReportValue(deviceStatus, res);
        // 库存状态
        res.setWarehouseStatus(companyDevice.getWarehouseStatus());
        res.setDeviceId(companyDevice.getDeviceId());
        res.setMac(companyDevice.getMac());
        res.setDeviceSn(companyDevice.getDeviceSn());
        res.setDeviceName(companyDevice.getDeviceName());
        res.setCategoryCode(companyDevice.getSecondCategoryCode());
        res.setOnlineStatus(companyDevice.getOnlineStatus());
        // 获取fleet品类信息
        companyDeviceUtils.setProductInfo(deviceId, companyDevice, res);
        return res;
    }


}
