package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.dashboard.EnergyUsageDashboardQuery;
import com.chervon.fleet.web.entity.po.BiDailyEnergyCharged;
import com.chervon.fleet.web.entity.vo.dashboard.EnergyUsageDashboardVo;

import java.util.List;

/**
 * 租户下充电电量统计表(看板：Energy Charged图--柱状图使用)
---Energy Charged统计租户所有Power HUB在统计时段内每天给电池充电的总电量，单位为kWh，并展示租户的所有设备（包含已删除设备）的历史总的充给电池的总电量和统计时段内有数据上报的日期的平均充给电池的电量；以下两个参数为基于此表数据汇总实时统计参数：Historical Total ：当前用户绑定的全部Charger在被当前租户绑定后的指定的时间段内的总充电电量(单位:KWh)      
Daily Average: 设备被当前租户绑定后的指定时间段内的充电电量÷有充电数据的天数服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
public interface BiDailyEnergyChargedService   extends IService<BiDailyEnergyCharged> {

    /**
     * 租户下充电电量统计
     * @param query 查询类
     * @return 查询结果
     */
    List<EnergyUsageDashboardVo> energyCharged(EnergyUsageDashboardQuery query);
}
