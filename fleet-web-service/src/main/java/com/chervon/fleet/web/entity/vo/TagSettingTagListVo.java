package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 设置标签根据分组查询标签列表结果数据
 * <AUTHOR>
 * @date 2023/7/12 15:10
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "设置标签根据分组查询标签列表结果数据")
public class TagSettingTagListVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 标签设置中标签数据
     */
    @ApiModelProperty("标签设置中标签数据")
    private List<TagSettingTagVo> tags;

    @ApiModelProperty("分组id")
    private Long groupId;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("选中设备数量")
    private Integer deviceSelectNumber;

}
