package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-16 16:53
 **/
@Data
@TableName("t_data_battery_count")
public class BatteryCount implements Serializable {

    private static final long serialVersionUID = 1;

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 电池类型名称
     */
    private String batteryType;
    /**
     * 电池总数
     */
    private Integer batteryCount;
    /**
     * 更新时间
     */
    private Long modifyTime;
}
