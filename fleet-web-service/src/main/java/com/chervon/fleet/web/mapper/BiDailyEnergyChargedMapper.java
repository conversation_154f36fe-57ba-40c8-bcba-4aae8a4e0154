package com.chervon.fleet.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.fleet.web.entity.po.BiDailyEnergyCharged;
import org.apache.ibatis.annotations.Mapper;

/**
 * 租户下充电电量统计表(看板：Energy Charged图--柱状图使用)
---Energy Charged统计租户所有Power HUB在统计时段内每天给电池充电的总电量，单位为kWh，并展示租户的所有设备（包含已删除设备）的历史总的充给电池的总电量和统计时段内有数据上报的日期的平均充给电池的电量；以下两个参数为基于此表数据汇总实时统计参数：Historical Total ：当前用户绑定的全部Charger在被当前租户绑定后的指定的时间段内的总充电电量(单位:KWh)      
Daily Average: 设备被当前租户绑定后的指定时间段内的充电电量÷有充电数据的天数(t_bi_daily_energy_charged)数据Mapper
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
*/
@Mapper
public interface BiDailyEnergyChargedMapper extends BaseMapper<BiDailyEnergyCharged> {

}
