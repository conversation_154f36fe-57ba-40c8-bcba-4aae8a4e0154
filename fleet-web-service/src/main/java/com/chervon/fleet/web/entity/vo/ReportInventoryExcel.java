package com.chervon.fleet.web.entity.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: ReportInventoryExcel
 * @description:库存报表导出对象
 * <AUTHOR>
 * @date 2023/7/25 11:35
 */
@Data
@Accessors(chain = true)
public class ReportInventoryExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    @Alias("Name")
    private String name;

    @Alias("Serial Number")
    private String sn;

    @Alias("Category")
    private String category;

    @Alias("Model#")
    private String model;

    @Alias("Inventory Status")
    private String status;

    public String getName() {
        return CsvUtil.format(this.name);
    }

    public String getSn() {
        return CsvUtil.format(this.sn);
    }

    public String getCategory() {
        return CsvUtil.format(this.category);
    }

    public String getModel() {
        return CsvUtil.format(this.model);
    }

    public String getStatus() {
        return CsvUtil.format(this.status);
    }
}
