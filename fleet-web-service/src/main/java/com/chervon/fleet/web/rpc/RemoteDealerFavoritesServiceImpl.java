package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.fleet.web.api.service.RemoteDealerFavoritesService;
import com.chervon.fleet.web.entity.po.DealerFavorites;
import com.chervon.fleet.web.service.DealerFavoritesService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/1 16:24
 */
@DubboService
@Service
@Slf4j
@AllArgsConstructor
public class RemoteDealerFavoritesServiceImpl implements RemoteDealerFavoritesService {

    private final DealerFavoritesService dealerFavoritesService;

    @Override
    public void removeByDealerIds(List<Long> dealerIds) {
        if (!CollectionUtils.isEmpty(dealerIds)) {
            dealerFavoritesService.remove(new LambdaQueryWrapper<DealerFavorites>().in(DealerFavorites::getDealerId, dealerIds));
        }
    }
}
