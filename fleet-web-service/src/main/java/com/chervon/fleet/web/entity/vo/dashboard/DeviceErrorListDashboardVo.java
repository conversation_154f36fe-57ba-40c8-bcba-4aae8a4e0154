package com.chervon.fleet.web.entity.vo.dashboard;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备错误列表看板Vo
 * <AUTHOR>
 * @since 2023-07-28 09:42
 **/
@Data
@ApiModel("设备错误列表看板Vo")
public class DeviceErrorListDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 错误数量
     */
    @ApiModelProperty("错误数量")
    private Integer count;
    /**
     * 设备错误列表
     */
    @ApiModelProperty("设备错误列表")
    private List<DeviceError> deviceErrorList;

    /**
     * 设备错误列表项
     */
    @Data
    @ApiModel("设备错误列表项")
    public static class DeviceError implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 设备id
         */
        @ApiModelProperty("设备id")
        private String deviceId;
        @ApiModelProperty("设备名称")
        private String deviceName;
        @ApiModelProperty("错误编码")
        private String errorCode;
        @ApiModelProperty("错误内容")
        private String errorMessage;
        /**
         * 处理建议内容
         */
        @ApiModelProperty("处理建议内容")
        private String suggestionContent;
    }
}
