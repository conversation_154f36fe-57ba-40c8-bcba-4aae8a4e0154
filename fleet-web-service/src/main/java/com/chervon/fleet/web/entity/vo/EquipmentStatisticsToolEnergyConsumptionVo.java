package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Entity com.chervon.fleet.domain.EquipmentStatisticsToolEnergyConsumption
 * @description: 工具能量消耗
 * <AUTHOR>
 * @date 2023/7/24 18:53
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsToolEnergyConsumptionVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public EquipmentStatisticsToolEnergyConsumptionVo() {
        this.power = 0;
    }

    @ApiModelProperty("日期yyyy-MM-dd字符串")
    private String date;

    @ApiModelProperty("电量(kWh)")
    private Integer power;

}
