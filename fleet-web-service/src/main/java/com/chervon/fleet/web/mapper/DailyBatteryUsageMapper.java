package com.chervon.fleet.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.fleet.web.entity.po.DailyBatteryUsage;
import com.chervon.fleet.web.entity.po.DailyBatteryUsageCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-15 17:30
 **/
@Mapper
public interface DailyBatteryUsageMapper extends BaseMapper<DailyBatteryUsage> {

    /**
     * * 按日期和分类分组汇总统计各分类的设备使用情况
     * @param date
     * @return
     */
    List<DailyBatteryUsageCount> countDailyBatteryUsageByDate(@Param("date")String date, @Param("companyId")Long companyId);
}
