package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * power-bank充电详情统计表(看板详情页：Power Overview Detail图)(t_bi_power_bank_detail)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_power_bank_detail")
public class BiPowerBankDetail extends Model<BiPowerBankDetail> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * hub设备id(父设备id)
     */
    private String hubDeviceId;
    /**
     * 仓位号
     */
    private String portNumber;
    /**
     * 充电仓适配器名称
     */
    private String adaptorName;
    /**
     * 充电仓适配器类别编码
     */
    private String adaptorCategoryCode;
    /**
     * 仓位状态：1:正常充电(绿色)  2等待充电(灰色) 3:电池过温(橙色) 4:电池故障(红色)
     */
    private Integer portStatus;
    /**
     * 电池设备id
     */
    private String batteryDeviceId;
    /**
     * 仓位电池类型：8.0Ah、12.0Ah、5.0Ah
     */
    private String batteryType;
    /**
     * 充电百分比
     */
    private BigDecimal chargingPercentage;
    /**
     * 已充电安时数
     */
    private BigDecimal chargingEnergyAh;
    /**
     * 是否有故障：0无   1有
     */
    private Integer isFault;
    /**
     * 充电仓适配器状态: 0保留 1正常充电 2等待充电 3电池过温 4电池故障
     */
    private Integer adaptorState;
    /**
     * 更新时间
     */
    private Date modifyTime;

}