package com.chervon.fleet.web.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 充电器数量统计表（看板：total charge system饼状图）(t_bi_charger_count)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_charger_count")
public class BiChargerCount extends Model<BiChargerCount> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 充电器二级分类编号：
     * PGXPowerBank
     * PGXHub
     * TurboCharger
     * Adaptor
     */
    @Translate(adapter = "fleetCategory", targetField = {"chargerName"})
    private String chargerCategory;
    /**
     * 充电系统名称
     */
    private String chargerName;
    /**
     * 日期：2023-07-12
     */
    private Integer chargerCount;
    /**
     * 更新时间
     */
    private Date modifyTime;

}