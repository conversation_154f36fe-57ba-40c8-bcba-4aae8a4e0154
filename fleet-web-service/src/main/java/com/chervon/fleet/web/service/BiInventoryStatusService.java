package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiInventoryStatus;
import com.chervon.fleet.web.api.entity.vo.InventoryStatusDashboardVo;

import java.util.List;

/**
 * 设备库存状态统计表(看板：Inventory Status)服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
public interface BiInventoryStatusService extends IService<BiInventoryStatus> {
    /**
     * 库存状态看板查询
     *
     * @param companyId 公司ID
     * @return 看板VO
     */
    List<InventoryStatusDashboardVo> inventoryStatus(Long companyId);
}
