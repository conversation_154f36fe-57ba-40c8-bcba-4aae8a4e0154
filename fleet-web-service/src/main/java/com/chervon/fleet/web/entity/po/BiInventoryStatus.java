package com.chervon.fleet.web.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 设备库存状态统计表(看板：Inventory Status)(t_bi_inventory_status)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_inventory_status")
public class BiInventoryStatus extends Model<BiInventoryStatus> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 库存状态名称：out for work、in warehouse、unknown location、never seen、
     */
    private String inventoryType;
    /**
     * 库存状态数量
     */
    private Integer inventoryCount;
    /**
     * 更新时间
     */
    private Date modifyTime;

}