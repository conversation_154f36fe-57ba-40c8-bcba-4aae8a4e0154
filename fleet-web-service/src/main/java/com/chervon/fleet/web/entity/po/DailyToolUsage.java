package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-14 17:23
 **/
@Data
@TableName("t_data_daily_tool_usage")
public class DailyToolUsage implements Serializable {
    private static final long serialVersionUID = 1L;
    public DailyToolUsage() {
        this.usageDuration = 0;
        this.numberTimes = 0;
        this.energyConsume = 0;
    }

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 设备一级分类
     */
    private String categoryCode;
    /**
     * 日期：2023-07-12
     */
    private String date;
    /**
     * 当日总使用时长（单位：分钟 不受绑定解绑影响）
     */
    private Integer usageDuration;
    /**
     * 当日开机次数（不受绑定解绑影响）
     */
    private Integer numberTimes;
    /**
     * 设备当天放电总量(单位：kwh)
     */
    private Integer energyConsume;
    /**
     * 开始和截止的坐标值
     */
    private String spanUsageDuration;
    private String spanNumberTimes;
    private String spanEnergyConsume;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long modifyTime;

    public void setId(){
        this.id=(long)(this.deviceId+this.companyId+this.date+"0").hashCode();
    }
}
