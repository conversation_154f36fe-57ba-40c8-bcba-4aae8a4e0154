package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.fleet.web.api.entity.dto.CompanyDeviceDto;
import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusEnum;
import com.chervon.fleet.web.api.entity.query.CompanyDeviceQuery;
import com.chervon.fleet.web.api.entity.vo.CompanyDeviceVo;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.dto.DeviceBasicCache;
import com.chervon.fleet.web.entity.dto.WarehouseStatusCache;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.mapper.CompanyDeviceMapper;
import com.chervon.fleet.web.service.AppGatewayService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/5 11:04
 */
@Service
@Slf4j
public class CompanyDeviceServiceImpl extends ServiceImpl<CompanyDeviceMapper, CompanyDevice> implements CompanyDeviceService {

    @Override
    public PageResult<CompanyDeviceVo> pageList(CompanyDeviceQuery query) {
        //分页查询
        IPage<CompanyDevice> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<CompanyDevice> queryWrapper = getWrapper(query);
        queryWrapper.orderByAsc(CompanyDevice::getBindingTime);
        page = this.page(page, queryWrapper);
        //分页结果
        PageResult<CompanyDeviceVo> pageResult = new PageResult<>();
        pageResult.setTotal(page.getTotal());
        pageResult.setPageSize(page.getSize());
        pageResult.setPageNum(page.getCurrent());
        List<CompanyDevice> queryList = page.getRecords();
        if (CollectionUtils.isEmpty(queryList)) {
            pageResult.setList(new ArrayList<>());
            return pageResult;
        }
        final List<CompanyDeviceVo> companyVos = BeanCopyUtils.copyList(page.getRecords(), CompanyDeviceVo.class);
        pageResult.setList(companyVos);
        return pageResult;
    }

    @Override
    public List<CompanyDevice> getList(CompanyDeviceQuery query) {
        LambdaQueryWrapper<CompanyDevice> queryWrapper = getWrapper(query);
        queryWrapper.orderByAsc(CompanyDevice::getBindingTime);
        return list(queryWrapper);
    }

    @Override
    public List<CompanyDeviceVo> queryList(CompanyDeviceQuery query) {
        final List<CompanyDevice> list = getList(query);
        return BeanCopyUtils.copyList(list, CompanyDeviceVo.class);
    }

    @Override
    public CompanyDevice get(CompanyDeviceQuery query) {
        final List<CompanyDevice> list = getList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 获取租户下PowerHub在线设备数量
     * @param companyId 租户id
     * @return
     */
    @Override
    public long getPowerHubCount(Long companyId, Boolean isOnline) {
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new QueryWrapper<CompanyDevice>().lambda();
        queryWrapper.eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getSecondCategoryCode, ChargerCategoryEnum.PGX_HUB.getCode());
        if(!Objects.isNull(isOnline) && isOnline){
            queryWrapper.eq(CompanyDevice::getOnlineStatus, OnlineStatusEnum.ONLINE.getType());
        }
        return count(queryWrapper);
    }

    /**
     * 获取租户下PowerHub在线设备列表
     * @param companyId 租户id
     * @return
     */
    @Override
    public List<String> getHubOnlineList(Long companyId) {
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new QueryWrapper<CompanyDevice>().lambda();
        queryWrapper.eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getSecondCategoryCode, ChargerCategoryEnum.PGX_HUB.getCode())
                .eq(CompanyDevice::getOnlineStatus, OnlineStatusEnum.ONLINE.getType())
                .select(CompanyDevice::getDeviceId);
        final List<CompanyDevice> list = list(queryWrapper);
        return list.stream().map(a->a.getDeviceId()).collect(Collectors.toList());
    }

    @Override
    public CompanyDevice getDeviceOnlineInfo(CompanyDeviceQuery query) {
        final List<CompanyDevice> list = getDeviceOnlineInfoList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public ConcurrentMap<String,CompanyDevice> getDeviceOnlineInfoMap(CompanyDeviceQuery query) {
        final List<CompanyDevice> list = getDeviceOnlineInfoList(query);
        if(CollectionUtils.isEmpty(list)){
            return new ConcurrentHashMap<>();
        }
        return list.stream().collect(Collectors.toConcurrentMap(CompanyDevice::getDeviceId, Function.identity(), (k1, k2) -> k2));
    }

    private List<CompanyDevice> getDeviceOnlineInfoList(CompanyDeviceQuery query) {
        LambdaQueryWrapper<CompanyDevice> queryWrapper = getWrapper(query);
        queryWrapper.select(CompanyDevice::getDeviceId,CompanyDevice::getOnlineStatus);
        final List<CompanyDevice> list = list(queryWrapper);
        return list;
    }

    @Override
    public Integer clearWarehouseStatus(Long companyId){
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new QueryWrapper<CompanyDevice>().lambda();
        queryWrapper.eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getIsDeleted, 0)
                .select(CompanyDevice::getDeviceId);
        final List<CompanyDevice> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        for(CompanyDevice companyDevice:list){
            final String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(companyDevice.getDeviceId());
            RedisUtils.deleteObject(warehouseStatusKey);
        }
        return list.size();
    }

    @Override
    public WarehouseStatusCache getWarehouseStatusById(String deviceId) {
        final String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(deviceId);
        WarehouseStatusCache warehouseStatusCache = RedisUtils.getCacheObject(warehouseStatusKey);
        // 初始化数据
        if (Objects.isNull(warehouseStatusCache)) {
            final List<CompanyDevice> list = this.getList(new CompanyDeviceQuery().setDeviceId(deviceId).setIsDelete(0));
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            final CompanyDevice companyDevice = list.get(0);
            // 设置设备基本信息
            DeviceBasicCache deviceBasicCache = new DeviceBasicCache();
            BeanUtils.copyProperties(companyDevice, deviceBasicCache);
            String deviceInfoKey = RedisConst.getDeviceInfoKey(deviceId);
            RedisUtils.setWithExpire(deviceInfoKey, deviceBasicCache, RedisConst.DEVICE_INFO_EXPIRE_SECOND);
            // 设置设备状态缓存
            warehouseStatusCache = new WarehouseStatusCache();
            warehouseStatusCache.setDeviceId(companyDevice.getDeviceId());
            warehouseStatusCache.setCompanyId(companyDevice.getCompanyId());
            warehouseStatusCache.setWarehouseStatus(companyDevice.getWarehouseStatus());
            warehouseStatusCache.setGatewayId(companyDevice.getGatewayId());
            if (StringUtils.hasText(companyDevice.getGatewayId())) {
                final AppGatewayService gatewayService = SpringUtils.getBean(AppGatewayService.class);
                final AppGateway gatewayId = gatewayService.getById(companyDevice.getGatewayId());
                if (gatewayId != null) {
                    warehouseStatusCache.setLocation(gatewayId.getLocation());
                    warehouseStatusCache.setCoordinate(gatewayId.getCoordinate());
                }
            }
            RedisUtils.setCacheObject(warehouseStatusKey, warehouseStatusCache);
        }
        return warehouseStatusCache;
    }

    @Override
    public DeviceBasicCache getDeviceByCache(String deviceId) {
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
        final String deviceInfoKey = RedisConst.getDeviceInfoKey(deviceId);
        final Object objDeviceCache = RedisUtils.getCacheObject(deviceInfoKey);
        if (Objects.isNull(objDeviceCache)) {
            final CompanyDevice device = get(new CompanyDeviceQuery().setDeviceId(deviceId));
            if(Objects.isNull(device)){
                return null;
            }
            final DeviceBasicCache deviceBasicCache = BeanCopyUtils.copy(device, DeviceBasicCache.class);
            RedisUtils.setWithExpire(deviceInfoKey, deviceBasicCache, RedisConst.DEVICE_INFO_EXPIRE_SECOND);
            return deviceBasicCache;
        }
        return (DeviceBasicCache) objDeviceCache;
    }

    @Override
    public List<String> getDeviceListByCache(Long companyId) {
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        final String deviceListKey = RedisConst.getCompanyDeviceListKey(companyId);
        List<String> listDeviceId = RedisUtils.getCacheObject(deviceListKey);
        if (Objects.isNull(listDeviceId)) {
            LambdaQueryWrapper<CompanyDevice> queryWrapper = getWrapper(new CompanyDeviceQuery().setCompanyId(companyId));
            queryWrapper.select(CompanyDevice::getDeviceId);
            final List<CompanyDevice> list = list(queryWrapper);
            listDeviceId = list.stream().map(CompanyDevice::getDeviceId).collect(Collectors.toList());
            RedisUtils.setWithExpire(deviceListKey, listDeviceId, RedisConst.DEVICE_LIST_EXPIRE_SECOND);
        }
        return listDeviceId;
    }

    @Override
    public List<CompanyDevice> selectMaxOrder(Long companyId, String cDeviceName) {
        return this.getBaseMapper().selectMaxOrder(companyId, cDeviceName);
    }

    @Async
    @Override
    public void updateDeviceStatus(CompanyDeviceDto requestDto) {
        LambdaUpdateWrapper<CompanyDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StringUtils.hasText(requestDto.getDeviceId()), CompanyDevice::getDeviceId, requestDto.getDeviceId())
            .in(!CollectionUtils.isEmpty(requestDto.getDeviceIds()), CompanyDevice::getDeviceId, requestDto.getDeviceIds())
            .eq(!Objects.isNull(requestDto.getCompanyId()), CompanyDevice::getCompanyId, requestDto.getCompanyId())
            .set(CompanyDevice::getGatewayId, requestDto.getGatewayId())
            .set(CompanyDevice::getWarehouseStatus, requestDto.getWarehouseStatus())
            .set(CompanyDevice::getModifyTime, System.currentTimeMillis())
            .set(CompanyDevice::getModifier, "update-warehouse-status");
        this.update(updateWrapper);
    }

    @Async
    @Override
    public void updateDeviceOnlineStatus(CompanyDeviceDto requestDto) {
        LambdaUpdateWrapper<CompanyDevice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StringUtils.hasText(requestDto.getDeviceId()), CompanyDevice::getDeviceId, requestDto.getDeviceId())
            .in(!CollectionUtils.isEmpty(requestDto.getDeviceIds()), CompanyDevice::getDeviceId, requestDto.getDeviceIds())
            .eq(!Objects.isNull(requestDto.getCompanyId()), CompanyDevice::getCompanyId, requestDto.getCompanyId())
            .set(CompanyDevice::getOnlineStatus, requestDto.getOnlineStatus())
            .set(CompanyDevice::getModifyTime, System.currentTimeMillis())
            .set(CompanyDevice::getModifier, "update-online-status");
        this.update(updateWrapper);
    }

    @Override
    public List<CompanyDevice> listWithDeletedByCompanyId(Long companyId) {
        return this.getBaseMapper().selectListWithDeletedByCompanyId(companyId);
    }

    @Override
    public List<CompanyDevice> selectDeletedList(Long companyId,List<String> secondCategoryCodes,
                                                          List<String> tagDeviceIds,List<String> excludeDeviceIds) {
        return this.getBaseMapper().selectDeletedList(companyId,secondCategoryCodes,tagDeviceIds,excludeDeviceIds);
    }

    /**
     * 根据租户和分类获取分类下设备数量
     * @param companyId 租户
     * @return 分类数量
     */
    public Integer getCategoryCountCache(Long companyId,String categoryCode){
        final String deviceCountKey = RedisConst.getCategoryDeviceCountKey(companyId,categoryCode);
        Integer count = RedisUtils.getCacheObject(deviceCountKey);
        if(Objects.isNull(count)){
            count = baseMapper.getDeviceCount(companyId,categoryCode);
            RedisUtils.setWithExpire(deviceCountKey,count,RedisConst.DEVICE_INFO_EXPIRE_SECOND);
        }
        return count;
    }

    public List<Long> getCompanyIdList(){
        return baseMapper.getCompanyIdList();
    }

    /**
     * * 拼接综合查询条件
     *
     * @param query 查询条件类
     * @return MybatisPlus条件构造器
     */
    private static LambdaQueryWrapper<CompanyDevice> getWrapper(CompanyDeviceQuery query) {
        LambdaQueryWrapper<CompanyDevice> queryWrapper = new QueryWrapper<CompanyDevice>().lambda();
        queryWrapper.eq(query.getCompanyId() != null, CompanyDevice::getCompanyId, query.getCompanyId())
            .eq(query.getDeviceId() != null, CompanyDevice::getDeviceId, query.getDeviceId())
            .eq(query.getDeviceSn() != null, CompanyDevice::getDeviceSn, query.getDeviceSn())
            .eq(query.getGatewayId() != null, CompanyDevice::getGatewayId, query.getGatewayId())
            .eq(query.getIsDelete()!=null, CompanyDevice::getIsDeleted, query.getIsDelete())
            .eq(query.getWarehouseStatus() != null, CompanyDevice::getWarehouseStatus, query.getWarehouseStatus())
            .in(!CollectionUtils.isEmpty(query.getFirstCategoryCodes()), CompanyDevice::getFirstCategoryCode, query.getFirstCategoryCodes());
        return queryWrapper;
    }
}
