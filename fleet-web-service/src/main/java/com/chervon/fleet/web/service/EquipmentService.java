package com.chervon.fleet.web.service;

import com.chervon.fleet.web.api.entity.dto.DeviceEditDto;
import com.chervon.fleet.web.entity.dto.*;
import com.chervon.fleet.web.entity.vo.*;
import java.util.List;
/**
 * 设备服务接口
 */
public interface EquipmentService {

    /**
     * 一个设备最大能打50个标签
     */
    Integer DEVICE_TAG_MAX = 50;

    /**
     * 筛选项
     * @param search 搜索设备名称
     * @return 筛选项
     */
    WebFilterConditionVo filterCondition(String search);

    /**
     * 根据筛选条件查询结果
     * @param req 查询条件
     * @return 查询结果
     */
    EquipmentSearchVo search(EquipmentSearchDto req);

    /**
     * 查询设备详情
     *
     * @param deviceId 设备id
     * @return 设备详情
     */
    EquipmentVo detail(String deviceId);

    /**
     * 编辑设备名称
     * @param req 操作对象
     */
    void editName(DeviceEditDto req);

    /**
     * 删除设备
     * @param deviceId 设备id
     */
    void delete(String deviceId);
}
