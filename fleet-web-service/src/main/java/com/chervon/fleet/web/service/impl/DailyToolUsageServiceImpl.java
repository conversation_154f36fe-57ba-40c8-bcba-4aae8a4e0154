package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.DailyToolUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolAverageDailyUsageVo;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolEnergyConsumptionVo;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolRuntimeFrequencyVo;
import com.chervon.fleet.web.mapper.DailyToolUsageMapper;
import com.chervon.fleet.web.service.DailyToolUsageService;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.chervon.fleet.web.utils.ParseUtil.getDiffValue;
import static com.chervon.fleet.web.utils.ParseUtil.parseTup2;

/**
 * <AUTHOR>
 * @since 2023-08-14 17:29
 **/
@Service
@Slf4j
public class DailyToolUsageServiceImpl extends ServiceImpl<DailyToolUsageMapper, DailyToolUsage>
    implements DailyToolUsageService {

    /**
     * 获取设备在指定日期内的工具使用情况
     * @param req 请求参数
     * @return
     */
    @Override
    public EquipmentStatisticsToolAverageDailyUsageVo detailStatisticsToolAverageDailyUsage(StatisticsQueryDto req) {
        List<DailyToolUsage> dailyToolUsages = list(new LambdaQueryWrapper<DailyToolUsage>()
                .eq(DailyToolUsage::getDeviceId, req.getDeviceId())
                .eq(DailyToolUsage::getCompanyId, UserContext.getCompanyId())
                .ge(DailyToolUsage::getDate, req.getStart())
                .le(DailyToolUsage::getDate, req.getEnd()));
        if (CollectionUtils.isEmpty(dailyToolUsages)) {
            return null;
        }
        Integer durationSum = 0;
        Integer useNumberSum = 0;

        Integer daySum = dailyToolUsages.size();
        for (DailyToolUsage dailyToolUsage : dailyToolUsages) {
            durationSum += dailyToolUsage.getUsageDuration();
            useNumberSum += dailyToolUsage.getNumberTimes();
        }
        EquipmentStatisticsToolAverageDailyUsageVo result = new EquipmentStatisticsToolAverageDailyUsageVo();
        BigDecimal averageDailyDurationSeconds = daySum == 0 ? BigDecimal.ZERO : new BigDecimal(durationSum).divide(new BigDecimal(daySum),1, RoundingMode.HALF_UP);
        result.setAverageDailyDurationOfUse(averageDailyDurationSeconds.divide(new BigDecimal(NumberConst.ONE_HOUR_SECOND),1,RoundingMode.HALF_UP));

        BigDecimal averageDailyDurationOfSingleSeconds = useNumberSum == 0 ? BigDecimal.ZERO : new BigDecimal(durationSum).divide(new BigDecimal(useNumberSum),1, RoundingMode.HALF_UP);
        result.setAverageDailyDurationOfSingleUse(averageDailyDurationOfSingleSeconds.divide(new BigDecimal(NumberConst.ONE_MINUTE_SECOND),1,RoundingMode.HALF_UP));

        result.setAverageNumberOfDailyUses(daySum == 0 ? BigDecimal.ZERO : new BigDecimal(useNumberSum).divide(new BigDecimal(daySum),1, RoundingMode.HALF_UP));
        return result;
    }

    /**
     * 获取设备在指定日期内的工具使用情况
     * @param req 请求参数
     * @return
     */
    @Override
    public List<EquipmentStatisticsToolRuntimeFrequencyVo> detailStatisticsToolRuntimeFrequency(StatisticsQueryDto req) {
        Map<String, DailyToolUsage> dateToolUsageMap = getStringDailyToolUsageMap(req);
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(req.getStart(), req.getEnd());

        List<EquipmentStatisticsToolRuntimeFrequencyVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            DailyToolUsage dailyToolUsage = dateToolUsageMap.get(date);
            EquipmentStatisticsToolRuntimeFrequencyVo vo = new EquipmentStatisticsToolRuntimeFrequencyVo();
            if (null != dailyToolUsage) {
                //秒转分钟四舍五入保留整数
                final BigDecimal runTimeMinutes = new BigDecimal(dailyToolUsage.getUsageDuration()).divide(new BigDecimal(NumberConst.ONE_MINUTE_SECOND), 0, RoundingMode.HALF_UP);
                vo.setRuntime(runTimeMinutes.intValue());
                vo.setFrequency(dailyToolUsage.getNumberTimes());
            }
            vo.setDate(date);
            result.add(vo);
        }
        return result;
    }

    /**
     * 统计工具的能耗消耗
     * @param req 请求参数
     * @return
     */
    @Override
    public List<EquipmentStatisticsToolEnergyConsumptionVo> detailStatisticsToolEnergyConsumption(StatisticsQueryDto req) {
        Map<String, DailyToolUsage> dateToolUsageMap = getStringDailyToolUsageMap(req);
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(req.getStart(), req.getEnd());
        List<EquipmentStatisticsToolEnergyConsumptionVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            DailyToolUsage dailyToolUsage = dateToolUsageMap.get(date);
            EquipmentStatisticsToolEnergyConsumptionVo vo = new EquipmentStatisticsToolEnergyConsumptionVo();
            vo.setDate(date);
            if (null != dailyToolUsage) {
                vo.setPower(dailyToolUsage.getEnergyConsume());
            }
            result.add(vo);
        }
        return result;
    }

    /**
     * 查询DailyToolUsage并映射为日期的Map
     *
     * @param req 请求体
     * @return key-日期字符串 value-PO
     */
    @NotNull
    private Map<String, DailyToolUsage> getStringDailyToolUsageMap(StatisticsQueryDto req) {
        List<DailyToolUsage> dailyToolUsageList = list(new LambdaQueryWrapper<DailyToolUsage>()
                .eq(DailyToolUsage::getDeviceId, req.getDeviceId())
                .eq(DailyToolUsage::getCompanyId, UserContext.getCompanyId())
                .ge(DailyToolUsage::getDate, req.getStart())
                .le(DailyToolUsage::getDate, req.getEnd())
                .orderByAsc(DailyToolUsage::getDate));
        return dailyToolUsageList
                .stream().collect(Collectors.toMap(DailyToolUsage::getDate, a -> a));
    }

    /**
     * * 批量保存入库
     * @param listToolDailyUsageVo
     */
    public void saveDataDailyToolUsage(List<FleetToolDailyUsageVo> listToolDailyUsageVo) {
        if(CollectionUtils.isEmpty(listToolDailyUsageVo)){
            return;
        }
        List<DailyToolUsage> dailyToolUsageList=new ArrayList<>();
        for(FleetToolDailyUsageVo vo:listToolDailyUsageVo) {
            DailyToolUsage dailyToolUsage = new DailyToolUsage();
            dailyToolUsage.setCompanyId(vo.getCompanyId());
            dailyToolUsage.setCategoryCode(vo.getFirstCategoryCode());
            dailyToolUsage.setDeviceId(vo.getDeviceId());
            dailyToolUsage.setCreateTime(System.currentTimeMillis());
            dailyToolUsage.setModifyTime(System.currentTimeMillis());
            dailyToolUsage.setDate(vo.getDate());

            dailyToolUsage.setSpanUsageDuration(parseTup2(vo.getMin1016(),vo.getMax1016()));
            dailyToolUsage.setSpanNumberTimes(parseTup2(vo.getMin1018(),vo.getMax1018()));
            dailyToolUsage.setSpanEnergyConsume(parseTup2(vo.getMin1025(),vo.getMax1025()));

            dailyToolUsage.setUsageDuration(getDiffValue(vo.getMin1016(),vo.getMax1016()));
            dailyToolUsage.setNumberTimes(getDiffValue(vo.getMin1018(),vo.getMax1018()));
            dailyToolUsage.setEnergyConsume(getDiffValue(vo.getMin1025(),vo.getMax1025()));
            dailyToolUsage.setId();

            dailyToolUsageList.add(dailyToolUsage);
        }

        if(!CollectionUtils.isEmpty(dailyToolUsageList)){
            saveOrUpdateBatch(dailyToolUsageList);
        }
    }
}
