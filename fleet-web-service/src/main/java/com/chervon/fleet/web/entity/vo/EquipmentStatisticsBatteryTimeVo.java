package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description:每日电池包返回对象
 * <AUTHOR>
 * @date 2023/7/25 10:19
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsBatteryTimeVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public EquipmentStatisticsBatteryTimeVo() {
        this.chargingTime = 0;
        this.dischargingTime = 0;
    }

    @ApiModelProperty("日期yyyy-MM-dd字符串")
    private String date;

    @ApiModelProperty("Charging time")
    private Integer chargingTime;

    @ApiModelProperty("Discharging time")
    private Integer dischargingTime;

}
