package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.dto.MaintenanceDto;
import com.chervon.fleet.web.entity.po.MaintenancePlan;
import com.chervon.fleet.web.entity.vo.MaintenanceVo;

/**
 * 设备维保计划表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-15 19:15:52
 * @description 
 */
public interface MaintenancePlanService extends IService<MaintenancePlan> {

    /**
     * 维保详情
     * @param deviceId 设备id
     * @return 维保详情
     */
    MaintenanceVo maintenance(String deviceId);

    /**
     * 设备维保信息-编辑
     *
     * @param req 维保操作对象
     */
    void maintenanceEdit(MaintenanceDto req);
}
