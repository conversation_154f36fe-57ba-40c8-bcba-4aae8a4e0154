package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 地图网关对象
 * <AUTHOR>
 * @date 2023/7/19 11:20
 */
@Data
@ApiModel(description = "地图网关对象")
public class MapGatewayVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 网关id
     */
    @ApiModelProperty("网关id")
    private String gatewayId;

    @ApiModelProperty("网关名称")
    private String name;

    @ApiModelProperty("软网关类型：1固定网关   2移动网关")
    private Integer gatewayType;

    @ApiModelProperty("网关软硬件类型：1硬件网关   2软网关")
    private Integer type;

    @ApiModelProperty("硬件网关对应的设备id")
    private String deviceId;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("网关地址信息")
    private String location;

    @ApiModelProperty("网关上设备数量")
    private Integer deviceNumber;
}
