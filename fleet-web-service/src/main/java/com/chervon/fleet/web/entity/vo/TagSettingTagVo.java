package com.chervon.fleet.web.entity.vo;

import com.chervon.fleet.web.api.entity.vo.TagVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 打标签-标签对象
 * <AUTHOR>
 * @date 2023/7/12 15:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "打标签-标签对象")
public class TagSettingTagVo extends TagVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 选中设备中，已经有该标签的设备数量
     */
    @ApiModelProperty("选中设备中，已经有该标签的设备数量")
    private Long tagUsedNumber;
}
