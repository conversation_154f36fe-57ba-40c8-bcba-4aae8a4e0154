package com.chervon.fleet.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.fleet.web.service.AppCompanyUserDeviceAsyncService;
import com.chervon.iot.middle.api.service.RemoteDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/7/5 11:44
 */
@Slf4j
@Service
public class AppCompanyUserDeviceAsyncServiceImpl implements AppCompanyUserDeviceAsyncService {

    @DubboReference
    private RemoteDeviceService remoteDeviceService;

    @Override
    public void syncCompanyUserInfo2Device(Long companyId, Long userId, String deviceId) {
        Map<String, String> newAttributes = new ConcurrentHashMap<>();
        newAttributes.put("companyId", companyId + "");
        newAttributes.put("userId", userId + "");
        newAttributes.put("businessType", "2");
        if (CollectionUtil.isNotEmpty(newAttributes)) {
            remoteDeviceService.updateAttributes(deviceId, newAttributes);
        }
    }
}
