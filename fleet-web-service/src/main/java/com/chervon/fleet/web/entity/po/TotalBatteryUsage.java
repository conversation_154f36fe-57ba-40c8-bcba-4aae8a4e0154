package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-15 18:12
 **/
@Data
@TableName("t_data_total_battery_usage")
public class TotalBatteryUsage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 设备一级分类编号
     */
    private String categoryCode;
    /**
     * 设备总充电时长（单位：秒）
     */
    private Integer totalChargingTime;
    /**
     * 设备总充电次数
     */
    private Integer chargingCyclesNumber;
    /**
     * 设备总放电时长（单位：秒）
     */
    private Integer totalDischargingTime;
    /**
     * 更新时间
     */
    private Long modifyTime;
}
