package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiMaintenance;
import com.chervon.fleet.web.entity.vo.dashboard.MaintenanceDashboardVo;

import java.util.List;

/**
 * 设备维保状态数量统计表(看板：Maintenance & Service)服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
public interface BiMaintenanceService extends IService<BiMaintenance> {

    /**
     * 维保服务看板查询
     *
     * @param companyId 公司ID
     * @return 结果
     */
    List<MaintenanceDashboardVo> maintenance(Long companyId);
}
