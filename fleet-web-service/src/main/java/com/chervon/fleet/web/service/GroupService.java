package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.dto.GroupDto;
import com.chervon.fleet.web.entity.po.Group;
import com.chervon.fleet.web.api.entity.vo.GroupVo;

import java.util.List;

public interface GroupService extends IService<Group> {
    /**
     * 租户下的最大分组数量为100
     */
    Integer GROUP_MAX = 100;

    /**
     * 在租户创建的时候创建一个默认的分组
     *
     * @param companyId 租户id
     */
    void createDefaultGroup(Long companyId);

    /**
     * 查询租户下的所有分组
     *
     * @return 分组列表
     */
    List<GroupVo> listAll();

    /**
     * 创建分组
     *
     * @param req 分组对象
     */
    void add(GroupDto req);

    /**
     * 根据分组id，查询分组信息
     *
     * @param groupId 分组id
     * @return 分组信息
     */
    GroupVo detail(Long groupId);

    /**
     * 编辑分组
     *
     * @param req 分组对象
     */
    void edit(GroupDto req);

    /**
     * 删除分组
     *
     * @param groupId 分组id
     */
    void delete(Long groupId);
}
