package com.chervon.fleet.web.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * fleet状态通知topic消息类型枚举
 * topic消息业务类型：1设备库存状态变更  2设备在线离线状态变更  3设备数据上报完成云朵状态通知 4设备绑定解绑变更*
 * <AUTHOR> 2023/7/19
 */
public enum MqttMessageTypeEnum implements TypeEnum {
    /**
     * 设备库存状态变更
     */
    WAREHOUSE_STATUS_NOTICE(1, "设备库存状态变更"),
    /**
     * 设备在线离线状态变更
     */
    DEVICE_ONLINE_NOTICE(2, "设备在线离线状态变更"),
    /**
     * 设备数据上报完成云朵状态通知
     */
    DATA_REPORT_COMPLETE_NOTICE(3, "设备数据上报完成云朵状态通知"),
    /**
     * 设备绑定解绑变更
     */
    DEVICE_BIND_NOTICE(4, "设备绑定解绑变更"),
    ;

    private int type;
    private String desc;

    MqttMessageTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}