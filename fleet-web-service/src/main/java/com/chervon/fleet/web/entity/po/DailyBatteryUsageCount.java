package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 电池日使用量统计表(t_data_daily_battery_usage)实体类
 *
 * <AUTHOR>
 * @since 2023-08-28 17:04:04
 */
@Data
@NoArgsConstructor
public class DailyBatteryUsageCount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 电池二级分类编码
     */
    private String secondCategoryCode;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 日期：2023-07-12
     */
    private String date;
    /**
     * 总使用时长 1016*
     */
    private Integer dailyUsageTime;
    /**
     * 当日电池总使用次数：2034: 电池循环次数cycle
     */
    private Integer dailyNumberTimes;
    /**
     * 设备的使用数量*
     */
    private Integer deviceCount;
}