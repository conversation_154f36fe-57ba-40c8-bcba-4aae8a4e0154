package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: EquipmentStatisticsChargerUsageVo
 * @Description: 充电器使用情况统计vo
 * <AUTHOR>
 * @date 2023/7/25 10:04
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsChargerUsageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public EquipmentStatisticsChargerUsageVo() {
        this.totalEnergyOfCharging = 0;
        this.totalChargingTime = 0;
    }

    @ApiModelProperty("日期字符串")
    private String date;

    @ApiModelProperty("Total energy of charging")
    private Integer totalEnergyOfCharging;

    @ApiModelProperty("total charging time")
    private Integer totalChargingTime;
}
