package com.chervon.fleet.web.entity.vo;

import com.chervon.fleet.web.api.entity.vo.FleetWebCategoryVo;
import com.chervon.fleet.web.api.entity.vo.WebFilterGroupVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * web过滤条件
 * <AUTHOR>
 * @date 2023/8/17 13:38
 */
@Data
public class WebFilterConditionVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 库存状态选项
     */
    @ApiModelProperty(value = "库存状态选项")
    private List<WebStatusVo> warehouseStatus;

    @ApiModelProperty(value = "设备分类列表")
    private List<FleetWebCategoryVo> categories;

    @ApiModelProperty(value = "自定义标签列表")
    private List<WebFilterGroupVo> groups;

    @ApiModelProperty("在线状态")
    private List<WebStatusVo> onlineStatus;

    @ApiModelProperty("维保状态")
    private List<WebStatusVo> maintenanceStatus;
}
