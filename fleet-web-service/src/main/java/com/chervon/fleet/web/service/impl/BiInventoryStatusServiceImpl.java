package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.api.entity.vo.InventoryStatusDashboardVo;
import com.chervon.fleet.web.entity.po.BiInventoryStatus;
import com.chervon.fleet.web.mapper.BiInventoryStatusMapper;
import com.chervon.fleet.web.service.BiInventoryStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备库存状态统计表(看板：Inventory Status)服务接口实现
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiInventoryStatusServiceImpl extends ServiceImpl<BiInventoryStatusMapper, BiInventoryStatus>
    implements BiInventoryStatusService {

    @Override
    public List<InventoryStatusDashboardVo> inventoryStatus(Long companyId) {
        List<BiInventoryStatus> biInventoryStatusList = this.list(new LambdaQueryWrapper<BiInventoryStatus>()
            .eq(BiInventoryStatus::getCompanyId, companyId)
            .orderByAsc(BiInventoryStatus::getInventoryType));
        if (CollectionUtils.isEmpty(biInventoryStatusList)) {
            return new ArrayList<>();
        }
        return ConvertUtil.convertList(biInventoryStatusList, InventoryStatusDashboardVo.class);
    }
}