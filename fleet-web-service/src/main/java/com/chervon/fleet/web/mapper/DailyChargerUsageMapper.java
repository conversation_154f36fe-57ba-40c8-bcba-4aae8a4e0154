package com.chervon.fleet.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.fleet.web.entity.po.DailyChargerUsage;
import com.chervon.fleet.web.entity.po.DailyChargingCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-31 09:53
 **/
@Mapper
public interface DailyChargerUsageMapper extends BaseMapper<DailyChargerUsage> {
    /**
     * * 按日期和分类分组汇总统计各分类的设备使用情况
     * @param date
     * @return
     */
    List<DailyChargingCount> countDailyChargingByDate(@Param("date") String date, @Param("companyId")Long companyId);
}
