package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备维保计划表(t_maintenance_plan)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-15 19:15:52
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_maintenance_plan")
public class MaintenancePlan extends Model<MaintenancePlan> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 一级分类编码
     */
    private String firstCategoryCode;
    /**
     * 维保类型：1延保日期  2使用工时  3关闭
     */
    private Integer planType;
    /**
     * 截止时间
     */
    private Long deadlineTime;
    /**
     * 使用工时小时数计划数
     */
    private Integer usageHour;
    /**
     * 计划开始时间
     */
    private Long planBegin;
    /**
     * 维保计划开始时已使用的工时数（单位: 秒）
     */
    private Integer workHoursUsed;

}