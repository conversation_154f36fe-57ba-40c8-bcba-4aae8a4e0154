package com.chervon.fleet.web.service;

import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;

public interface FilterConditionService {

    /**
     * 筛选项
     *
     * @param companyId   租户id
     * @param warehouse   库存
     * @param category    品类
     * @param tag         标签
     * @param online      在线离线
     * @param maintenance 维保
     * @param search      搜索名称
     * @return 筛选项
     */
    FilterConditionVo filterCondition(Long companyId, boolean warehouse, boolean category, boolean tag, boolean online, boolean maintenance, String search);
}
