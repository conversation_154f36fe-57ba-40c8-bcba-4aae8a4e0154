package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.po.DailyBatteryUsage;
import com.chervon.fleet.web.entity.po.DailyChargerUsage;
import com.chervon.fleet.web.entity.po.DailyToolUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsChargerUsageVo;
import com.chervon.fleet.web.mapper.DailyChargerUsageMapper;
import com.chervon.fleet.web.service.DailyChargerUsageService;
import com.chervon.iot.middle.api.vo.usage.FleetChargerDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.chervon.fleet.web.utils.ParseUtil.getDiffValue;
import static com.chervon.fleet.web.utils.ParseUtil.parseTup2;

/**
 * <AUTHOR>
 * @since 2023-08-31 09:54
 **/
@Service
@Slf4j
public class DailyChargerUsageServiceImpl extends ServiceImpl<DailyChargerUsageMapper, DailyChargerUsage>
    implements DailyChargerUsageService {
    /**
     * 查询充电器使用统计信息
     * @param req 请求对象
     * @return
     */
    @Override
    public List<EquipmentStatisticsChargerUsageVo> detailStatisticsChargerUsage(StatisticsQueryDto req) {

        List<DailyChargerUsage> dailyChargerUsageList = list(new LambdaQueryWrapper<DailyChargerUsage>()
                .eq(DailyChargerUsage::getDeviceId, req.getDeviceId())
                .eq(DailyChargerUsage::getCompanyId, UserContext.getCompanyId())
                .eq(DailyChargerUsage::getStatus,0)
                .ge(DailyChargerUsage::getDate, req.getStart())
                .le(DailyChargerUsage::getDate, req.getEnd())
                .orderByAsc(DailyChargerUsage::getDate));
        Map<String, DailyChargerUsage> dateChargerUsageMap = dailyChargerUsageList
                .stream().collect(Collectors.toMap(DailyChargerUsage::getDate, a -> a));
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(req.getStart(), req.getEnd());

        List<EquipmentStatisticsChargerUsageVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            DailyChargerUsage dailyChargerUsage = dateChargerUsageMap.get(date);
            EquipmentStatisticsChargerUsageVo vo = new EquipmentStatisticsChargerUsageVo();
            vo.setDate(date);
            if (null != dailyChargerUsage) {
                vo.setTotalChargingTime(dailyChargerUsage.getChargingTime()/ NumberConst.ONE_MINUTE_SECOND);
                vo.setTotalEnergyOfCharging(dailyChargerUsage.getChargingEnergy());
            }
            result.add(vo);
        }
        return result;
    }

    /**
     * * 批量保存入库
     * @param listChargerDailyUsageVo
     */
    public void saveDataDailyChargerUsage(List<FleetChargerDailyUsageVo> listChargerDailyUsageVo) {
        if(CollectionUtils.isEmpty(listChargerDailyUsageVo)){
            return;
        }
        List<DailyChargerUsage> list=new ArrayList<>();
        for(FleetChargerDailyUsageVo vo:listChargerDailyUsageVo) {
            DailyChargerUsage dataDailyCharging=new DailyChargerUsage();
            dataDailyCharging.setCompanyId(vo.getCompanyId());
            dataDailyCharging.setCategoryCode(vo.getFirstCategoryCode());
            dataDailyCharging.setSecondCategoryCode(vo.getSecondCategoryCode());
            dataDailyCharging.setDeviceId(vo.getDeviceId());
            dataDailyCharging.setCreateTime(System.currentTimeMillis());
            dataDailyCharging.setModifyTime(System.currentTimeMillis());
            dataDailyCharging.setDate(vo.getDate());
            dataDailyCharging.setChargingEnergy(getDiffValue(vo.getMin2027(),vo.getMax2027()));
            dataDailyCharging.setChargingTime(getDiffValue(vo.getMinChargeTime(),vo.getMaxChargeTime()));

            dataDailyCharging.setSpanChargingEnergy(parseTup2(vo.getMin2027(),vo.getMax2027()));
            dataDailyCharging.setSpanChargingTime(parseTup2(vo.getMinChargeTime(),vo.getMaxChargeTime()));
            dataDailyCharging.setStatus(0);
            dataDailyCharging.setId();

            list.add(dataDailyCharging);
        }

        if(!CollectionUtils.isEmpty(list)){
            saveOrUpdateBatch(list);
        }
    }
}
