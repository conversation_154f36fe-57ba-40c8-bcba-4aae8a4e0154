package com.chervon.fleet.web.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.WarehouseStatusCache;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 离线超12小时刷新仓库状态JOB任务
 * <AUTHOR>
 * @date 2023/2/25 20:08
 */
@Component
@Slf4j
@AllArgsConstructor
public class RefreshWarehouseStatusJob {
    /**
     * 美国展会使用租户id：排除
     */
    private static final Long EXHIBITION_TENANT_ID = 429236286952308738L;

    private final CompanyDeviceService companyDeviceService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    /**
     * 设备库存状态 12个小时候后变成UNKNOWN_LOCATION
     */
    @XxlJob("refreshWarehouseStatusJob")
    public void refreshWarehouseStatusJob() {
        long now = System.currentTimeMillis();
        // 查询租户设备数据
        List<CompanyDevice> companyDevices = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                //排除美国展会租户自动刷新状态
                .ne(CompanyDevice::getCompanyId, EXHIBITION_TENANT_ID));
        List<ProductCache> productCaches = remoteOperationCacheService.listProducts(companyDevices.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity(), (k1, k2) -> k2));
        // 按租户分组
        Map<Long, List<CompanyDevice>> companyDeviceMap = companyDevices.stream().collect(Collectors.groupingBy(CompanyDevice::getCompanyId));
        for (Map.Entry<Long, List<CompanyDevice>> entry : companyDeviceMap.entrySet()) {
            Long k = entry.getKey();
            List<CompanyDevice> companyDeviceList = entry.getValue();
            List<String> deviceIds = new ArrayList<>();
            updateCache(companyDeviceList, productCacheMap, now, deviceIds);
            if (CollectionUtils.isEmpty(deviceIds)) {
                continue;
            }
            companyDeviceService.update(new CompanyDevice(), new LambdaUpdateWrapper<CompanyDevice>()
                    .eq(CompanyDevice::getCompanyId, k)
                    .in(CompanyDevice::getDeviceId, deviceIds)
                    .set(CompanyDevice::getGatewayId, null)
                    .set(CompanyDevice::getWarehouseStatus, WarehouseStatusEnum.UNKNOWN_CURRENT_LOCATION.getType()));

        }
    }

    private static void updateCache(List<CompanyDevice> companyDeviceList, Map<Long, ProductCache> productCacheMap, long now, List<String> deviceIds) {
        for (CompanyDevice device : companyDeviceList) {
            //读取设备状态缓存key
            final String deviceWarehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(device.getDeviceId());
            //设备库存状态
            WarehouseStatusCache warehouseStatusCache = RedisUtils.getCacheObject(deviceWarehouseStatusKey);
            if (Objects.isNull(warehouseStatusCache)) {
                return;
            }
            ProductCache product = productCacheMap.get(device.getProductId());
            if (product == null || !StringConst.GATEWAY_DEVICE.equals(product.getType())) {
                Long reportTime = warehouseStatusCache.getReportTime();
                if (reportTime != null && (now - reportTime) > NumberConst.TIMESPAN_12_HOURS_MS) {
                    // 间隔时间超过12个小时
                    warehouseStatusCache.setWarehouseStatus(WarehouseStatusEnum.UNKNOWN_CURRENT_LOCATION.getType());
                    RedisUtils.setCacheObject(deviceWarehouseStatusKey, warehouseStatusCache);
                    deviceIds.add(device.getDeviceId());
                }
            }

        }
    }

}