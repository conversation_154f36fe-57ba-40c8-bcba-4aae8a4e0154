package com.chervon.fleet.web.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * fleet网关上报扫描到的设备缓存结构信息
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@Accessors(chain = true)
@ApiModel("fleet设备维度的库存状态信息")
public class DeviceStatusSubBody implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 设备库存状态：0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location
     * @see com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum
     */
    @ApiModelProperty(value = "设备库存状态：0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location")
    private Integer warehouseStatus;

    @ApiModelProperty(value = "蓝牙信号强度:大约0~-96dBm")
    private Integer rssi;
}
