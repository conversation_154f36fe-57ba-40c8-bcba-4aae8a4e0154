package com.chervon.fleet.web.service.impl;

import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.entity.I18nEnumAttribute;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;
import com.chervon.fleet.web.api.entity.vo.FleetWebCategoryVo;
import com.chervon.fleet.web.api.entity.vo.WebFilterGroupVo;
import com.chervon.fleet.web.entity.vo.WebFilterConditionVo;
import com.chervon.fleet.web.entity.vo.WebStatusVo;
import com.chervon.fleet.web.service.FilterConditionService;
import com.chervon.fleet.web.service.WebFilterConditionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/17 14:13
 */
@Service
@Slf4j
@AllArgsConstructor
public class WebFilterConditionServiceImpl implements WebFilterConditionService {

    @Lazy
    private final FilterConditionService filterConditionService;

    @Override
    public WebFilterConditionVo filterCondition(Long companyId, boolean warehouse, boolean category, boolean tag, boolean online, boolean maintenance, String search) {
        FilterConditionVo condition = filterConditionService.filterCondition(companyId, true, true, true, true, true, search);
        WebFilterConditionVo res = new WebFilterConditionVo();
        if (condition.getCategories() != null) {
            res.setCategories(condition.getCategories().stream().map(e -> {
                FleetWebCategoryVo vo = new FleetWebCategoryVo();
                vo.setCategoryCode(e.getCategoryCode());
                vo.setCategoryName(e.getCategoryName());
                vo.setChildren(e.getChildren());
                vo.setLevel(e.getLevel());
                return vo;
            }).collect(Collectors.toList()));
        }
        if (condition.getGroups() != null) {
            res.setGroups(condition.getGroups().stream().map(e -> {
                WebFilterGroupVo vo = new WebFilterGroupVo();
                vo.setGroupId(e.getGroupId());
                vo.setGroupName(e.getGroupName());
                vo.setIsDefault(e.getIsDefault());
                vo.setTags(e.getTags());
                return vo;
            }).collect(Collectors.toList()));
        }
        if (condition.getWarehouseStatus() != null) {
            List<I18nEnumAttribute> warehouseStatusEnum = I18nController.getEnum(UserContext.getLanguage(), "WarehouseStatusEnum");
            Map<String, I18nEnumAttribute> warehouseStatusEnumMap = warehouseStatusEnum.stream()
                    .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
            res.setWarehouseStatus(condition.getWarehouseStatus().stream().map(e -> {
                WebStatusVo vo = new WebStatusVo();
                vo.setKey(e);
                vo.setValue(Optional.ofNullable(warehouseStatusEnumMap.get(e + "")).orElse(new I18nEnumAttribute()).getDesc());
                return vo;
            }).collect(Collectors.toList()));
        }
        if (condition.getOnlineStatus() != null) {
            List<I18nEnumAttribute> fleetDeviceOnlineStatusEnum = I18nController.getEnum(UserContext.getLanguage(), "FleetDeviceOnlineStatusEnum");
            Map<String, I18nEnumAttribute> fleetDeviceOnlineStatusEnumMap = fleetDeviceOnlineStatusEnum.stream()
                    .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
            res.setOnlineStatus(condition.getOnlineStatus().stream().map(e -> {
                WebStatusVo vo = new WebStatusVo();
                vo.setKey(e);
                vo.setValue(Optional.ofNullable(fleetDeviceOnlineStatusEnumMap.get(e + "")).orElse(new I18nEnumAttribute()).getDesc());
                return vo;
            }).collect(Collectors.toList()));
        }
        if (condition.getMaintenanceStatus() != null) {
            List<I18nEnumAttribute> maintenanceStatusEnum = I18nController.getEnum(UserContext.getLanguage(), "MaintenanceStatusEnum");
            Map<String, I18nEnumAttribute> maintenanceStatusEnumMap = maintenanceStatusEnum.stream()
                    .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
            res.setMaintenanceStatus(condition.getMaintenanceStatus().stream().map(e -> {
                WebStatusVo vo = new WebStatusVo();
                vo.setKey(e);
                vo.setValue(Optional.ofNullable(maintenanceStatusEnumMap.get(e + "")).orElse(new I18nEnumAttribute()).getDesc());
                return vo;
            }).collect(Collectors.toList()));
        }
        return res;
    }
}
