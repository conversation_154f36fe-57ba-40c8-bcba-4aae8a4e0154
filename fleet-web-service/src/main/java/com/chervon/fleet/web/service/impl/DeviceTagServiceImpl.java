package com.chervon.fleet.web.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.error.GroupTagErrorCodeEnum;
import com.chervon.fleet.web.api.entity.vo.GroupVo;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.EquipmentSearchDto;
import com.chervon.fleet.web.entity.dto.TagSettingDto;
import com.chervon.fleet.web.entity.dto.TagSettingTagListDto;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.entity.po.Group;
import com.chervon.fleet.web.entity.po.Tag;
import com.chervon.fleet.web.entity.vo.TagSettingTagListVo;
import com.chervon.fleet.web.entity.vo.TagSettingTagVo;
import com.chervon.fleet.web.mapper.DeviceTagMapper;
import com.chervon.fleet.web.service.DeviceTagService;
import com.chervon.fleet.web.service.EquipmentService;
import com.chervon.fleet.web.service.GroupService;
import com.chervon.fleet.web.service.TagService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备标签服务实现类
 * <AUTHOR>
 * @date 2023/7/12 16:13
 */
@AllArgsConstructor
@Service
@Slf4j
public class DeviceTagServiceImpl extends ServiceImpl<DeviceTagMapper, DeviceTag> implements DeviceTagService {

    /**
     * 根据设备id和租户id查询设备标签
     * @param deviceId  设备id
     * @param companyId 租户id
     */
    @Override
    public void removeByDeviceIdAndCompany(String deviceId, Long companyId) {
        this.remove(new LambdaQueryWrapper<DeviceTag>().eq(DeviceTag::getDeviceId, deviceId).eq(DeviceTag::getCompanyId, companyId));
    }

    /**
     * 根据设备id查询设备标签
     * @param deviceId 设备id
     * @return
     */
    @Override
    public List<TagVo> tagList(String deviceId) {
        Long companyId = UserContext.getCompanyId();
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        List<DeviceTag> deviceTags = list(new LambdaQueryWrapper<DeviceTag>()
                .eq(DeviceTag::getDeviceId, deviceId).eq(DeviceTag::getCompanyId, companyId)
                .select(DeviceTag::getTagId)
                .orderByDesc(DeviceTag::getCreateTime));
        if (deviceTags == null) {
            return new ArrayList<>();
        }
        List<Long> tagIds = deviceTags.stream().map(DeviceTag::getTagId).distinct().collect(Collectors.toList());
        if (tagIds.isEmpty()) {
            return new ArrayList<>();
        }
        TagService tagService= SpringUtil.getBean(TagService.class);
        List<Tag> tags = tagService.listByIds(tagIds);
        int size = tags.size();
        return tags.stream().map(e -> {
            TagVo vo = new TagVo();
            vo.setTagId(e.getId()).setTagName(e.getName());
            return vo;
        }).sorted((o1, o2) -> {
            return sort(o1, o2, tagIds, size);
        }).collect(Collectors.toList());

    }

    private static int sort(TagVo o1, TagVo o2, List<Long> tagIds, int size) {
        int io1 = tagIds.indexOf(o1.getTagId());
        int io2 = tagIds.indexOf(o2.getTagId());
        if (io1 != -1) {
            io1 = size - io1;
        }
        if (io2 != -1) {
            io2 = size - io2;
        }
        return io2 - io1;
    }

    /**
     * 根据设备id查询设备分组
     * @param deviceIds 设备id集合
     * @return
     */
    @Override
    public List<GroupVo> tagSettingGroupList(List<String> deviceIds) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(deviceIds, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.notEmpty(deviceIds, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.noNullElements(deviceIds, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        List<DeviceTag> deviceTags = list(new LambdaQueryWrapper<DeviceTag>()
                .in(DeviceTag::getDeviceId, deviceIds).eq(DeviceTag::getCompanyId, companyId));
        // 如果没有设备标签，或总数小于设备标签最大数，则返回所有分组
        if (deviceTags == null || deviceTags.stream().map(DeviceTag::getTagId).count() < EquipmentService.DEVICE_TAG_MAX) {
            return getAllGroup(companyId);
        }
        Map<String, List<DeviceTag>> deviceIdGroup = deviceTags.stream().collect(Collectors.groupingBy(DeviceTag::getDeviceId));
        // 判断标签数量是否达到最大值
        Map<String, List<DeviceTag>> deviceIdGroupMax = new ConcurrentHashMap<>();
        deviceIdGroup.forEach((k, v) -> {
            if (v.size() >= EquipmentService.DEVICE_TAG_MAX) {
                deviceIdGroupMax.put(k, v);
            }
        });
        // 如果max为空，则获取所有分组
        if (deviceIdGroupMax.isEmpty()) {
            return getAllGroup(companyId);
        }
        // 如果max不为空，则在max中找到交集
        Map<Long, Long> tagIdMaxCountMap = deviceIdGroupMax.values().stream()
                .flatMap(Collection::stream).map(DeviceTag::getTagId)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        tagIdMaxCountMap.values().removeIf(e -> e < deviceIdGroupMax.size());
        // 如果没有交集，则返回空
        if (tagIdMaxCountMap.isEmpty()) {
            return new ArrayList<>();
        }
        // 如果有交集，则返回交集的group列表
        Set<Long> groupIdSet = deviceTags.stream()
                .filter(e -> tagIdMaxCountMap.containsKey(e.getTagId())).map(DeviceTag::getGroupId)
                .collect(Collectors.toSet());
        GroupService groupService= SpringUtil.getBean(GroupService.class);
        List<Group> groups = groupService.list(new LambdaQueryWrapper<Group>()
                .in(Group::getId, groupIdSet)
                .orderByDesc(Group::getIsDefault).orderByDesc(Group::getCreateTime));
        return groups.stream().map(e -> {
            GroupVo vo = new GroupVo();
            vo.setGroupId(e.getId()).setGroupName(e.getName()).setIsDefault(e.getIsDefault());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有分组
     * @param companyId
     * @return
     */
    private List<GroupVo> getAllGroup(Long companyId) {
        GroupService groupService = SpringUtil.getBean(GroupService.class);
        List<Group> groups = groupService.list(new LambdaQueryWrapper<Group>()
                .eq(Group::getCompanyId, companyId)
                .orderByDesc(Group::getIsDefault).orderByDesc(Group::getCreateTime));
        return groups.stream().map(e -> {
            GroupVo vo = new GroupVo();
            vo.setGroupId(e.getId()).setGroupName(e.getName()).setIsDefault(e.getIsDefault());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取设备标签列表
     * @param req 查询条件
     * @return
     */
    @Override
    public TagSettingTagListVo tagSettingTagList(TagSettingTagListDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "tagSettingTagListDto");
        Assert.notNull(req.getDeviceIds(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.notEmpty(req.getDeviceIds(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.noNullElements(req.getDeviceIds(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.notNull(req.getGroupId(), ErrorCode.PARAMETER_NOT_PROVIDED, "groupId");
        List<DeviceTag> deviceTags = list(new LambdaQueryWrapper<DeviceTag>()
                .in(DeviceTag::getDeviceId, req.getDeviceIds()).eq(DeviceTag::getCompanyId, companyId).orderByDesc(DeviceTag::getCreateTime));
        // 如果设备标签为空，或者总数小于设备标签最大数，则返回该分组的所有的标签
        if (deviceTags == null || deviceTags.stream().map(DeviceTag::getTagId).count() < EquipmentService.DEVICE_TAG_MAX) {
            return handleTagSettingTags(true, req.getGroupId(), null, deviceTags, req.getDeviceIds().size());
        }
        Map<String, List<DeviceTag>> deviceIdGroup = deviceTags.stream().collect(Collectors.groupingBy(DeviceTag::getDeviceId));
        // 判断标签数量是否达到最大值
        Map<String, List<DeviceTag>> deviceIdGroupMax = new ConcurrentHashMap<>();
        deviceIdGroup.forEach((k, v) -> {
            if (v.size() >= EquipmentService.DEVICE_TAG_MAX) {
                deviceIdGroupMax.put(k, v);
            }
        });
        // 如果max为空，则返回该分组的所有的标签
        if (deviceIdGroupMax.isEmpty()) {
            return handleTagSettingTags(true, req.getGroupId(), null, deviceTags, req.getDeviceIds().size());
        }
        // 如果max不为空，则在max中找到交集
        Map<Long, Long> tagIdMaxCountMap = deviceIdGroupMax.values().stream()
                .flatMap(Collection::stream).map(DeviceTag::getTagId)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        tagIdMaxCountMap.values().removeIf(e -> e < deviceIdGroupMax.size());
        // 如果没有交集，则返回空
        if (tagIdMaxCountMap.isEmpty()) {
            return new TagSettingTagListVo().setDeviceSelectNumber(req.getDeviceIds().size());
        }
        // 如果有交集，则返回交集的该分组的标签列表
        return handleTagSettingTags(false, req.getGroupId(), tagIdMaxCountMap.keySet(), deviceTags, req.getDeviceIds().size());
    }

    /**
     * 处理标签设置标签列表
     * @param selected
     * @param targetGroupId
     * @param targetTagIds
     * @param deviceTags
     * @param deviceIdsSize
     * @return
     */
    private TagSettingTagListVo handleTagSettingTags(boolean selected, Long targetGroupId, Set<Long> targetTagIds, List<DeviceTag> deviceTags, Integer deviceIdsSize) {
        deviceTags = deviceTags == null ? new ArrayList<>() : deviceTags;
        Map<Long, Long> tagSelectMap = deviceTags.stream().collect(Collectors.groupingBy(DeviceTag::getTagId, Collectors.counting()));
        TagSettingTagListVo res = new TagSettingTagListVo();
        res.setDeviceSelectNumber(deviceIdsSize);
        GroupService groupService = SpringUtil.getBean(GroupService.class);
        Group group = groupService.getById(targetGroupId);
        res.setGroupId(group.getId());
        res.setGroupName(group.getName());
        TagService tagService = SpringUtil.getBean(TagService.class);
        List<Tag> tags = tagService.list(new LambdaQueryWrapper<Tag>().eq(Tag::getGroupId, targetGroupId).orderByDesc(Tag::getCreateTime));
        if (!selected) {
            tags.removeIf(e -> !targetTagIds.contains(e.getId()));
        }
        res.setTags(tags.stream().map(e -> {
            TagSettingTagVo vo = new TagSettingTagVo();
            vo.setTagId(e.getId()).setTagName(e.getName());
            vo.setTagUsedNumber(tagSelectMap.getOrDefault(e.getId(), 0L));
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    /**
     * 处理标签设置请求
     * @param req 操作对象
     */
    @Override
    public void tagSetting(TagSettingDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "tagSettingDto");
        Assert.notNull(req.getDeviceIds(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.notEmpty(req.getDeviceIds(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.noNullElements(req.getDeviceIds(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_IDS);
        Assert.notNull(req.getTagId(), ErrorCode.PARAMETER_NOT_PROVIDED, "tagId");
        Assert.notNull(req.getAddOrRemove(), ErrorCode.PARAMETER_NOT_PROVIDED, "addOrRemove");
        if (!Arrays.asList(CommonConstant.ONE,CommonConstant.TWO).contains(req.getAddOrRemove())) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, "addOrRemove");
        }
        TagService tagService = SpringUtil.getBean(TagService.class);
        Tag tag = tagService.getById(req.getTagId());
        if (tag == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.TAG_NOT_EXIST);
        }
        if (req.getAddOrRemove().equals(CommonConstant.ONE)) {
            tagSettingAdd(req.getDeviceIds(), tag, companyId);
        } else {
            remove(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .eq(DeviceTag::getTagId, tag.getId())
                    .in(DeviceTag::getDeviceId, req.getDeviceIds()));
        }
    }

    /**
     *  设备标签设置
     * @param deviceIds
     * @param tag
     * @param companyId
     */
    private void tagSettingAdd(List<String> deviceIds, Tag tag, Long companyId) {
        List<DeviceTag> existList = list(new LambdaQueryWrapper<DeviceTag>()
                .in(DeviceTag::getDeviceId, deviceIds)
                .eq(DeviceTag::getTagId, tag.getId())
                .eq(DeviceTag::getCompanyId, companyId));
        List<String> existDeviceIds = existList.stream().map(DeviceTag::getDeviceId).collect(Collectors.toList());
        List<DeviceTag> addData = deviceIds.stream().filter(e -> !existDeviceIds.contains(e)).map(e -> {
            DeviceTag deviceTag = new DeviceTag();
            deviceTag.setTagId(tag.getId()).setCompanyId(companyId).setGroupId(tag.getGroupId()).setDeviceId(e);
            return deviceTag;
        }).collect(Collectors.toList());
        if (!addData.isEmpty()) {
            saveBatch(addData);
        }
    }
    @Override
    public void filterTag(EquipmentSearchDto req, List<CompanyDevice> list, Long companyId) {
        if (!list.isEmpty() && !CollectionUtils.isEmpty(req.getTagIds())) {
            List<DeviceTag> deviceTags = list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getTagId, req.getTagIds())
                    .in(DeviceTag::getDeviceId, list.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList())));
            List<String> restDeviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            list.removeIf(e -> !restDeviceIds.contains(e.getDeviceId()));
        }
    }

}
