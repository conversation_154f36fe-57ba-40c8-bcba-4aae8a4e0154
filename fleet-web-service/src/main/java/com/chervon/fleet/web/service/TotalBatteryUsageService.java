package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.po.TotalBatteryUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsBatteryVo;

/**
 * <AUTHOR>
 * @since 2023-08-15 18:14
 **/
public interface TotalBatteryUsageService extends IService<TotalBatteryUsage> {
    /**
     * 详情-statistics-电池
     *
     * @param deviceId 设备id
     * @return 看板数据
     */
    EquipmentStatisticsBatteryVo detailStatisticsBattery(String deviceId);
}
