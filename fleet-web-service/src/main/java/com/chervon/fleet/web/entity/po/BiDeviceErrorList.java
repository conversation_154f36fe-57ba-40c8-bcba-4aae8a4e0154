package com.chervon.fleet.web.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;

import com.chervon.common.web.annotation.Translate;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 设备故障列表（看板：Error List）(t_bi_device_error_list)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_device_error_list")
public class BiDeviceErrorList extends Model<BiDeviceErrorList> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 设备分类
     */
    private String deviceCategory;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 错误编码
     */
    @Translate(adapter = "faultCode",targetField = {"errorMessage","suggestionContent"})
    private String errorCode;
    /**
     * 错误内容
     */
    private String errorMessage;
    /**
     * 处理建议内容
     */
    @TableField(exist = false)
    private String suggestionContent;
    /**
     * 更新时间
     */
    private Date modifyTime;

}