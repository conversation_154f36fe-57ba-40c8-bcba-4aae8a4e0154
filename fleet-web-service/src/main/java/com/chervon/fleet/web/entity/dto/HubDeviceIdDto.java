package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *  PowerHub设备列表Id对象
 * <AUTHOR>
 * @date 2023/7/11 16:27
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "PowerHub设备列表对象")
public class HubDeviceIdDto implements Serializable {
    private static final long serialVersionUID = 1L;
    public HubDeviceIdDto() {
        this.listChargerId = new ArrayList<>();
        this.listBatteryId = new ArrayList<>();
    }

    @ApiModelProperty("hub设备Id")
    private String hubDeviceId;

    @ApiModelProperty("充电器和电源适配器id")
    private List<String> listChargerId;

    @ApiModelProperty("电池包id")
    private List<String> listBatteryId;

    public List<String> getMountDevice(){
        List<String> allDevice = new ArrayList<>();
        allDevice.addAll(this.listChargerId);
        allDevice.addAll(this.listBatteryId);
        return allDevice;
    }

    public List<String> getAllDevice(){
        List<String> allDevice = new ArrayList<>();
        allDevice.add(hubDeviceId);
        allDevice.addAll(this.listChargerId);
        allDevice.addAll(this.listBatteryId);
        return allDevice;
    }

}
