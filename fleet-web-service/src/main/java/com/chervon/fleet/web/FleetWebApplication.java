package com.chervon.fleet.web;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Spring Boot Starter
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.chervon.common","com.chervon.fleet"})
@EnableDubbo
@EnableAsync
public class FleetWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(FleetWebApplication.class, args);
    }
}
