package com.chervon.fleet.web.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/20 10:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class GatewayPageDto extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty("获取网关列表，参数type：1 charger gateway 2 app gateway-fix 3 app gateway-mobile")
    private Integer type;
}
