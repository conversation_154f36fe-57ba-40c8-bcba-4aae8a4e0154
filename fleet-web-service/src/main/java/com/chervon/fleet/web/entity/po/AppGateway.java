package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * app软网关表(t_app_gateway)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-25 10:37:56
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_app_gateway")
public class AppGateway extends Model<AppGateway> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private String id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 网关名称
     */
    private String name;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 软网关类型：1固定网关   2移动网关
     */
    private Integer gatewayType;
    /**
     * 网关软硬件类型：1硬件网关   2软网关
     */
    private Integer type;
    /**
     * 硬件网关对应的设备id
     */
    private String deviceId;
    /**
     * 位置坐标，逗号分隔：纬度，经度
     */
    private String coordinate;
    /**
     * 网关地址信息
     */
    private String location;
    /**
     * 手机唯一码
     */
    private String uniqueId;
    /**
     * 网关在线状态: 1-online   0-offline
     */
    private Integer onlineStatus;
    /**
     * 设备品牌：samsung
     */
    private String deviceBrand;
    /**
     * 设备型号
     */
    private String deviceModel;
    /**
     * 系统版本
     */
    private String deviceSysVersion;
    /**
     * app应用版本
     */
    private String appVersion;
    /**
     * 最后一次连接时间
     */
    private Long lastConnectedTime;
    /**
     * 是由有效 1 有效 0 无效
     */
    private Integer active;

}