package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/24 16:51
 */
@Data
public class StatisticsQueryDto implements Serializable {

    private static final long serialVersionUID = 1;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("开始时间,yyyy-MM-dd字符串")
    private String start;

    @ApiModelProperty("结束时间,yyyy-MM-dd字符串")
    private String end;
}
