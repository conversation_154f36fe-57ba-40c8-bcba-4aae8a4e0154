package com.chervon.fleet.web.config;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.chervon.common.web.util.UserContext;
import com.chervon.idgenerator.util.IdUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * 自动填充处理器
 *
 * <AUTHOR> 2023/6/29
 */
@Primary
@Component
public class AutoFillHandler implements MetaObjectHandler {
    private static final String ID = "id";
    private static final String MODIFY_TIME = "modifyTime";
    private static final String MODIFIER = "modifier";
    private static final String STRING_CLASS="java.lang.String";

    @Override
    public void insertFill(MetaObject metaObject) {
        if (metaObject.hasSetter(ID)) {
            Object idValue = metaObject.getValue(ID);
            if (idValue == null) {
                String idSetterClass = JSON.toJSONString(metaObject.getSetterType(ID));
                if (STRING_CLASS.equals(idSetterClass)) {
                    setFieldValByName(ID, IdUtils.snowFlakeNext() + "", metaObject);
                } else {
                    setFieldValByName(ID, IdUtils.snowFlakeNext(), metaObject);
                }
            }
        }
        final boolean hasCreateTime = metaObject.hasSetter("createTime");
        final boolean hasCreator = metaObject.hasSetter("creator");
        final boolean hasModifyTime = metaObject.hasSetter(MODIFY_TIME);
        final boolean hasModifier = metaObject.hasSetter(MODIFIER);
        final boolean hasIsDeleted = metaObject.hasSetter("isDeleted");
        if (hasCreateTime) {
            setFieldValByName("createTime", System.currentTimeMillis(), metaObject);
        }
        if (hasCreator) {
            setFieldValByName("creator", UserContext.getUserName(), metaObject);
        }
        if (hasModifier) {
            setFieldValByName(MODIFIER, UserContext.getUserName(), metaObject);
        }
        if (hasModifyTime) {
            setFieldValByName(MODIFY_TIME, System.currentTimeMillis(), metaObject);
        }
        if (hasIsDeleted) {
            setFieldValByName("isDeleted", 0, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        final boolean hasUpdateTime = metaObject.hasSetter("updateTime");
        final boolean hasModifyTime = metaObject.hasSetter(MODIFY_TIME);
        final boolean hasModifier = metaObject.hasSetter(MODIFIER);
        if (hasUpdateTime) {
            setFieldValByName("updateTime", System.currentTimeMillis(), metaObject);
        }
        if (hasModifyTime) {
            setFieldValByName(MODIFY_TIME, System.currentTimeMillis(), metaObject);
        }
        if (hasModifier) {
            setFieldValByName(MODIFIER, UserContext.getUserName(), metaObject);
        }
    }
}
