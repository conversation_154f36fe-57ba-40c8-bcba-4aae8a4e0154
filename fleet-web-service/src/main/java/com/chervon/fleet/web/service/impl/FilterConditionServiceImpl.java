package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;
import com.chervon.fleet.web.api.entity.vo.FleetCategoryVo;
import com.chervon.fleet.web.api.entity.vo.GroupVo;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.entity.po.Group;
import com.chervon.fleet.web.entity.po.Tag;
import com.chervon.fleet.web.service.*;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName FilterConditionServiceImpl
 * <AUTHOR>
 * @date 2023/7/19 11:39
 */
@AllArgsConstructor
@Service
@Slf4j
public class FilterConditionServiceImpl implements FilterConditionService {
    private CompanyDeviceService companyDeviceService;
    private final DeviceTagService deviceTagService;
    private final GroupService groupService;
    private final TagService tagService;

    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    @Override
    public FilterConditionVo filterCondition(Long companyId, boolean warehouse, boolean category, boolean tag, boolean online, boolean maintenance, String deviceName) {
        FilterConditionVo res = new FilterConditionVo();
        List<CompanyDevice> list = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId));
        if (list.isEmpty()) {
            return res;
        }
        // 过滤搜索项
        if (StringUtils.isNotBlank(deviceName)) {
            list.removeIf(e -> !StringUtils.containsIgnoreCase(e.getDeviceName(), deviceName));
        }
        if (list.isEmpty()) {
            return res;
        }
        setFilterCondition(warehouse, online, maintenance, res, list);
        // 设置分类
        if (category) {
            //维保筛选分类需要包含已删除设备的分类
            List<String> excludeDeviceIds = list.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
            final List<CompanyDevice> listDeletedCompany = companyDeviceService.selectDeletedList(companyId,null,null,excludeDeviceIds);
            List<CompanyDevice> filterList= new ArrayList<>();
            filterList.addAll(list);
            if (!CollectionUtils.isEmpty(listDeletedCompany)) {
                filterList.addAll(listDeletedCompany);
            }
            // 一级二级品类code先做映射
            setCategory(filterList, res);
        }
        // 设置标签
        if (tag) {
            final List<GroupVo> groupList = getGroupVos(companyId, list);
            res.setGroups(groupList);
        }
        return res;
    }

    private static void setFilterCondition(boolean warehouse, boolean online, boolean maintenance, FilterConditionVo res, List<CompanyDevice> list) {
        // 设置在线状态
        if (online) {
            res.setOnlineStatus(list.stream().map(CompanyDevice::getOnlineStatus).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
        // 设置维保状态
        if (maintenance) {
            res.setMaintenanceStatus(list.stream().map(CompanyDevice::getMaintenanceStatus).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
        // 设置库存状态
        if (warehouse) {
            res.setWarehouseStatus(list.stream().map(CompanyDevice::getWarehouseStatus).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        }
    }

    private void setCategory(List<CompanyDevice> list, FilterConditionVo res) {
        Map<String, List<String>> collect = list.stream().collect(Collectors
                .groupingBy(CompanyDevice::getFirstCategoryCode, Collectors.mapping(CompanyDevice::getSecondCategoryCode, Collectors.toList())));
        List<String> categoryCodes = list.stream()
                .flatMap(e -> Stream.of(e.getFirstCategoryCode(), e.getSecondCategoryCode()))
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCodes(categoryCodes);
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        Map<String, String> categoryMap = fleetCategoryList.stream().collect(Collectors.toMap(FleetCategoryListVo::getCode, FleetCategoryListVo::getCategoryName));
        List<FleetCategoryVo> fleetCategories = new ArrayList<>();
        collect.forEach((k, v) -> {
            final FleetCategoryVo vo = getFleetCategoryVo(k, v, categoryMap);
            fleetCategories.add(vo);
        });
        res.setCategories(fleetCategories);
    }

    @NotNull
    private List<GroupVo> getGroupVos(Long companyId, List<CompanyDevice> list) {
        List<GroupVo> groupList = new ArrayList<>();
        List<String> deviceIds = list.stream().map(CompanyDevice::getDeviceId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>().eq(DeviceTag::getCompanyId, companyId).in(DeviceTag::getDeviceId, deviceIds));
        List<Long> groupIds = deviceTags.stream().map(DeviceTag::getGroupId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (!groupIds.isEmpty()) {
            List<Group> groups = groupService.listByIds(groupIds);
            Map<Long, Group> groupMap = groups.stream().collect(Collectors.toMap(Group::getId, Function.identity()));
            List<Long> tagIds = deviceTags.stream().map(DeviceTag::getTagId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!tagIds.isEmpty()) {
                List<Tag> tags = tagService.listByIds(tagIds);
                Map<Long, Tag> tagMap = tags.stream().collect(Collectors.toMap(Tag::getId, Function.identity()));
                Map<Long, List<DeviceTag>> collect = deviceTags.stream().collect(Collectors.groupingBy(DeviceTag::getGroupId));
                collect.forEach((k, v) -> {
                    if (groupMap.containsKey(k)) {
                        final GroupVo g = getGroupVo(k, v, groupMap, tagMap);
                        groupList.add(g);
                    }
                });
            }
        }
        return groupList;
    }

    @NotNull
    private static GroupVo getGroupVo(Long k, List<DeviceTag> v, Map<Long, Group> groupMap, Map<Long, Tag> tagMap) {
        Group group = groupMap.get(k);
        GroupVo g = new GroupVo();
        g.setGroupId(group.getId()).setGroupName(group.getName()).setIsDefault(group.getIsDefault());
        g.setTags(v.stream().map(DeviceTag::getTagId).distinct().filter(tagMap::containsKey).map(e -> {
            Tag tagOne = tagMap.get(e);
            TagVo t = new TagVo();
            t.setTagId(tagOne.getId()).setTagName(tagOne.getName());
            return t;
        }).collect(Collectors.toList()));
        return g;
    }

    @NotNull
    private static FleetCategoryVo getFleetCategoryVo(String k, List<String> v, Map<String, String> categoryMap) {
        FleetCategoryVo vo = new FleetCategoryVo();
        vo.setLevel(CommonConstant.ONE);
        vo.setCategoryCode(k);
        vo.setCategoryName(categoryMap.get(k));
        if (v != null) {
            vo.setChildren(v.stream().distinct().map(e -> {
                FleetCategoryVo f = new FleetCategoryVo();
                f.setLevel(CommonConstant.TWO);
                f.setCategoryCode(e);
                f.setCategoryName(categoryMap.get(e));
                return f;
            }).collect(Collectors.toList()));
        }
        return vo;
    }
}
