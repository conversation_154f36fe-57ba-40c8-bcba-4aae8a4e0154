package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.TotalToolUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolVo;
import com.chervon.fleet.web.mapper.TotalToolUsageMapper;
import com.chervon.fleet.web.service.TotalToolUsageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @since 2023-08-14 16:57
 **/
@Service
@Slf4j
public class TotalToolUsageServiceImpl extends ServiceImpl<TotalToolUsageMapper, TotalToolUsage>
    implements TotalToolUsageService {

    /**
     * 统计工具使用情况
     * @param deviceId 设备id
     * @return
     */
    @Override
    public EquipmentStatisticsToolVo detailStatisticsTool(String deviceId) {
        TotalToolUsage totalToolUsage = getOne(new LambdaQueryWrapper<TotalToolUsage>()
                .eq(TotalToolUsage::getDeviceId, deviceId)
                .orderByDesc(TotalToolUsage::getModifyTime)
                .last(StringConst.SQL_LIMIT));
        if (null == totalToolUsage) {
            return null;
        }
        EquipmentStatisticsToolVo result = new EquipmentStatisticsToolVo();
        //秒转小时四舍五入保留一位小数
        final BigDecimal totalToolUsageHours = new BigDecimal(totalToolUsage.getUsageDuration()).divide(new BigDecimal(NumberConst.ONE_HOUR_SECOND), 1, RoundingMode.HALF_UP);
        result.setTotalUsageTime(totalToolUsageHours);
        result.setTotalNumberOfUses(totalToolUsage.getNumberTimes());
        return result;
    }
}
