package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.po.TotalToolUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolVo;

/**
 * <AUTHOR>
 * @since 2023-08-14 16:57
 **/
public interface TotalToolUsageService extends IService<TotalToolUsage> {

    /**
     * 详情-statistics-工具类
     *
     * @param deviceId 设备id
     * @return 统计数据
     */
    EquipmentStatisticsToolVo detailStatisticsTool(String deviceId);
}
