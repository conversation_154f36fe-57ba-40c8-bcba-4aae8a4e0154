package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.api.entity.dto.ProductFaultDto;
import com.chervon.fleet.web.api.service.RemoteProductFaultService;
import com.chervon.fleet.web.entity.po.ProductFault;
import com.chervon.fleet.web.service.ProductFaultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-09-18 18:07
 **/
@DubboService
@Slf4j
public class RemoteProductFaultServiceImpl implements RemoteProductFaultService {
    @Resource
    private ProductFaultService productFaultService;

    @Override
    public void save(List<ProductFaultDto> productFaultDtoList) {
        // 增量更新
        long currentTime = System.currentTimeMillis();
        List<ProductFault> target = new ArrayList<>();
        for (ProductFaultDto productFaultDto : productFaultDtoList) {
            ProductFault productFault = ConvertUtil.convert(productFaultDto, ProductFault.class);
            productFault.setId((long) Objects.hash(productFault.getProductId(), productFault.getFaultCode(), productFault.getLanguage()));
            productFault.setModifyTime(currentTime);
            productFault.setLanguage(productFaultDto.getLanguage().toLowerCase());
            target.add(productFault);
        }
        log.debug("syncProductFault -> RemoteProductFaultServiceImpl#save -> target.size(): {}", target.size());
        productFaultService.saveOrUpdateBatch(target);
        productFaultService.remove(new LambdaQueryWrapper<ProductFault>().lt(ProductFault::getModifyTime,currentTime));
    }
}
