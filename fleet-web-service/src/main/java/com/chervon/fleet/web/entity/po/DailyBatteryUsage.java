package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-15 17:10
 **/
@Data
@TableName("t_data_daily_battery_usage")
public class DailyBatteryUsage implements Serializable {
    private static final long serialVersionUID = 1L;

    public DailyBatteryUsage() {
        this.chargingTime = 0;
        this.dischargingTime = 0;
        this.chargingEnergy = 0;
        this.dischargingEnergy = 0;
        this.dailyUsageTime = 0;
    }

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 设备一级分类
     */
    private String categoryCode;
    /**
     * 电池二级分类编码
     */
    private String secondCategoryCode;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 数据状态：0 使用数据，  -1 每日最后上报的缓存值
     */
    private Integer status;
    /**
     * 日期：2023-07-12
     */
    private String date;
    /**
     * 当日充电时长（单位：分钟）
     */
    private Integer chargingTime;
    /**
     * 当日放电时长（单位：分钟）
     */
    private Integer dischargingTime;
    /**
     * 当日充电电量
     */
    private Integer chargingEnergy;
    /**
     * 当日放电电量
     */
    private Integer dischargingEnergy;
    /**
     * 当日电池总使用时长（分钟）-2031
     */
    private Integer dailyUsageTime;
    /**
     * 当日电池总使用次数：2034: 电池循环次数cycle
     */
    private Integer dailyNumberTimes;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long modifyTime;

    /**
     * 开始和截止的坐标值
     */
    private String spanChargingTime;
    private String spanDischargingTime;
    private String spanChargingEnergy;
    private String spanDischargingEnergy;

    public void setId(){
        this.id=(long)(this.deviceId+this.companyId+this.date+"0").hashCode();
    }
}
