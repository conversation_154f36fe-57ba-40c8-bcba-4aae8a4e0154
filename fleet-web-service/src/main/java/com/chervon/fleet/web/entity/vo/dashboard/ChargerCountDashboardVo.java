package com.chervon.fleet.web.entity.vo.dashboard;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 充电器数量看板Vo
 * <AUTHOR>
 * @since 2023-07-28 17:20
 **/
@Data
@Accessors(chain = true)
@ApiModel("充电器数量看板Vo")
public class ChargerCountDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 充电器二级分类编号：
     * PGXPowerBank
     * PGXHub
     * TurboCharger
     * Adaptor
     */
    @Translate(adapter = "fleetCategory", targetField = {"chargerName"})
    private String chargerCategory;
    @ApiModelProperty("充电系统名称: CH7000、power-hub、DC-DC charger")
    private String chargerName;
    @ApiModelProperty("数量")
    private Integer chargerCount;
}
