package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * power-hub充电状态统计表（看板：Charging System Overview图）(t_bi_power_hub_charging)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_power_hub_charging")
public class BiPowerHubCharging extends Model<BiPowerHubCharging> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * power hub设备id
     */
    private String deviceId;
    /**
     * power hub设备名称
     */
    private String deviceName;
    /**
     * 二级分类
     */
    private String secondCategoryCode;
    /**
     * 标签列表，管道符|分隔
     * 该字段主要提供给PowerBi方案,后端需要去t_tag,t_device_tag查库
     */
    private String deviceTag;
    /**
     * 充电进度百分比
     */
    private BigDecimal chargingPercentage;
    /**
     * 故障数
     */
    private Integer faultCount;
    /**
     * Portable Battery充电完成的电池的总数量
     */
    private Integer portableBatteryReady;
    /**
     * Portable Battery充电中的电池的总数量
     */
    private Integer portableBatteryCharging;
    /**
     * Portable Battery等待充电的电池的总数量
     */
    private Integer portableBatteryStandby;
    /**
     * High Capacity Battery充电完成的电池的总数量
     */
    private Integer highCapacityBatteryReady;
    /**
     * High Capacity Battery充电中的电池的总数量
     */
    private Integer highCapacityBatteryCharging;
    /**
     * High Capacity Battery等待充电的电池的总数量
     */
    private Integer highCapacityBatteryStandby;
    /**
     * 已占用充电口的数量
     */
    private Integer chargingPortOccupy;
    /**
     * 充电口的总数量
     */
    private Integer chargingPortCount;
    /**
     * 充电口使用百分比：charging_port_occupy除以charging_port_count
     */
    private BigDecimal portUsagePercentage;
    /**
     * 更新时间
     */
    private Date modifyTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 当前deviceId下的所有充电器上的各类电池都充满的所需时长: (分钟)
     */
    private Integer remainingChargingTimestamp;
    /**
     * 当前deviceId下所有的充电器上的电池都充满的预计完成时间
     */
    private Long estimatedCompletionTimestamp;
    /**
     * 充电中的电池剩余安时数
     */
    private BigDecimal chargingEnergyAh;
    /**
     * 充电完成的电池安时数
     */
    private BigDecimal readyEnergyAh;
    /**
     * 当前deviceId下的Portable电池下一个电池待充满的所需时长: (分钟)
     */
    private Integer nextChargingComplete;
    /**
     * 当前deviceId下的下一个高能电池待充满的所需时长: (分钟)
     */
    private Integer nextHighChargingComplete;
    /**
     * 充电状态：0保留  1 正常充电 2等待充电 3电池过温 4电池故障  5空闲 6充电完成  取值从设备影子转换:2016
     */
    private Integer chargerState;
    /**
     * PowerBank工作模式:1AC  2DC
     */
    private Integer mode;
    /**
     * 非标老电池包数量
     */
    private Integer legacyBattery;
}