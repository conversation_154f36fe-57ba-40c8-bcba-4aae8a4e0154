package com.chervon.fleet.web.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
public enum RequestListType implements TypeEnum {
    /**
     * 白名单
     */
    WHITELIST(1, "白名单"),
    /**
     *  正常
     */
    NORMAL(2, "正常"),
    /**
     *  黑名单
     */
    BLACKLIST(3, "黑名单"),
    ;


    private int type;
    private String desc;

    RequestListType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
