package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.web.api.entity.query.CompanyDeviceQuery;
import com.chervon.fleet.web.api.entity.query.dashboard.ChargingSystemDetailDashboardQuery;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.dto.DeviceBasicCache;
import com.chervon.fleet.web.entity.po.BiPowerHubDetail;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingSystemDetailDashboardVo;
import com.chervon.fleet.web.mapper.BiPowerHubDetailMapper;
import com.chervon.fleet.web.service.BiPowerHubDetailService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.utils.DataUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * power-hub充电详情统计表(看板详情页：Power Overview Detail图)服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiPowerHubDetailServiceImpl extends ServiceImpl<BiPowerHubDetailMapper, BiPowerHubDetail>
    implements BiPowerHubDetailService {
    public static final List<String> NO_BINDING_SN = Arrays.asList("CH15");
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Override
    public List<ChargingSystemDetailDashboardVo> powerHubDetailRight(ChargingSystemDetailDashboardQuery query) {
        List<BiPowerHubDetail> biPowerHubDetails = this.list(new LambdaQueryWrapper<BiPowerHubDetail>()
            .eq(BiPowerHubDetail::getCompanyId, query.getCompanyId())
            .eq(BiPowerHubDetail::getHubDeviceId, query.getDeviceId())
            .orderByAsc(BiPowerHubDetail::getHubDeviceId)
            .orderByAsc(BiPowerHubDetail::getBatteryDeviceId));
        if (CollectionUtils.isEmpty(biPowerHubDetails)) {
            return new ArrayList<>();
        }
        List<ChargingSystemDetailDashboardVo> listVo=new ArrayList<>();
        final CompanyDevice deviceStatus = companyDeviceService.getDeviceOnlineInfo(new CompanyDeviceQuery().setDeviceId(query.getDeviceId()));
        final boolean offline = DataUtils.isOffline(deviceStatus);
        for(BiPowerHubDetail detail:biPowerHubDetails){
            if(offline || FleetDeviceConfig.isExpired5Minutes(detail.getModifyTime())){
                continue;
            }
            ChargingSystemDetailDashboardVo vo = BeanCopyUtils.copy(detail, ChargingSystemDetailDashboardVo.class);
            //读取设备名称
            final String suffix = vo.getAdaptorName().substring(vo.getAdaptorName().length() - CommonConstant.FIVE, vo.getAdaptorName().length());
            if(isNoBindingDevice(vo.getAdaptorName())){
                vo.setAdaptorName(vo.getAdaptorCategoryCode()+suffix);
            }else{
                final DeviceBasicCache deviceBasicCache = companyDeviceService.getDeviceByCache(vo.getAdaptorName());
                if(Objects.isNull(deviceBasicCache)){
                    vo.setAdaptorName(vo.getAdaptorCategoryCode()+suffix);
                }else{
                    vo.setAdaptorName(deviceBasicCache.getDeviceName());
                }
            }
            listVo.add(vo);
        }
        return listVo;
    }

    public static boolean isNoBindingDevice(String deviceId){
        final String sn = getSn(deviceId);
        if(StringUtils.isEmpty(sn)){
            return true;
        }
        return NO_BINDING_SN.contains(sn);
    }
    public static String getSn(String deviceId){
        if(deviceId.length()<CommonConstant.FIVE){
            return "";
        }
        return deviceId.substring(CommonConstant.ONE,CommonConstant.FIVE);
    }
}