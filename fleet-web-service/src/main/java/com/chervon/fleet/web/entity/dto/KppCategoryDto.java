package com.chervon.fleet.web.entity.dto;


import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import com.chervon.fleet.web.entity.enums.CommonStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2022/11/30
 */
@Data
@Accessors(chain = true)
@ApiModel("分类信息实体")
public class KppCategoryDto {
    private static final long serialVersionUID = 1L;

    //根据字典或数据库获取描述转换
    @Translate(adapter = "multiLanguage", type = ConvertType.DB, targetField = {"categoryName","publishStatusName"})
    @ApiModelProperty(value = "分类编号")
    private String categoryCode;

    @ApiModelProperty(value = "所属类型名称")
    private String categoryName;

    //根据枚举获取描述转换
    @ApiModelProperty(value = "状态枚举：0关闭、1打开")
    @Translate(adapter = "default", type = ConvertType.ENUM, enumName = "CommonStatusEnum",targetField = {"openStatusName"})
    private Integer openStatus;

    @ApiModelProperty(value = "状态名称 ")
    private String openStatusName;


    //测试时间格式化转换
    @ApiModelProperty(value = "发布时间")
    @Translate(adapter = "default", type = ConvertType.DATEFORMAT,targetField = {"strPublishTime"})
    private LocalDateTime publishTime;

    @ApiModelProperty(value = "发布时间文本格式化后")
    private String strPublishTime;

}
//https://blog.csdn.net/u010012932/article/details/125621347
