package com.chervon.fleet.web.utils;

import java.util.Objects;

/**
 * 进制转换工具类
 */
public class ParseUtil {
    public static Double parseDouble(String strNum){
        if(Objects.isNull(strNum)){
            return 0d;
        }
        double doubleNum;
        try {
            doubleNum = Double.parseDouble(strNum);
        } catch (NumberFormatException e) {
            doubleNum=0;
        }
        return doubleNum;
    }
    /**
     * 将二进制转换成16进制
     *
     * @param buf
     * @return
     */
    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 转为初值,末值保存原始数据值
     * @param min 每日初值
     * @param max 每日末值
     * @return
     */
    public static String parseTup2(Integer min,Integer max) {
        return min + "," + max;
    }

    public static Integer getDiffValue(Integer min,Integer max) {
        if(!Objects.isNull(min) && !Objects.isNull(max)){
            return max - min;
        }
        return 0;
    }
}
