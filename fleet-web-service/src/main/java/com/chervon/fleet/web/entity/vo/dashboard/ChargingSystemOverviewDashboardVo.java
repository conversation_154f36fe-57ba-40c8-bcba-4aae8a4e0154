package com.chervon.fleet.web.entity.vo.dashboard;

import com.chervon.fleet.web.api.entity.consts.GlobalConsts;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 充电系统概览看板Vo
 * <AUTHOR>
 * @since 2023-07-31 11:28
 **/
@Data
@ApiModel("充电系统概览看板Vo")
public class ChargingSystemOverviewDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * power hub设备id
     */
    @ApiModelProperty("power hub设备id")
    private String deviceId;
    /**
     * power hub设备名称
     */
    @ApiModelProperty("power hub设备名称")
    private String deviceName;
    @ApiModelProperty("标签列表，管道符|分隔")
    private String deviceTag;
    @ApiModelProperty("充电进度百分比")
    private BigDecimal chargingPercentage;
    @ApiModelProperty("故障数")
    private Integer faultCount=0;
    @ApiModelProperty("最近一条(故障码)故障名称,且Charger优先于Battery")
    private String latestErrorMessage;
    /**
     * 处理建议内容
     */
    @ApiModelProperty("最近一条(故障码)处理建议内容")
    private String suggestionContent;
    @ApiModelProperty("Portable Battery充电完成的电池的总数量")
    private Integer portableBatteryReady=0;
    @ApiModelProperty("Portable Battery充电中的电池的总数量")
    private Integer portableBatteryCharging=0;
    @ApiModelProperty("Portable Battery等待充电的电池的总数量")
    private Integer portableBatteryStandby=0;
    @ApiModelProperty("High Capacity Battery充电完成的电池的总数量")
    private Integer highCapacityBatteryReady=0;
    @ApiModelProperty("High Capacity Battery充电中的电池的总数量")
    private Integer highCapacityBatteryCharging=0;
    @ApiModelProperty("High Capacity Battery等待充电的电池的总数量")
    private Integer highCapacityBatteryStandby=0;
    @ApiModelProperty("充电器的剩余充电时长预估：4:37")
    private String readyInTime;
    @ApiModelProperty("已占用充电口的数量")
    private Integer chargingPortOccupy=0;
    @ApiModelProperty("充电口的总数量")
    private Integer chargingPortCount=0;
    @ApiModelProperty("充电口使用百分比：charging_port_occupy除以charging_port_count")
    private BigDecimal portUsagePercentage;
    @ApiModelProperty("当前deviceId下的下一个Portable电池待充满的所需时长: (分钟)")
    private Integer nextChargingComplete;
    @ApiModelProperty("当前deviceId下的下一个High Capacity电池待充满的所需时长: (分钟)")
    private Integer nextHighChargingComplete;
    @ApiModelProperty("充电状态：0保留 1 正常充电 2等待充电 4电池故障 5空闲 6充电完成")
    private Integer chargerState;
    @ApiModelProperty("非标老电池包数量")
    private Integer legacyBattery;
    /**
     * 老电池包列表：HC2240T,BA4480T
     */
    @ApiModelProperty("老电池包列表：HC2240T,BA4480T")
    private String compatibleList = GlobalConsts.COMPATIBLE_LIST;
    /**
     * 更新时间
     */
    private Date modifyTime;
}
