package com.chervon.fleet.web.entity.vo.dashboard;

import com.chervon.fleet.web.api.entity.consts.GlobalConsts;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 充电状态看板Vo
 * <AUTHOR>
 * @since 2023-07-28 17:31
 **/
@Data
@ApiModel("充电状态看板Vo")
public class ChargingStatusDashboardVo {
    private static final long serialVersionUID = 1L;
    public ChargingStatusDashboardVo() {
        chargerCount = 0;
        chargingPercentage=0;
        readyBatteryCount=0;
        availableAh=BigDecimal.ZERO;
        readyBatteryAh=BigDecimal.ZERO;
        chargingBatteryCount=0;
        chargingBatteryAh=BigDecimal.ZERO;
        errors=0;
        latestErrorDevice="";
        remainingChargingTime="";
    }
    /**
     * 充电器数量:返回0不展示chargingStatus图表
     */
    private int chargerCount;
    /**
     * 当前租户账号下的所有充电器上的各类电池都充满的所需时长,返回格式为11:59
     */
    @ApiModelProperty("当前租户账号下的所有充电器上的各类电池都充满的所需时长,返回格式为11:59")
    private String remainingChargingTime;
    /**
     * 当前租户账号下所有的充电器上的电池都充满的预计完成时间
     */
    @ApiModelProperty("当前租户账号下所有的充电器上的电池都充满的预计完成时间")
    private Long estimatedCompletionTime;
    @ApiModelProperty("充电百分比")
    private Integer chargingPercentage;
    @ApiModelProperty("租户下所有插在充电座上的已充满的电池的总数量")
    private Integer readyBatteryCount;
    @ApiModelProperty("租户账号下所有插在充电座上的已充满的电池的总标称电量")
    private BigDecimal readyBatteryAh;
    /**
     * 已充电量（Available）
     * 挂在充电器上的电量总和
     */
    @ApiModelProperty("已充电量（Available）")
    private BigDecimal availableAh;
    @ApiModelProperty("租户账号下所有插在充电座上的充电中和等待充电的电池的总数量")
    private Integer chargingBatteryCount;
    @ApiModelProperty("租户账号下所有插在充电座上的充电中和等待充电的电池已有的总电量")
    private BigDecimal chargingBatteryAh;
    @ApiModelProperty("错误数量: 如果为0展示NORMAL; 不为0展示x ERRORS")
    private Integer errors;
    @ApiModelProperty("最近报错设备名称")
    private String latestErrorDevice;
    /**
     * 老电池包列表：HC2240T,BA4480T
     */
    @ApiModelProperty("老电池包列表：HC2240T,BA4480T")
    private String compatibleList = GlobalConsts.COMPATIBLE_LIST;
}
