package com.chervon.fleet.web.service.translate;

import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.core.TranslateService;
import com.chervon.common.web.entity.ConvertType;
import com.chervon.common.web.util.UserContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2022/12/6
 */
@Component
public class I18NTranslateImpl implements TranslateService<Object,Object,Object> {

    public String getInstanceCode(){
        return "I18NResource";
    }

    @Override
    public Object getSourceValue(Field field, Object dataEntity) throws IllegalAccessException {
        final String language = UserContext.getLanguage();
        if(StringUtils.isEmpty(language) || language.equals(I18nController.DEFAULT_LANGUAGE)){
            return null;
        }
        final Translate translate = field.getAnnotation(Translate.class);
        Object objValue = field.get(dataEntity);
        if (objValue == null) {
            return null;
        }
        if (translate.type() == ConvertType.ENUM) {
            final String enumItemValue = I18nController.getEnumItemValue(language, translate.enumName(), objValue.toString());
            return enumItemValue;
        } else if (translate.type() == ConvertType.I18NResource) {
            final String name = I18nController.getResourceById(language, objValue.toString());
            return name;
        }else{
            return null;
        }
    }

    @Override
    public void setTargetValue(Object dataDto, Object result, List<String> targetField) throws IllegalAccessException {
        if(Objects.isNull(result)){
            return;
        }
        Class<?> aClass = dataDto.getClass();
        List<Field> fields = TranslateUtils.getBindFields(aClass,targetField);
        for (Field field : fields) {
            field.setAccessible(true);
            field.set(dataDto, result);
        }
    }
}
