package com.chervon.fleet.web.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.BiChargingStatus;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingStatusDashboardVo;
import com.chervon.fleet.web.mapper.BiChargingStatusMapper;
import com.chervon.fleet.web.service.BiChargingStatusService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 充电状态统计表（看板顶端Charging Status的4个title图和Power Availability图）服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiChargingStatusServiceImpl extends ServiceImpl<BiChargingStatusMapper, BiChargingStatus>
    implements BiChargingStatusService {

    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Override
    public BiChargingStatus getByCompanyId(Long companyId) {
        return this.getOne(new LambdaQueryWrapper<BiChargingStatus>()
            .eq(BiChargingStatus::getCompanyId, companyId)
            .last(StringConst.SQL_LIMIT));
    }

    /**
     * power web端看板顶部充电状态栏（租户级别）
     * @param companyId 公司ID
     * @return
     */
    @Override
    public ChargingStatusDashboardVo chargingStatus(Long companyId) {
        BiChargingStatus biChargingStatus = getByCompanyId(companyId);
        if (null == biChargingStatus || companyDeviceService.getPowerHubCount(companyId,true)==0L){
            return new ChargingStatusDashboardVo();
        }
        ChargingStatusDashboardVo result = ConvertUtil.convert(biChargingStatus, ChargingStatusDashboardVo.class);
        result.setAvailableAh(biChargingStatus.getReadyBatteryAh());
        String remainingChargingTime = biChargingStatus.getRemainingChargingTime();
        // 如果数据库中存储值符合12h 31min格式，则转化为12:31的格式
        if (StringConst.REG_H_MIN.matcher(remainingChargingTime).matches()) {
            String h = remainingChargingTime.substring(0, remainingChargingTime.indexOf('h'));
            if (StrUtil.isBlank(h)) {
                h = "0";
            }
            String min = remainingChargingTime.substring(remainingChargingTime.indexOf('h') + CommonConstant.TWO, remainingChargingTime.indexOf("min"));
            remainingChargingTime = h + ":" + min;
        }
        result.setRemainingChargingTime(remainingChargingTime);
        long timestamp = biChargingStatus.getRemainingChargingTimestamp()==0? 0: (System.currentTimeMillis() + (biChargingStatus.getRemainingChargingTimestamp() * NumberConst.ONE_MINUTE_MS));
        result.setEstimatedCompletionTime(timestamp);

        int count = biChargingStatus.getChargingBatteryCount() + biChargingStatus.getReadyBatteryCount();
        int value = count==0?0: (int) ((BigDecimal.valueOf(biChargingStatus.getReadyBatteryCount() / count).setScale(CommonConstant.TWO, RoundingMode.UP).doubleValue()) * NumberConst.PERCENTAGE);
        result.setChargingPercentage(value);
        return result;
    }

}