package com.chervon.fleet.web.entity.vo;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet品类数据
 * <AUTHOR>
 * @since 2023-08-28 16:59
 **/
@Data
@Accessors(chain = true)
@ApiModel(description = "fleet品类数据")
public class FleetCategoryFilterVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 品类code
     */
    @ApiModelProperty("品类code")
    @Translate(adapter = "fleetCategory", targetField = {"categoryName"})
    private String categoryCode;
    @ApiModelProperty("品类名称")
    private String categoryName;
}

