package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * (t_company_device)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-25 10:37:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_company_device")
public class CompanyDevice extends Model<CompanyDevice> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * mac地址
     */
    private String mac;
    /**
     * 设备sn
     */
    private String deviceSn;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 一级分类编码
     */
    private String firstCategoryCode;
    /**
     * 二级分类编码
     */
    private String secondCategoryCode;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 设备绑定时间
     */
    private Long bindingTime;
    /**
     * 设备绑定用户id
     */
    private Long bindingUserId;
    /**
     * 设备库存状态：1-In Warehouse，2-Out for Work，3-Unknown location，0-Never Seen
     */
    private Integer warehouseStatus;
    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 在线状态 0 离线 1 在线
     */
    private Integer onlineStatus;
    /**
     * 业务定义品类分类 1 tool 2 charger 3 battery
     */
    private Integer customType;
    /**
     * 维保状态 1 due 2 on 3 off
     */
    private Integer maintenanceStatus;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * wifi设备上线时间戳
     */
    private Long onlineTime;
    /**
     * wifi设备离线时间戳
     */
    private Long offlineTime;
}