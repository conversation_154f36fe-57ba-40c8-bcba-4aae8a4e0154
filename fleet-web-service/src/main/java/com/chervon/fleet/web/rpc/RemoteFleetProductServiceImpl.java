package com.chervon.fleet.web.rpc;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.dto.BaseProductDto;
import com.chervon.fleet.web.api.entity.enums.BusinessTypeEnum;
import com.chervon.fleet.web.api.entity.vo.ProductInfoVo;
import com.chervon.fleet.web.api.service.RemoteFleetProductService;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.FleetProductCategoryVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.vo.ProductRpcVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:33
 */
@Service
@DubboService
@Slf4j
public class RemoteFleetProductServiceImpl implements RemoteFleetProductService {

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    @Override
    public List<ProductInfoVo> listProduct() {
        List<ProductInfoVo> res = new ArrayList<>();
        //查询iot-platform 4库缓存key缓存的pid列表：key: fleet_product_ids
        List<Long> appListOrderProduct = remoteProductService.getFleetProductIds();
        if (CollectionUtils.isEmpty(appListOrderProduct)) {
            return res;
        }
        //查询operation-platform 7库产品信息缓存：cache:operation:product:1751865305756000257
        List<ProductCache> productCacheList = remoteOperationCacheService.listProducts(appListOrderProduct);
        // 删除不使用fleet的产品
        productCacheList.removeIf(e -> e.getBusinessType() == null || !e.getBusinessType().contains(BusinessTypeEnum.FLEET.getType()));
        if (CollectionUtils.isEmpty(productCacheList)) {
            return res;
        }
        // 获取产品的商品型号：从上面缓存中读取：commodityModel 与fleet品类比对
        List<String> models = productCacheList.stream().map(ProductCache::getCommodityModel).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        if (models.isEmpty()) {
            return res;
        }
        List<FleetProductCategoryVo> fleetProductCategories = remoteFleetCategoryService.listProductCategory(models);
        if (CollectionUtils.isEmpty(fleetProductCategories)) {
            return res;
        }
        // 获取一二级品类code
        List<String> categoryCodes = fleetProductCategories.stream()
                .flatMap(e -> Stream.of(e.getFirstCategoryCode(), e.getSecondCategoryCode()))
                .filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        // 获取fleet品类
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCodes(categoryCodes);
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        Map<String, String> categoryMap = fleetCategoryList.stream().collect(Collectors.toMap(FleetCategoryListVo::getCode, FleetCategoryListVo::getCategoryName));
        // 产品品类关系进行排序，一级--二级--商品型号
        fleetProductCategories.sort(Comparator.comparing(FleetProductCategoryVo::getFirstCategoryCode)
                .thenComparing(FleetProductCategoryVo::getSecondCategoryCode));
        // 按照产品品类关系构建出参
        Map<String, ProductCache> productMap = productCacheList.stream().collect(Collectors.toMap(ProductCache::getCommodityModel, Function.identity(), (k1, k2) -> k1));
        return fleetProductCategories.stream().map(e -> {
            ProductCache productCache = productMap.get(e.getProductModel());
            if (productCache != null) {
                return getProductInfoVo(e, productCache, categoryMap);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @NotNull
    private static ProductInfoVo getProductInfoVo(FleetProductCategoryVo e, ProductCache productCache, Map<String, String> categoryMap) {
        ProductInfoVo vo = new ProductInfoVo();
        vo.setProductId(productCache.getId());
        vo.setProductIconUrl(productCache.getUrl());
        vo.setCommodityModel(e.getProductModel());
        vo.setCategoryCode(e.getSecondCategoryCode());
        vo.setCategoryName(categoryMap.get(e.getSecondCategoryCode()));
        vo.setNetworkModes(productCache.getNetworkModes());
        vo.setProductType(productCache.getType());
        vo.setProductSnCode(productCache.getSnCode());
        vo.setBusinessType(productCache.getBusinessType());
        return vo;
    }

    @Override
    public ProductInfoVo detailProduct(BaseProductDto req) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "baseProductDto");
        ProductInfoVo res = new ProductInfoVo();
        if (StringUtils.hasText(req.getProductSnCode()) || req.getProductId() != null) {
            if (req.getProductId() == null) {
                ProductRpcVo rpc = remoteProductService.getProductIdBySnCode(req.getProductSnCode());
                if (rpc == null) {
                    return res;
                } else {
                    req.setProductId(rpc.getId());
                }
            }
            ProductCache productCache = remoteOperationCacheService.getProduct(req.getProductId());
            if (productCache == null) {
                return res;
            }
            // 构建数据
            res.setProductId(productCache.getId());
            res.setProductIconUrl(productCache.getUrl());
            res.setCommodityModel(productCache.getCommodityModel());
            res.setNetworkModes(productCache.getNetworkModes());
            res.setProductType(productCache.getType());
            res.setProductSnCode(productCache.getSnCode());
            // 获取产品的商品型号
            List<FleetProductCategoryVo> fleetProductCategories = remoteFleetCategoryService.listProductCategory(Collections.singletonList(productCache.getCommodityModel()));
            FleetProductCategoryVo productCategory = fleetProductCategories.stream().filter(e ->e.getProductModel()!=null && e.getProductModel().equals(productCache.getCommodityModel())).findAny().orElse(null);
            if (productCategory != null) {
                res.setCategoryCode(productCategory.getSecondCategoryCode());
                // 获取fleet品类
                FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
                fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
                fleetCategoryQuery.setCode(productCategory.getSecondCategoryCode());
                List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
                fleetCategoryList.stream().filter(e ->e.getCode()!=null && e.getCode().equals(productCategory.getSecondCategoryCode())).findAny().ifPresent(fleetCategory -> res.setCategoryName(fleetCategory.getCategoryName()));
            }
            // 适用app类型
            res.setBusinessType(productCache.getBusinessType());
            return res;
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED, "productId and productSnCode");
        }
    }
}
