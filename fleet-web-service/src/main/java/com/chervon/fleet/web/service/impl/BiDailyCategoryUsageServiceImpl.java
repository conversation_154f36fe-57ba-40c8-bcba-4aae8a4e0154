package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.fleet.web.api.entity.query.dashboard.DailyCategoryUsageDashboardQuery;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.po.BiDailyCategoryUsage;
import com.chervon.fleet.web.entity.vo.dashboard.DailyCategoryUsageDashboardVo;
import com.chervon.fleet.web.mapper.BiDailyCategoryUsageMapper;
import com.chervon.fleet.web.service.BiDailyCategoryUsageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备日使用量统计表（看板：Usage Statistics）服务接口实现
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiDailyCategoryUsageServiceImpl extends ServiceImpl<BiDailyCategoryUsageMapper, BiDailyCategoryUsage>
    implements BiDailyCategoryUsageService {

    @Override
    public List<DailyCategoryUsageDashboardVo> dailyCategoryUsage(DailyCategoryUsageDashboardQuery query) {
        List<BiDailyCategoryUsage> biDailyCategoryUsageList = this.list(new LambdaQueryWrapper<BiDailyCategoryUsage>()
            .eq(BiDailyCategoryUsage::getCompanyId, query.getCompanyId())
            .in(BiDailyCategoryUsage::getCategoryCode, query.getCategoryCodeList())
            .ge(BiDailyCategoryUsage::getStrDate, query.getStartTime())
            .le(BiDailyCategoryUsage::getStrDate, query.getEndTime()));
        Map<String, List<BiDailyCategoryUsage>> dateCategoryUsageMap = biDailyCategoryUsageList.stream()
            .collect(Collectors.groupingBy(BiDailyCategoryUsage::getStrDate));
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(query.getStartTime(), query.getEndTime());

        List<DailyCategoryUsageDashboardVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            List<BiDailyCategoryUsage> biDailyCategoryUsages = dateCategoryUsageMap.get(date);
            DailyCategoryUsageDashboardVo vo = new DailyCategoryUsageDashboardVo()
                .setDate(date).setIsFullyCalculated(true);
            if (!CollectionUtils.isEmpty(biDailyCategoryUsages)) {
                Integer dailyTotalUsageTime = 0;
                //根据查询条件设定数量：如果选择一个分类，按分类数量统计，如果选择多个分类认为查询的租户下总工具类数量；
                //默认赋值：租户总设备数
                Integer deviceCount = biDailyCategoryUsages.get(0).getDailyDeviceTotalCount();
                //当日设备总使用数量
                Integer dailyDeviceUsageCount = 0;

                final Result resultValue = getResult(query, biDailyCategoryUsages, vo, dailyTotalUsageTime, deviceCount, dailyDeviceUsageCount);

                setValueByCondition(query, resultValue.deviceCount, vo, resultValue.dailyTotalUsageTime, resultValue.dailyDeviceUsageCount, biDailyCategoryUsages);
            } else {
                // 查库没有查到当日数据
                vo.setIsFullyCalculated(false);
            }
            result.add(vo);
        }
        return result;
    }

    @NotNull
    private static Result getResult(DailyCategoryUsageDashboardQuery query, List<BiDailyCategoryUsage> biDailyCategoryUsages, DailyCategoryUsageDashboardVo vo, Integer dailyTotalUsageTime, Integer deviceCount, Integer dailyDeviceUsageCount) {
        for (BiDailyCategoryUsage biDailyCategoryUsage : biDailyCategoryUsages) {
            vo.setCategoryName(vo.getCategoryName() == null ? biDailyCategoryUsage.getCategoryName() :
                    (vo.getCategoryName() + "|" + biDailyCategoryUsage.getCategoryName()));
            vo.setCategoryCode(vo.getCategoryCode() == null ? biDailyCategoryUsage.getCategoryCode() :
                    (vo.getCategoryCode() + "|" + biDailyCategoryUsage.getCategoryCode()));
            if (BiDailyCategoryUsage.propertyNotNull(biDailyCategoryUsage)) {
                dailyTotalUsageTime += biDailyCategoryUsage.getDailyTotalUsageTime();
                if(query.getCategoryCodeList()!=null && query.getCategoryCodeList().size()>1){
                    //全选：按租户下总工具类数量统计：初始值已赋值
                }
                else{//单选某一个分类，取分类下设备数量
                    deviceCount = biDailyCategoryUsage.getDeviceCategoryCount();
                }
                dailyDeviceUsageCount += biDailyCategoryUsage.getDailyDeviceUsageCount();
            } else {
                // 存在空数值
                vo.setIsFullyCalculated(false);
            }
        }
        Result resultValue = new Result(dailyTotalUsageTime, deviceCount, dailyDeviceUsageCount);
        return resultValue;
    }

    private static class Result {
        public final Integer dailyTotalUsageTime;
        public final Integer deviceCount;
        public final Integer dailyDeviceUsageCount;

        public Result(Integer dailyTotalUsageTime, Integer deviceCount, Integer dailyDeviceUsageCount) {
            this.dailyTotalUsageTime = dailyTotalUsageTime;
            this.deviceCount = deviceCount;
            this.dailyDeviceUsageCount = dailyDeviceUsageCount;
        }
    }

    private static void setValueByCondition(DailyCategoryUsageDashboardQuery query, Integer deviceCount, DailyCategoryUsageDashboardVo vo, Integer dailyTotalUsageTime, Integer dailyDeviceUsageCount, List<BiDailyCategoryUsage> biDailyCategoryUsages) {
        if (deviceCount != 0) {
            vo.setDailyAverageUsageTime(dailyTotalUsageTime / dailyDeviceUsageCount);
            // 当日设备总使用数量  求百分比
            BigDecimal deviceUsageCount = new BigDecimal(dailyDeviceUsageCount);
            BigDecimal sumDeviceCount = new BigDecimal(deviceCount);
            BigDecimal percentage = sumDeviceCount.equals(BigDecimal.ZERO)?BigDecimal.ZERO:  deviceUsageCount.divide(sumDeviceCount, CommonConstant.TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(NumberConst.PERCENTAGE));
            if(percentage.compareTo(new BigDecimal(100)) > 0){
                percentage = new BigDecimal(100);
            }
            vo.setDailyUsageRate(percentage);
        }
        if (biDailyCategoryUsages.size() != query.getCategoryCodeList().size()) {
            // 实际查库的数量不等于查询的标签数
            vo.setIsFullyCalculated(false);
        }
    }
}