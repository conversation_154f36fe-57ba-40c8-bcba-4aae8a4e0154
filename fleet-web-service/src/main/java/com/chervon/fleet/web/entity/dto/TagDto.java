package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/11 16:27
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "标签操作对象")
public class TagDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("标签id，新增不必填，编辑必填")
    private Long tagId;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("分组id")
    private Long groupId;

}
