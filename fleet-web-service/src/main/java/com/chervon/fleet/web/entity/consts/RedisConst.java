package com.chervon.fleet.web.entity.consts;

import java.text.MessageFormat;

/**
 * redis缓存key常量
 *
 * <AUTHOR> 2023/7/13
 */
public class RedisConst {
    /**
     * 网关综合信息缓存(通过网关缓存找到租户信息，通过租户设备关系，拿到设备缓存详情)
     */
    public static final String GATEWAY_INFO_KEY = "GATEWAY_INFO_{0}";

    /**
     * 网关信息缓存key: GATEWAY_INFO_{gatewayId}
     *
     * @param gatewayId 网关id
     * @return key值
     */
    public static String getGatewayKey(String gatewayId) {
        return MessageFormat.format(RedisConst.GATEWAY_INFO_KEY, gatewayId);
    }
    //****************************************************************************
    /**
     * 设备综合信息缓存(设备的基础信息，不含状态信息)
     * * (通过设备信息找到租户信息，通过租户设备关系，拿到设备缓存详情)
     */
    public static final String DEVICE_INFO_KEY = "DEVICE_INFO_{0}";

    /**
     * 设备信息缓存key: DEVICE_INFO_{deviceId}
     *
     * @param deviceId 设备id
     * @return key值
     */
    public static String getDeviceInfoKey(String deviceId) {
        return MessageFormat.format(RedisConst.DEVICE_INFO_KEY, deviceId);
    }
    //****************************************************************************

    /**
     * 网关与设备关系map-key: COMPANY_GATEWAY_{companyId}_{gatewayId}
     * 大key：租户id-网关id：小key：{设备Id} 值：设备上报时间戳
     */
    public static final String COMPANY_GATEWAY_REF_KEY = "COMPANY_GATEWAY_{0}_{1}";

    /**
     * 租户网关设备关系map大key: COMPANY_GATEWAY_{companyid}
     *
     * @param companyId 租户id
     * @return key值
     */
    public static String getCompanyGatewayMapKey(Long companyId, String gatewayId) {
        return MessageFormat.format(RedisConst.COMPANY_GATEWAY_REF_KEY, companyId.toString(), gatewayId);
    }
    //****************************************************************************
    /**
     * 租户设备列表关系缓存key: COMPANY_DEVICE_LIST_{companyid}
     * 租户id： 值：{  List<deviceId> 设备Id列表 }
     */
    public static final String COMPANY_DEVICE_LIST_KEY = "COMPANY_DEVICE_LIST_{0}";

    /**
     * * 租户设备列表关系缓存key: COMPANY_DEVICE_LIST_{companyid}
     *
     * @param companyId 租户id
     * @return key值
     */
    public static String getCompanyDeviceListKey(Long companyId) {
        return MessageFormat.format(RedisConst.COMPANY_DEVICE_LIST_KEY, companyId.toString());
    }
    //****************************************************************************

    /**
     * 设备库存状态key: DEVICE_WAREHOUSE_STATUS_{deviceId}
     * 设备Id：   值：{  网关id、库存状态、RSSI 、坐标 }
     */
    public static final String DEVICE_WAREHOUSE_STATUS_KEY = "DEVICE_WAREHOUSE_STATUS_{0}";

    /**
     * * 网关设备库存状态map大key: DEVICE_WAREHOUSE_STATUS_{deviceId}
     *
     * @param deviceId 设备id
     * @return key值
     */
    public static String getDeviceWarehouseStatusKey(String deviceId) {
        return MessageFormat.format(RedisConst.DEVICE_WAREHOUSE_STATUS_KEY, deviceId);
    }
    //****************************************************************************
    /**
     * 租户设备数据上报完成时间map-key: DATA_REPORT_COMPLETE_{companyid}
     * 租户id： 设备Id：   值：{  数据上报完成时间戳 }
     */
    public static final String DATA_REPORT_COMPLETE_KEY = "DATA_REPORT_COMPLETE_{0}";

    /**
     * * 获取租户设备数据上报完成时间map大key: DATA_REPORT_COMPLETE_{companyid}
     *
     * @param companyId 租户id
     * @return key值
     */
    public static String getDataReportCompleteKey(Long companyId) {
        return MessageFormat.format(RedisConst.DATA_REPORT_COMPLETE_KEY, companyId.toString());
    }

    public static String getCompanyFixGatewayCoordinate(Long companyId) {
        return MessageFormat.format("Company_FixGateway_Coordinate_{0}", companyId.toString());
    }
    //****************************************************************************

    //数据字典缓存时间
    public static final long DATA_DICTIONARY_EXPIRE_SECOND = 3600L * 3L;
    public static final int HOURS_4_EXPIRE =3600*4;
    //网关临时缓存时间   5分钟
    public static final long GATEWAY_INFO_EXPIRE_SECOND = 300L;
    //设备临时缓存时间
    public static final long DEVICE_INFO_EXPIRE_SECOND = 300L;
    //租户与设备列表关系临时缓存时间
    public static final long DEVICE_LIST_EXPIRE_SECOND = 300L;

    /**
     * category分类设备数量key: company:category:count_{company}_{categoryCode}
     * @return key值 缓存结果对象数量：55
     */
    public static String getCategoryDeviceCountKey(Long companyId,String categoryCode){
        return MessageFormat.format("company:category:count_{0}_{1}",companyId.toString(),categoryCode);
    }
}
