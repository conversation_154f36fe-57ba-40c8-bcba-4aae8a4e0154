package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Entity com.chervon.fleet.domain.EquipmentStatisticsTool
 * @description: 工具类平均使用情况vo
 * <AUTHOR>
 * @date 2023/7/24 18:53
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsToolAverageDailyUsageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Average daily duration of use")
    private BigDecimal averageDailyDurationOfUse;

    @ApiModelProperty("Average number of daily uses")
    private BigDecimal averageNumberOfDailyUses;

    @ApiModelProperty("Average daily duration of single use")
    private BigDecimal averageDailyDurationOfSingleUse;

}
