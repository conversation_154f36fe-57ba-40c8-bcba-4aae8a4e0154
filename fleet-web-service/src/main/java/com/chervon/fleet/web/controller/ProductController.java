package com.chervon.fleet.web.controller;

import com.chervon.common.web.util.HeaderUtils;
import com.chervon.operation.api.RemotePostSaleService;
import com.chervon.operation.api.vo.ProductPostSaleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/7/3 16:04
 */
@Slf4j
@Api(tags = "产品相关")
@RestController
@RequestMapping("/product")
public class ProductController {

    @DubboReference
    private RemotePostSaleService remotePostSaleService;

    /**********************产品售后************************/

    @ApiOperation("根据产品id，查询产品售后数据")
    @GetMapping("postSale")
    public ProductPostSaleVo postSale(@RequestParam("productId") Long productId) {
        return remotePostSaleService.postSale(productId, HeaderUtils.getLanguage());
    }


}
