package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户下充电电量统计表(看板：Energy Charged图--柱状图使用)
 * ---Energy Charged统计租户所有Power HUB在统计时段内每天给电池充电的总电量，单位为kWh，并展示租户的所有设备（包含已删除设备）的历史总的充给电池的总电量和统计时段内有数据上报的日期的平均充给电池的电量；以下两个参数为基于此表数据汇总实时统计参数：Historical Total ：当前用户绑定的全部Charger在被当前租户绑定后的指定的时间段内的总充电电量(单位:KWh)
 * Daily Average: 设备被当前租户绑定后的指定时间段内的充电电量÷有充电数据的天数(t_bi_daily_energy_charged)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_daily_energy_charged")
public class BiDailyEnergyCharged extends Model<BiDailyEnergyCharged> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 日期：2023-07-12
     */
    private Date date;
    /**
     * 日期字符串: yyyy-MM-dd
     */
    private String strDate;
    /**
     * 每日充电总量(单位：kwh)
     */
    private Integer energyCharged;
    /**
     * 更新时间
     */
    private Date modifyTime;

    public void setId(){
        this.id=(long)(this.companyId.toString().concat(this.strDate)).hashCode();
    }
}