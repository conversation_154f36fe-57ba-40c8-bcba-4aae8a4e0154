package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.vo.GroupVo;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.dto.EquipmentSearchDto;
import com.chervon.fleet.web.entity.dto.TagSettingDto;
import com.chervon.fleet.web.entity.dto.TagSettingTagListDto;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.entity.vo.TagSettingTagListVo;
import java.util.List;

/**
 * 设备标签Service接口
 */
public interface DeviceTagService extends IService<DeviceTag> {

    /**
     * 删除指定设备id和租户的设备标签
     *
     * @param deviceId  设备id
     * @param companyId 租户id
     */
    void removeByDeviceIdAndCompany(String deviceId, Long companyId);

    /**
     * 根据设备id查询绑定标签列表
     * @param deviceId 设备id
     * @return 标签列表
     */
    List<TagVo> tagList(String deviceId);

    /**
     * 选中设备-打标签--标签数据查询，支持多个设备id，单个设备也适用
     *
     * @param deviceIds 设备id集合
     * @return 分组列表
     */
    List<GroupVo> tagSettingGroupList(List<String> deviceIds);

    /**
     * 选中设备-打标签--标签数据查询
     * @param req 查询条件
     * @return 标签列表
     */
    TagSettingTagListVo tagSettingTagList(TagSettingTagListDto req);

    /**
     * 选中设备-打标签操作
     * @param req 操作对象
     */
    void tagSetting(TagSettingDto req);

    /**
     * 过滤标签
     * @param req 请求对象
     * @param list 设备列表
     * @param companyId 租户id
     */
    void filterTag(EquipmentSearchDto req, List<CompanyDevice> list, Long companyId);
}
