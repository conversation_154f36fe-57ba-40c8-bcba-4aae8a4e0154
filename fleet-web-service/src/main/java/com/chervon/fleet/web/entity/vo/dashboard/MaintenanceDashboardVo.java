package com.chervon.fleet.web.entity.vo.dashboard;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import com.chervon.fleet.web.api.entity.enums.MaintenanceStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 维保服务看板Vo
 * <AUTHOR>
 * @since 2023-07-27 16:34
 **/
@Data
@Accessors(chain = true)
@ApiModel("维保服务看板Vo")
public class MaintenanceDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 维保状态枚举key
     * @see com.chervon.fleet.web.api.entity.enums.MaintenanceStatusEnum
     */
    @ApiModelProperty("维保状态枚举key")
    @Translate(type = ConvertType.ENUM,enumName = "MaintenanceStatusEnum",adapter = "I18NResource", targetField = {"serviceType"})
    private Integer key;
    @ApiModelProperty("维保类型名称：Service due、Normal、Off")
    private String serviceType;
    @ApiModelProperty("维保设备类型数量")
    private Integer serviceCount;

    public void setServiceType(String serviceType) {
        this.serviceType=serviceType;
        final Integer typeByDesc = MaintenanceStatusEnum.getTypeByDesc(serviceType);
        this.key = typeByDesc;
    }
}
