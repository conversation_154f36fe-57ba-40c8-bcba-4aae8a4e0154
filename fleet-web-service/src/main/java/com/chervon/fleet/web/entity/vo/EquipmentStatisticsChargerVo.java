package com.chervon.fleet.web.entity.vo;

import com.chervon.fleet.web.api.entity.vo.DashboardBatteryVo;
import com.chervon.fleet.web.api.entity.vo.PowerBankAvailabilityVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: EquipmentStatisticsChargerVo
 * @Description:充电器vo
 * <AUTHOR>
 * @date 2023/7/25 9:58
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsChargerVo implements Serializable {
    private static final long serialVersionUID = 1L;
    public EquipmentStatisticsChargerVo() {
        this.remainingChargingTime = "";
        this.completionTime = 0L;
        this.portUsedNumber = 0;
        this.portUsagePercentage = BigDecimal.ZERO;
        this.portTotalNumber = 0;
        this.totalBatteryNumber = 0;
        this.batteries = new ArrayList<>();
        this.bankBatteries=new ArrayList<>();
        this.chargingNumber = 0;
        this.chargingAh = BigDecimal.ZERO;
        this.readyNumber = 0;
        this.readyAh = BigDecimal.ZERO;
        this.availableAh=BigDecimal.ZERO;
        this.legacyAh=BigDecimal.ZERO;
        this.legacyBattery=0;
    }

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("剩余充电时长格式为13:41,代表13小时41分钟")
    private String remainingChargingTime;

    @ApiModelProperty("充电完成时间--时间戳")
    private Long completionTime;

    @ApiModelProperty("充电仓使用中数量")
    private Integer portUsedNumber;

    @ApiModelProperty("充电口使用百分比")
    private BigDecimal portUsagePercentage;

    @ApiModelProperty("充电仓总数")
    private Integer portTotalNumber;

    @ApiModelProperty("电池总数")
    private Integer totalBatteryNumber;

    @ApiModelProperty("PowerHub电池包列表")
    private List<DashboardBatteryVo> batteries;

    @ApiModelProperty("PowerBank电池包列表")
    private List<PowerBankAvailabilityVo> bankBatteries;

    @ApiModelProperty("PowerBank工作模式")
    private Integer workMode;

    @ApiModelProperty("正在充电数量")
    private Integer chargingNumber;

    @ApiModelProperty("待充电量总和安时数")
    private BigDecimal chargingAh;

    @ApiModelProperty("充电完成数据")
    private Integer readyNumber;

    @ApiModelProperty("充电完成电量")
    private BigDecimal readyAh;

    /**
     * 已充电量（Available）
     * 挂在充电器上的电量总和
     */
    @ApiModelProperty("已充电量（Available）")
    private BigDecimal availableAh;

    /**
     * 非标老电池包数量
     */
    @ApiModelProperty("非标老电池包数量")
    private Integer legacyBattery;
    /**
     * 非标老电池包总标称安时
     */
    @ApiModelProperty("非标老电池包总标称安时")
    private BigDecimal legacyAh;

    @ApiModelProperty("备注信息")
    private String remark;
}
