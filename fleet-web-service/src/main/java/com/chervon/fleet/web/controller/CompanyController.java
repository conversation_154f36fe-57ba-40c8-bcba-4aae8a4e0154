package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.dto.CompanyAddDto;
import com.chervon.fleet.user.api.entity.dto.CompanyAddWithoutTokenDto;
import com.chervon.fleet.user.api.entity.dto.CompanyCancelDto;
import com.chervon.fleet.user.api.entity.dto.CompanyNameDto;
import com.chervon.fleet.user.api.service.RemoteCompanyService;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.job.SumDailyUsageJob;
import com.chervon.fleet.web.service.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

/**
 * 企业租户相关接口
 *
 * <AUTHOR> 2023/6/28
 */
@Slf4j
@Api(tags = "企业租户")
@RestController
@RequestMapping("/company")
public class CompanyController {
    @DubboReference
    private RemoteCompanyService remoteCompanyService;
    @Lazy
    @Autowired
    private GroupService groupService;

    @Autowired
    private SumDailyUsageJob sumDailyUsageJob;

    @ApiOperation("创建并绑定企业")
    @PostMapping(value = "/createCompany")
    public R<Boolean> createCompany(@RequestBody CompanyAddDto dto) {
        Assert.hasText(dto.getCompanyName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_NAME);
        Long userId = UserContext.getUserId();
        Assert.notNull(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        // 验证企业名称是否重复,新建企业并绑定企业ID给指定user_id
        Long companyId = remoteCompanyService.add(dto.getCompanyName(), userId);
        // 创建默认的标签分组
        groupService.createDefaultGroup(companyId);
        return R.ok(true);
    }

    @ApiOperation("创建并绑定企业(不携带token)")
    @PostMapping(value = "/createCompany/withoutToken")
    public R<Boolean> createCompanyWithoutToken(@RequestBody CompanyAddWithoutTokenDto dto) {
        Assert.hasText(dto.getCompanyName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_NAME);
        Assert.notNull(dto.getUserId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        // 验证企业名称是否重复,新建企业并绑定企业ID给指定user_id
        Long companyId = remoteCompanyService.add(dto.getCompanyName(), dto.getUserId());
        // 创建默认的标签分组
        groupService.createDefaultGroup(companyId);
        return R.ok(true);
    }

    /**
     * 注销企业接口,前端给提示语，线下联系注销
     *
     * @return 注销结果
     */
    @PostMapping("/cancellation")
    public R<Boolean> accountCancellation(@RequestBody CompanyCancelDto requestDto) {

        return R.ok(true);
    }

    @ApiOperation("编辑名称")
    @PostMapping(value = "/editName")
    public R<Boolean> editName(@RequestBody CompanyNameDto requestDto) {
        // 验证企业名称是否重复,新建企业并绑定企业ID给指定user_id
        remoteCompanyService.editName(requestDto);
        return R.ok(true);
    }

    @ApiOperation(value = "设备每日使用量统计job执行")
    @PostMapping(value = "/dataDailyJob")
    public void sumDailyUsageJob(@RequestParam(required = false) String s) {
        sumDailyUsageJob.executeDataDailyUsageJob();
    }

    @ApiOperation(value = "设备每日使用量按租户绑定设备执行")
    @PostMapping(value = "/dataDailyJobByBind")
    public void dataDailyUsageJobByBind(@RequestParam String date,@RequestParam Long companyId,@RequestParam String category) {
        sumDailyUsageJob.dataDailyUsageByCompany(date,companyId,category);
    }
}
