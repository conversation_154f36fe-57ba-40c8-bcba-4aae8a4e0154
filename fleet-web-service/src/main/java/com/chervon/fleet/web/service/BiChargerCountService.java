package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiChargerCount;
import com.chervon.fleet.web.entity.vo.dashboard.ChargerCountDashboardVo;

import java.util.List;

/**
 * 充电器数量统计表（看板：total charge system饼状图）服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
public interface BiChargerCountService extends IService<BiChargerCount> {

    /**
     * 根据companyID获取PO列表
     * @param companyId 租户ID
     * @return PO列表
     */
    List<BiChargerCount> listByCompanyId(Long companyId);

    /**
     * 根据companyID获取PO列表
     * @param companyId 租户ID
     * @return PO列表
     */
    int countChargerByCompanyId(Long companyId);

    /**
     * 充电器统计看板查询
     * @param companyId 租户ID
     * @return 查询结果
     */
    List<ChargerCountDashboardVo> chargerCount(Long companyId);
}
