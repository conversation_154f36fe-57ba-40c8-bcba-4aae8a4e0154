package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.api.entity.query.GatewayQuery;
import com.chervon.fleet.web.api.entity.vo.GatewayVo;
import com.chervon.fleet.web.entity.dto.GatewayCache;
import com.chervon.fleet.web.entity.dto.GatewayPageDto;
import com.chervon.fleet.web.entity.po.AppGateway;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 11:50
 */
public interface AppGatewayService extends IService<AppGateway> {

    /**
     * * 查询网关列表
     *
     * @param query
     * @return
     */
    List<AppGateway> getList(GatewayQuery query);

    /**
     * * 查询网关详情
     *
     * @param query
     * @return
     */
    AppGateway get(GatewayQuery query);

    /**
     * * 查询网关详情
     *
     * @param gatewayId
     * @return
     */
    GatewayCache getByCache(String gatewayId);

    /**
     * 根据网关类型查看网关列表
     *
     * @param req 请求参数
     * @return 网关列表
     */
    PageResult<GatewayVo> listByType(GatewayPageDto req);

    /**
     * 移除网关
     *
     * @param gatewayId 网关id
     */
    void removeGateway(String gatewayId);
}
