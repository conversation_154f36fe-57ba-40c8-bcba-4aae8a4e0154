package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-14 16:55
 **/
@Data
@TableName("t_data_shadow_status")
public class ShadowStatus implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 设备总使用时长（单位：秒 不受绑定解绑影响）
     */
    private Integer totalUsageTime;
    /**
     * 设备总使用次数（不受绑定解绑影响
     */
    private Integer totalUsageNumber;
    /**
     * 设备最后上报数据时间戳
     */
    private Long lastUpdateTimestamp;
    /**
     * 更新时间
     */
    private Long modifyTime;
    /**
     * 充电百分比，物模型1011
     */
    private Integer energy;
    /**
     * 充电器状态，2016
     * 0: 保留
     * 1: 充电中
     * 2: 充电完成(或接插设
     * 备无需充电)
     * 3: 等待计划充电
     * 4:充电器过温
     * 5: 充电器故障
     * 6: 充电器高温慢充
     */
    private Integer chargerState;
    /**
     * 电池状态 2028
     * 0：保留
     * 1：充电中
     * 2：放电中
     * 3：充电满
     * 4：充电失败
     * 5：等待中（空闲）
     * 6：过温等待
     * 7：故障
     */
    private Integer batteryState;
    /**
     * 电池包健康度百分比
     */
    private Integer batteryHealth;
    /**
     * 变量性字段：
     * PowerBank工作模式:2017: 1:AC 2:DC 3待机  4故障
     */
    private String value1;
    /**
     * 变量性字段：
     * PowerBank AC剩余充电时间：分钟（2021->2001）
     */
    private String value2;

}
