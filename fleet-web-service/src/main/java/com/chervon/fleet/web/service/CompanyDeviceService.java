package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.api.entity.dto.CompanyDeviceDto;
import com.chervon.fleet.web.api.entity.query.CompanyDeviceQuery;
import com.chervon.fleet.web.api.entity.vo.CompanyDeviceVo;
import com.chervon.fleet.web.entity.dto.DeviceBasicCache;
import com.chervon.fleet.web.entity.dto.WarehouseStatusCache;
import com.chervon.fleet.web.entity.po.CompanyDevice;

import java.util.List;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @since 2023/07/05
 */
public interface CompanyDeviceService extends IService<CompanyDevice> {
    /**
     * 租户设备VO分页列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<CompanyDeviceVo> pageList(CompanyDeviceQuery query);

    /**
     * 租户设备PO列表
     *
     * @param query 查询条件
     * @return PO类列表
     */
    List<CompanyDevice> getList(CompanyDeviceQuery query);
    /**
     * 租户设备VO列表
     *
     * @param query 查询条件
     * @return VO类列表
     */
    List<CompanyDeviceVo> queryList(CompanyDeviceQuery query);

    /**
     * 查询租户设备PO详情
     *
     * @param query 查询条件
     * @return PO类
     */
    CompanyDevice get(CompanyDeviceQuery query);

    /**
     * 获取租户下PowerHub（在线）设备数量
     * @param companyId 租户id
     * @param isOnline 是否要求在线
     * @return 数量
     */
    long getPowerHubCount(Long companyId, Boolean isOnline);
    /**
     * 获取租户下PowerHub在线设备列表
     * @param companyId 租户id
     * @return
     */
    List<String> getHubOnlineList(Long companyId);
    /**
     * 获取设备在线状态
     * @param query
     * @return
     */
    CompanyDevice getDeviceOnlineInfo(CompanyDeviceQuery query);

    /**
     * 批量获取设备在线状态
     * @param query
     * @return
     */
    ConcurrentMap<String,CompanyDevice> getDeviceOnlineInfoMap(CompanyDeviceQuery query);

    /**
     * * 根据设备id获取库存状态
     *
     * @param deviceId 设备id
     * @return 状态缓存
     */
    WarehouseStatusCache getWarehouseStatusById(String deviceId);

    Integer clearWarehouseStatus(Long companyId);

    /**
     * * 基于缓存查询设备基础信息（临时缓存）
     *
     * @param deviceId 设备id
     * @return 设备基础信息
     */
    DeviceBasicCache getDeviceByCache(String deviceId);

    /**
     * * 根据租户查询设备关系列表（临时缓存）
     *
     * @param companyId 租户id
     * @return 设备列表
     */
    List<String> getDeviceListByCache(Long companyId);

    /**
     * 查询最大序号的租户设备信息
     *
     * @param companyId   租户id
     * @param cDeviceName 模糊匹配的设备名称
     * @return 租户设备信息
     */
    List<CompanyDevice> selectMaxOrder(Long companyId, String cDeviceName);

    /**
     * * 更新设备库存状态
     *
     * @param requestDto 请求对象
     */
    void updateDeviceStatus(CompanyDeviceDto requestDto);

    /**
     * * 更新设备在线离线状态
     *
     * @param requestDto 请求对象
     */
    void updateDeviceOnlineStatus(CompanyDeviceDto requestDto);

    /**
     * 根据租户id查询绑定设备列表，包含已解绑的设备
     *
     * @param companyId 租户id
     * @return 设备列表
     */
    List<CompanyDevice> listWithDeletedByCompanyId(Long companyId);

    /**
     * 查询已逻辑删除的设备信息列表
     * @param companyId
     * @param secondCategoryCodes
     * @param tagDeviceIds
     * @param excludeDeviceIds
     * @return
     */
    List<CompanyDevice> selectDeletedList(Long companyId,List<String> secondCategoryCodes,
                                                   List<String> tagDeviceIds,List<String> excludeDeviceIds);

    Integer getCategoryCountCache(Long companyId,String categoryCode);

    List<Long> getCompanyIdList();
}
