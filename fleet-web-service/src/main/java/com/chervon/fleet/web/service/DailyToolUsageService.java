package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.po.DailyToolUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolAverageDailyUsageVo;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolEnergyConsumptionVo;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsToolRuntimeFrequencyVo;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-14 17:29
 **/
public interface DailyToolUsageService extends IService<DailyToolUsage> {
    /**
     * 详情-statistics-工具类-Average Daily Usage
     *
     * @param req 请求参数
     * @return 看板数据
     */
    EquipmentStatisticsToolAverageDailyUsageVo detailStatisticsToolAverageDailyUsage(StatisticsQueryDto req);

    /**
     * 详情-statistics-工具类-Runtime&Frequency
     *
     * @param req 请求参数
     * @return 看板数据
     */
    List<EquipmentStatisticsToolRuntimeFrequencyVo> detailStatisticsToolRuntimeFrequency(StatisticsQueryDto req);

    /**
     * 详情-statistics-工具类-Energy Consumption
     *
     * @param req 请求参数
     * @return 看板数据
     */
    List<EquipmentStatisticsToolEnergyConsumptionVo> detailStatisticsToolEnergyConsumption(StatisticsQueryDto req);

    /**
     * 日统计数据入库
     * @param listToolDailyUsageVo
     */
    void saveDataDailyToolUsage(List<FleetToolDailyUsageVo> listToolDailyUsageVo);
}
