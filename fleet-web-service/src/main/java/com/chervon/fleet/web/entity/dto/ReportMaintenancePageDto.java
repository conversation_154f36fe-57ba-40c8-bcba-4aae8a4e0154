package com.chervon.fleet.web.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/25 14:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportMaintenancePageDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("开始时间")
    private Long start;

    @ApiModelProperty("结束时间")
    private Long end;

    @ApiModelProperty("选中的二级品类code集合，如果所属一级品类code被选中，则传一级下的所有二级品类code")
    private List<String> categoryCodes;

    /**
     * 区分客户端环境区域：欧洲还是北美：na eu
     */
    @ApiModelProperty("区域：欧洲还是北美：na eu")
    private String region;
}
