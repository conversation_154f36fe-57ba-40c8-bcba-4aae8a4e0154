package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet设备信息临时缓存结构
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@Accessors(chain = true)
@ApiModel("fleet设备基础信息临时缓存")
public class DeviceBasicCache implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 硬件网关对应的设备id
     */
    private String deviceId;
    /**
     * 设备Sn
     */
    private String deviceSn;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 租户id
     */
    private Long companyId;
}
