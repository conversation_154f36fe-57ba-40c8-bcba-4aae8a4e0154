package com.chervon.fleet.web.entity.enums;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 电池二级分类枚举
 * @Date: 2023/7/20 13:34
 */
public enum BatteryCategoryEnum {
    /**
     * 40AhBattery
     */
    B40("40AhBattery", new BigDecimal("40.0"), "40Ah Battery"),
    /**
     * 8AhBattery
     */
    B8("8AhBattery", new BigDecimal("8.0"), "8.0Ah Battery"),
    /**
     * 非标电池包
     */
    LEGACY("Legacy Battery",new BigDecimal("0"),"Legacy Battery");
    /**
     * 编号
     */
    private final String code;
    /**
     * 类型
     */
    private final BigDecimal capacity;
    /**
     * 值
     */
    private final String value;

    BatteryCategoryEnum(String code, BigDecimal capacity, String value) {
        this.code = code;
        this.capacity = capacity;
        this.value = value;
    }

    /**
     * 获取编号
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public BigDecimal getCapacity() {
        return capacity;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }


    /**
     * 通过描述获取值
     */
    public static BigDecimal getCapacityByCode(String code) {
        return Arrays.stream(values())
                .filter(x -> x.getCode().equals(code))
                .map(BatteryCategoryEnum::getCapacity)
                .findFirst()
                .orElse(BigDecimal.ZERO);
    }


    /**
     * 获取枚举类型值
     * @param code 编码
     * @return 返回值
     */
    public static BatteryCategoryEnum getEnum(String code) {
        return Arrays.stream(values())
                .filter(x -> x.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
