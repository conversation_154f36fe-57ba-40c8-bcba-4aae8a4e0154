package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.dto.TagDto;
import com.chervon.fleet.web.entity.po.Tag;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TagService extends IService<Tag> {

    /**
     * 分组下的最大标签数量为100
     */
    Integer TAG_MAX = 100;

    /**
     * 查询指定分组下的标签列表
     *
     * @param groupId 分组id
     * @return 标签列表
     */
    List<TagVo> listByGroupId(Long groupId);

    /**
     * 新建标签
     *
     * @param req 标签对象
     */
    void add(TagDto req);

    /**
     * 标签详情
     *
     * @param tagId 标签id
     * @return 标签详情
     */
    TagVo detail(Long tagId);

    /**
     * 编辑标签
     *
     * @param req 标签对象
     */
    void edit(TagDto req);

    /**
     * 删除标签
     *
     * @param tagId 标签id
     */
    void delete(Long tagId);

    /**
     * 根据companyId获取标签map
     * key: deviceId
     * value: TagVoList
     *
     * @param companyId 租户ID
     * @return deviceId-标签 MAP
     */
    Map<String, List<TagVo>> getTagVoMap(Long companyId, List<String> deviceIds);
}
