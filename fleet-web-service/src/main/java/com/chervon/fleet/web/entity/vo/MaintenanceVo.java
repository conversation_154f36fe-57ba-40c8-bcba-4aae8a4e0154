package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 维保对象
 * <AUTHOR>
 * @date 2023/7/21 10:51
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "维保对象")
public class MaintenanceVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 维保id
     */
    @ApiModelProperty("维保id")
    private Long maintenanceId;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("维保类型：1延保日期  2使用工时  3关闭")
    private Integer planType;
    /**
     * 界面上Next Service
     */
    @ApiModelProperty("延保日期：截止时间")
    private Long deadlineTime;

    @ApiModelProperty("使用工时：使用工时小时数计划值")
    private Integer usageHour;

    @ApiModelProperty("使用工时：已经使用的小时数")
    private Integer usedHour;

    @ApiModelProperty("使用工时：计划开始时间")
    private Long planBegin;

    @ApiModelProperty("维保状态 1 due 2 on 3 off")
    private Integer maintenanceStatus;

}
