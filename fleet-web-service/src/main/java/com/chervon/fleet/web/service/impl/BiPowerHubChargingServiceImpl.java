package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.fleet.web.api.entity.enums.ChargerStateEnum;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusEnum;
import com.chervon.fleet.web.api.entity.enums.WorkModeEnum;
import com.chervon.fleet.web.api.entity.query.CompanyDeviceQuery;
import com.chervon.fleet.web.api.entity.query.dashboard.ChargingSystemDetailDashboardQuery;
import com.chervon.fleet.web.api.entity.vo.DashboardBatteryVo;
import com.chervon.fleet.web.api.entity.vo.PowerBankAvailabilityVo;
import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.enums.BatteryCategoryEnum;
import com.chervon.fleet.web.entity.po.*;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsChargerVo;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingSystemOverviewDashboardVo;
import com.chervon.fleet.web.mapper.BiPowerBankChargingMapper;
import com.chervon.fleet.web.mapper.BiPowerHubChargingMapper;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.service.translate.TranslateUtils;
import com.chervon.fleet.web.utils.DataUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * power-hub充电状态统计表（看板：Charging System Overview图）服务接口实现
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiPowerHubChargingServiceImpl extends ServiceImpl<BiPowerHubChargingMapper, BiPowerHubCharging>
    implements BiPowerHubChargingService {
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;
    @Autowired
    private TagService tagService;
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private TranslateUtils translateUtils;
    @Autowired
    private BiPowerHubDetailService biPowerHubDetailService;
    @Autowired
    private BiPowerBankDetailService biPowerBankDetailService;
    @Autowired
    private BiPowerBankChargingMapper biPowerBankChargingMapper;
    @Autowired
    private ShadowStatusService shadowStatusService;
    @Override
    public List<BiPowerHubCharging> listByCompanyId(Long companyId) {
        List<BiPowerHubCharging> list = this.list(new LambdaQueryWrapper<BiPowerHubCharging>()
            .eq(BiPowerHubCharging::getCompanyId, companyId)
                .eq(BiPowerHubCharging::getSecondCategoryCode, ChargerCategoryEnum.PGX_HUB.getCode())
                .orderByDesc(BiPowerHubCharging::getCreateTime));
        for (BiPowerHubCharging biPowerHubCharging : list) {
            if (!Objects.equals(biPowerHubCharging.getChargerState(), ChargerStateEnum.OVERHEAT.getValue()) &&
                !Objects.equals(biPowerHubCharging.getChargerState(), ChargerStateEnum.ERROR.getValue())) {
                if (Objects.equals(biPowerHubCharging.getChargingPercentage(), new BigDecimal(NumberConst.PERCENTAGE))) {
                    // 充满
                    biPowerHubCharging.setChargerState(ChargerStateEnum.READY.getValue());
                }
                if (biPowerHubCharging.getChargingPortOccupy() == 0) {
                    // 空闲
                    biPowerHubCharging.setChargerState(ChargerStateEnum.EMPTY.getValue());
                }
            }
        }
        return list;
    }

    /**
     * powerhub设备列表
     * @param companyId 公司ID
     * @return PowerHubCharging列表
     */
    @Override
    public List<ChargingSystemOverviewDashboardVo> getPowerHubChargingList(Long companyId) {
        List<BiPowerHubCharging> biPowerHubChargingList = this.list(new LambdaQueryWrapper<BiPowerHubCharging>()
            .eq(BiPowerHubCharging::getCompanyId, companyId)
                .eq(BiPowerHubCharging::getSecondCategoryCode, ChargerCategoryEnum.PGX_HUB.getCode())
                .orderByDesc(BiPowerHubCharging::getCreateTime));
        if (CollectionUtils.isEmpty(biPowerHubChargingList)) {
            return Collections.emptyList();
        }
        //租户下所有PowerHub设备Id列表
        final List<String> hubDeviceIds = biPowerHubChargingList.stream().map(BiPowerHubCharging::getDeviceId).distinct().collect(Collectors.toList());
        Map<String, List<TagVo>> tagVoMap = tagService.getTagVoMap(companyId, hubDeviceIds);
        List<ChargingSystemOverviewDashboardVo> powerHubList = ConvertUtil.convertList(biPowerHubChargingList, ChargingSystemOverviewDashboardVo.class);
        final ConcurrentMap<String, CompanyDevice> mapOnline = companyDeviceService.getDeviceOnlineInfoMap(new CompanyDeviceQuery().setDeviceIds(hubDeviceIds));
        //获取hub,adaptor,battery所有故障列表
        Map<String, BiDeviceErrorList> latestErrorMap = biDeviceErrorListService.batchGetLatestErrorForPowerHub(companyId, hubDeviceIds);
        for (ChargingSystemOverviewDashboardVo powerHubVo : powerHubList) {
            // tags
            List<TagVo> tagVos = tagVoMap.getOrDefault(powerHubVo.getDeviceId(), new ArrayList<>());
            powerHubVo.setDeviceTag(tagVos.stream().map(TagVo::getTagName).collect(Collectors.joining("|")));
            //数据状态过期验证
            if(DataUtils.isOffline(mapOnline.get(powerHubVo.getDeviceId()))){
                powerHubVo.setChargerState(ChargerStateEnum.NO_DATA.getValue());
                powerHubVo.setChargingPercentage(BigDecimal.ZERO);
                powerHubVo.setChargingPortCount(0);
                powerHubVo.setChargingPortOccupy(0);
                powerHubVo.setHighCapacityBatteryCharging(0);
                powerHubVo.setHighCapacityBatteryReady(0);
                powerHubVo.setHighCapacityBatteryStandby(0);
                powerHubVo.setNextChargingComplete(0);
                powerHubVo.setNextHighChargingComplete(0);
                powerHubVo.setPortUsagePercentage(BigDecimal.ZERO);
                powerHubVo.setPortableBatteryCharging(0);
                powerHubVo.setPortableBatteryReady(0);
                powerHubVo.setPortableBatteryStandby(0);
                powerHubVo.setFaultCount(0);
                powerHubVo.setLatestErrorMessage("");
                powerHubVo.setSuggestionContent("");
                continue;
            }
            //设置充电状态
            setChargerStateValue(powerHubVo);
            // 如果错误数量大于0,查找最后发生的一条故障信息,且充电器优先于电池
            if (powerHubVo.getFaultCount() > 0) {
                BiDeviceErrorList biDeviceErrorList = latestErrorMap.get(powerHubVo.getDeviceId());
                if (biDeviceErrorList != null) {
                    powerHubVo.setLatestErrorMessage("(" + biDeviceErrorList.getErrorCode() + ")" + biDeviceErrorList.getErrorMessage());
                    powerHubVo.setSuggestionContent(biDeviceErrorList.getSuggestionContent());
                }else{//兼容故障码已经消失，但设备故障数延迟刷新的情况
                    powerHubVo.setFaultCount(0);
                }
            }

        }
        return powerHubList;
    }

    /**
     * 根据vo中信息补充chargerState字段
     *
     * @param powerHub 看板Vo
     */
    private static void setChargerStateValue(ChargingSystemOverviewDashboardVo powerHub) {
        if (!Objects.equals(powerHub.getChargerState(), ChargerStateEnum.OVERHEAT.getValue()) &&
            !Objects.equals(powerHub.getChargerState(), ChargerStateEnum.ERROR.getValue())) {
            if (powerHub.getChargingPortOccupy() == 0) {
                // 空闲
                powerHub.setChargerState(ChargerStateEnum.EMPTY.getValue());
            }
            if (Objects.equals(powerHub.getChargingPercentage(), new BigDecimal(NumberConst.PERCENTAGE))) {
                // 充电完成
                powerHub.setChargerState(ChargerStateEnum.READY.getValue());
            }
        }
    }

    @Override
    public ChargingSystemOverviewDashboardVo powerHubLeftDetail(ChargingSystemDetailDashboardQuery query) {
        BiPowerHubCharging biPowerHubCharging = this.getOne(new LambdaQueryWrapper<BiPowerHubCharging>()
            .eq(BiPowerHubCharging::getDeviceId, query.getDeviceId())
            .eq(BiPowerHubCharging::getSecondCategoryCode, ChargerCategoryEnum.PGX_HUB.getCode())
            .eq(BiPowerHubCharging::getCompanyId, query.getCompanyId())
            .last(StringConst.SQL_LIMIT));
        if (null == biPowerHubCharging) {
            return null;
        }
        ChargingSystemOverviewDashboardVo powerHubVo = ConvertUtil.convert(biPowerHubCharging, ChargingSystemOverviewDashboardVo.class);
        final Integer hour = biPowerHubCharging.getRemainingChargingTimestamp() / 60; //换算小时
        final Integer minutes = biPowerHubCharging.getRemainingChargingTimestamp() % 60; //换算成分钟
        //预计充电完成剩余时间小时分钟字符串表示：(4h 37min)
        powerHubVo.setReadyInTime(MessageFormat.format("{0}:{1}",hour,minutes));
        final CompanyDevice deviceStatus = companyDeviceService.getDeviceOnlineInfo(new CompanyDeviceQuery().setDeviceId(query.getDeviceId()));
        if(!DataUtils.isOffline(deviceStatus)) {
            setChargerStateValue(powerHubVo);
            // 如果错误数量大于0,查找最后发生的一条故障信息,且充电器优先于电池
            if (powerHubVo.getFaultCount() > 0) {
                BiDeviceErrorList latestError = biDeviceErrorListService.getLatestErrorForPowerHub(query.getCompanyId(), query.getDeviceId());
                translateUtils.translateListForeach(Arrays.asList(latestError));
                if (latestError != null && StringUtils.hasText(latestError.getErrorCode()) && StringUtils.hasText(latestError.getErrorMessage())) {
                    powerHubVo.setLatestErrorMessage("(" + latestError.getErrorCode() + ")" + latestError.getErrorMessage());
                    powerHubVo.setSuggestionContent(latestError.getSuggestionContent());
                }
                if (Objects.isNull(latestError)) {//兼容故障码已经消失，但设备故障数延迟刷新的情况
                    powerHubVo.setFaultCount(0);
                }
            }
        }else{
            powerHubVo.setChargerState(ChargerStateEnum.NO_DATA.getValue());
            powerHubVo.setChargingPercentage(BigDecimal.ZERO);
            powerHubVo.setChargingPortCount(0);
            powerHubVo.setChargingPortOccupy(0);
            powerHubVo.setHighCapacityBatteryCharging(0);
            powerHubVo.setHighCapacityBatteryReady(0);
            powerHubVo.setHighCapacityBatteryStandby(0);
            powerHubVo.setNextChargingComplete(0);
            powerHubVo.setNextHighChargingComplete(0);
            powerHubVo.setPortUsagePercentage(BigDecimal.ZERO);
            powerHubVo.setPortableBatteryCharging(0);
            powerHubVo.setPortableBatteryReady(0);
            powerHubVo.setPortableBatteryStandby(0);
            powerHubVo.setFaultCount(0);
            powerHubVo.setLatestErrorMessage("");
            powerHubVo.setReadyInTime("");
            powerHubVo.setChargerState(ChargerStateEnum.NO_DATA.getValue());
        }
        final Map<String, List<TagVo>> tagVoMap = tagService.getTagVoMap(query.getCompanyId(), Arrays.asList(query.getDeviceId()));
        List<TagVo> tagVos = CollectionUtils.isEmpty(tagVoMap)?new ArrayList<>(): tagVoMap.get(query.getDeviceId());
        powerHubVo.setDeviceTag(tagVos.stream().map(TagVo::getTagName).collect(Collectors.joining("|")));
        return powerHubVo;
    }

    /**
     * powerHub或PowerBank设备详情：兼容显示
     * @param deviceId 设备id
     * @return
     */
    @Override
    public EquipmentStatisticsChargerVo detailStatisticsCharger(String deviceId) {
        Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        final CompanyDevice companyDevice = companyDeviceService.get(new CompanyDeviceQuery().setDeviceId(deviceId).setCompanyId(companyId));
        if(Objects.isNull(companyDevice)){
            return null;
        }
        EquipmentStatisticsChargerVo result = new EquipmentStatisticsChargerVo();
        result.setDeviceId(deviceId);

        if(companyDevice.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode())) {
            if(Objects.isNull(companyDevice.getOnlineStatus()) || !companyDevice.getOnlineStatus().equals(OnlineStatusEnum.ONLINE.getType())){
                result.setRemark("PowerHub Offline");
                return result;
            }
            powerHubProcess(result, companyId,deviceId);
            return result;
        }
        //PowerBank处理
        return getPowerBankStatisticsChargerVo(deviceId, companyId, result);
    }

    @Nullable
    private EquipmentStatisticsChargerVo getPowerBankStatisticsChargerVo(String deviceId, Long companyId, EquipmentStatisticsChargerVo result) {
        BiPowerBankCharging powerBankCharging = biPowerBankChargingMapper.selectOne(new LambdaQueryWrapper<BiPowerBankCharging>()
                .eq(BiPowerBankCharging::getDeviceId, deviceId)
                .eq(BiPowerBankCharging::getCompanyId, companyId)
                .orderByDesc(BiPowerBankCharging::getModifyTime)
                .last(StringConst.SQL_LIMIT));
        if (Objects.isNull(powerBankCharging)) {
            result.setRemark("BiPowerBankCharging no bank data");
            return result;
        }
        if (Objects.isNull(powerBankCharging.getMode())) {
            result.setRemark("BiPowerBankCharging no bank workMode");
            return result;
        }
        result.setWorkMode(powerBankCharging.getMode());
        WorkModeEnum workMode = WorkModeEnum.getEnum(powerBankCharging.getMode());
        final List<BiPowerBankDetail> bankDetailList = getBiPowerBankDetails(deviceId, companyId);
        if (CollectionUtils.isEmpty(bankDetailList)) {
            result.setRemark("power bank biPowerHubDetailList empty,work mode:" + workMode.getDesc());
            return result;
        }
        if (workMode == WorkModeEnum.DC) {//蓝牙模式，过期时间12小时
            if (FleetDeviceConfig.isExpired12Hours(powerBankCharging.getModifyTime())) {
                result.setRemark("powerBank dc mode expired 12 hours");
                return result;
            }
        } else if (workMode == WorkModeEnum.AC) {
            if (FleetDeviceConfig.isExpired5Minutes(powerBankCharging.getModifyTime())) {
                result.setRemark("powerBank ac mode expired 5 Minutes");
                return result;
            }
        }
        return powerBankBuildResult(result, powerBankCharging, bankDetailList);
    }

    private List<BiPowerBankDetail> getBiPowerBankDetails(String deviceId, Long companyId) {
        LambdaQueryWrapper<BiPowerBankDetail> queryWrapper = new LambdaQueryWrapper<BiPowerBankDetail>()
                .eq(BiPowerBankDetail::getCompanyId, companyId)
                .eq(BiPowerBankDetail::getHubDeviceId, deviceId)
                .eq(BiPowerBankDetail::getAdaptorName, deviceId)
                .orderByAsc(BiPowerBankDetail::getPortNumber)
                .last("limit 3");
        List<BiPowerBankDetail> biPowerHubDetailList = biPowerBankDetailService.list(queryWrapper);
        return biPowerHubDetailList;
    }

    /**
     * PowerHubCharging 数据处理
     * @param result 返回对象
     * @param companyId 租户
     * @param deviceId 设备id
     */
    private void powerHubProcess(EquipmentStatisticsChargerVo result, Long companyId, String deviceId) {
        BiPowerHubCharging biPowerHubCharging = getOne(new LambdaQueryWrapper<BiPowerHubCharging>()
                .eq(BiPowerHubCharging::getDeviceId, deviceId)
                .eq(BiPowerHubCharging::getCompanyId, companyId)
                .orderByDesc(BiPowerHubCharging::getModifyTime)
                .last(StringConst.SQL_LIMIT));
        if (biPowerHubCharging == null || hubIsEffectiveOnline(biPowerHubCharging.getSecondCategoryCode(), biPowerHubCharging.getModifyTime())) {
            result.setRemark("BiPowerHubCharging no data");
            return;
        }
        result.setReadyNumber(biPowerHubCharging.getPortableBatteryReady() + biPowerHubCharging.getHighCapacityBatteryReady());
        result.setReadyAh(biPowerHubCharging.getReadyEnergyAh());
        result.setAvailableAh(biPowerHubCharging.getReadyEnergyAh());
        //合并充电中和等待的电池数量
        result.setChargingNumber(biPowerHubCharging.getPortableBatteryCharging() + biPowerHubCharging.getPortableBatteryStandby() + biPowerHubCharging.getHighCapacityBatteryCharging() + biPowerHubCharging.getHighCapacityBatteryStandby());
        result.setChargingAh(biPowerHubCharging.getChargingEnergyAh());

        // 对应UI部分: charging status
        //剩余充电时长：如果为0，则返回"0"字符串，有值的时候返回 3:45 形式
        result.setRemainingChargingTime(getRemainChargingTimeStrByMinutes(biPowerHubCharging.getRemainingChargingTimestamp()));
        //根据剩余充电时长计算完成时间戳：
        long timestamp = biPowerHubCharging.getRemainingChargingTimestamp() == 0 ? 0 : (System.currentTimeMillis() + (biPowerHubCharging.getRemainingChargingTimestamp() * NumberConst.ONE_MINUTE_MS));
        result.setCompletionTime(timestamp);

        result.setPortUsedNumber(biPowerHubCharging.getChargingPortOccupy());
        result.setPortTotalNumber(biPowerHubCharging.getChargingPortCount());
        result.setPortUsagePercentage(biPowerHubCharging.getPortUsagePercentage());

        //获取充电挂载详情：powerHub正常取2041，dc-dc设备特殊，ac模式取PowerHub的2041上报的物模型,dc模式取dc-dc自己的2041物模型
        LambdaQueryWrapper<BiPowerHubDetail> queryWrapper = new LambdaQueryWrapper<BiPowerHubDetail>()
                .eq(BiPowerHubDetail::getCompanyId, companyId)
                .eq(BiPowerHubDetail::getHubDeviceId, biPowerHubCharging.getDeviceId())
                .orderByAsc(BiPowerHubDetail::getBatteryType);
        List<BiPowerHubDetail> biPowerHubDetailList = biPowerHubDetailService.list(queryWrapper);
        if (CollectionUtils.isEmpty(biPowerHubDetailList)) {
            result.setTotalBatteryNumber(0);
            result.setBatteries(new ArrayList<>());
            return;
        }
        //查询BiPowerHubDetail明细表挂载电池信息
        if (hubIsEffectiveOnline(biPowerHubCharging.getSecondCategoryCode(), biPowerHubDetailList.get(0).getModifyTime())) {
            return;
        }
        setPowerHubBatteryInfo(result, biPowerHubDetailList);
    }

    /**
     * PowerHubCharging 数据处理
     * @param result 返回对象
     * @param powerBankCharging bank设备对象
     */
    private EquipmentStatisticsChargerVo powerBankBuildResult(EquipmentStatisticsChargerVo result, BiPowerBankCharging powerBankCharging, List<BiPowerBankDetail> biPowerHubDetailList) {
        // 对应UI部分: charging status
        //剩余充电时长：如果为0，则返回"0"字符串，有值的时候返回 3:45 形式
        result.setRemainingChargingTime(getRemainChargingTimeStrByMinutes(powerBankCharging.getRemainingChargingTimestamp()));
        //根据剩余充电时长计算完成时间戳：
        long timestamp = powerBankCharging.getRemainingChargingTimestamp() == 0 ? 0 : ( System.currentTimeMillis() + (powerBankCharging.getRemainingChargingTimestamp() * NumberConst.ONE_MINUTE_MS));
        result.setCompletionTime(timestamp);

        result.setPortTotalNumber(powerBankCharging.getChargingPortCount());
        result.setPortUsedNumber(powerBankCharging.getChargingPortOccupy());
        result.setPortUsagePercentage(powerBankCharging.getPortUsagePercentage());

        setPowerBankBatteryInfo(result,biPowerHubDetailList);
        return result;
    }

    /**
     * Hub是否在线并未过期:实时数据还未上报阶段
     * @param chargerCategory 分类
     * @param modifyTime 修改时间
     * @return
     */
    private static boolean hubIsEffectiveOnline(String chargerCategory, Date  modifyTime) {
        if((chargerCategory.equals(ChargerCategoryEnum.PGX_HUB.getCode()))
                && FleetDeviceConfig.isExpired5Minutes(modifyTime)){
            return true;
        }
        return false;
    }

    /**
     * 设置充电器电池信息
     * @param result
     * @param biPowerHubDetailList
     */
    private static void setPowerHubBatteryInfo(EquipmentStatisticsChargerVo result, List<BiPowerHubDetail> biPowerHubDetailList) {
        Integer sum = 0;
        Map<String, List<BiPowerHubDetail>> powerHubBatteryMap = biPowerHubDetailList.stream()
                .filter(a->StringUtils.hasText(a.getBatteryDeviceId())).collect(Collectors.groupingBy(BiPowerHubDetail::getBatteryType));
        List<DashboardBatteryVo> dashboardBatteryVos=new ArrayList<>();
        BigDecimal legacyAh=BigDecimal.ZERO;
        for (Map.Entry<String, List<BiPowerHubDetail>> entry : powerHubBatteryMap.entrySet()) {
            DashboardBatteryVo batteryVo = new DashboardBatteryVo();
            batteryVo.setName(entry.getKey());
            batteryVo.setCount(entry.getValue().size());
            if(entry.getKey().equals(BatteryCategoryEnum.LEGACY.getCode())){
                final Optional<BiPowerHubDetail> first = entry.getValue().stream().filter(a -> a.getBatteryType().equals(BatteryCategoryEnum.LEGACY.getCode()) && a.getChargingEnergyAh().compareTo(BigDecimal.ZERO) > 0).findFirst();
                if(first.isPresent()){
                    legacyAh=first.get().getChargingEnergyAh();
                    batteryVo.setTotalAh(legacyAh);
                }
            }
            sum+=batteryVo.getCount();
            dashboardBatteryVos.add(batteryVo);
        }
        //计算非标电池包总安时
        result.setLegacyAh(legacyAh);
        result.setBatteries(dashboardBatteryVos);
        result.setTotalBatteryNumber(sum);
    }

    /**
     * 设置充电器电池信息
     * @param result
     * @param details
     */
    private static void setPowerBankBatteryInfo(EquipmentStatisticsChargerVo result, List<BiPowerBankDetail> details) {
        List<PowerBankAvailabilityVo> dashboardBatteryVos=new ArrayList<>();
        for (BiPowerBankDetail detail : details) {
            PowerBankAvailabilityVo batteryVo = new PowerBankAvailabilityVo();
            batteryVo.setIndex(detail.getPortNumber());
            batteryVo.setBatteryType(detail.getBatteryType());
            batteryVo.setChargingStatus(detail.getPortStatus());
            batteryVo.setChargingPercentage(detail.getChargingPercentage());
            dashboardBatteryVos.add(batteryVo);
        }
        result.setBankBatteries(dashboardBatteryVos);
    }

    /**
     * 根据分钟数获取14:32格式的小时:分钟字符串
     * 如果只有2分钟,则返回0:2
     *
     * @param minutes 分钟数
     * @return 14:32格式的小时:分钟字符串
     */
    public static String getRemainChargingTimeStrByMinutes(Integer minutes) {
        if (null != minutes && minutes > 0) {
            int h = minutes / NumberConst.ONE_MINUTE_SECOND;
            int min = minutes % NumberConst.ONE_MINUTE_SECOND;
            return h + ":" + min;
        }
        return "0";
    }
}