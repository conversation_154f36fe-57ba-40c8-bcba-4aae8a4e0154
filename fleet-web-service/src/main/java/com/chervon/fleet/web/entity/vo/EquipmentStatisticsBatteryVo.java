package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: EquipmentStatisticsBatteryVo
 *  @Description:统计电池包设备的相关信息
 * <AUTHOR>
 * @date 2023/7/25 10:16
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsBatteryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("Total Number Of Charging Cycles")
    private Integer totalNumberOfChargingCycles;
    /**
     * 总充电时长：单位：分钟
     */
    @ApiModelProperty("Total Charging Time")
    private Integer totalChargingTime;
    /**
     * 总放电时长：单位：分钟
     */
    @ApiModelProperty("Total Discharging Time")
    private Integer totalDischargingTime;
}
