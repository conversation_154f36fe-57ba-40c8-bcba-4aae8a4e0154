package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 通用数据字典表(t_data_dictionary)实体类
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_data_dictionary")
public class DataDictionary implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 父id
     */
    private Long parentId;
    /**
     * 数据分组
     */
    private String dataGroup;
    /**
     * 设置类型描述
     */
    private String groupDesc;
    /**
     * 设置名称
     */
    private String dataName;
    /**
     * 设置值
     */
    private String dataValue;
    /**
     * 配置备注
     */
    private String remark;
    /**
     * 排序编号
     */
    private Integer sortId;
    /**
     * 是否启用：0：未启用，1：启用
     */
    private Boolean enable;

    /**
     * 创建人姓名
     */
    private String createUser;
    /**
     * 更新人姓名
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;
}