package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12 20:34
 */
@Data
@ApiModel(description = "设备id集合，选中的分组")
public class TagSettingTagListDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备id集合")
    private List<String> deviceIds;

    @ApiModelProperty("分组id")
    private Long groupId;
}
