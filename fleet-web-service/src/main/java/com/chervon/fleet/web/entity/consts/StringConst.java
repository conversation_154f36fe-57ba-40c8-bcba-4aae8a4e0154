package com.chervon.fleet.web.entity.consts;

import java.util.regex.Pattern;

import static com.chervon.common.core.utils.DateUtils.YYYY_MM_DD_REG;

/**
 * <AUTHOR> 2022/10/17
 */
public class StringConst {
    public static final Pattern YMD_REG = Pattern.compile(YYYY_MM_DD_REG);
    private static final String FORMAT_H_MIN = "[0-9]+h[*\\s][0-9]+min";
    public static final Pattern REG_H_MIN = Pattern.compile(FORMAT_H_MIN);
    public static final String DEFAULT_LANGUAGE_HEAD="en";

    public static final String EMAIL_FORMAT="^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$";

    //发布设备库存状态刷新topic主题链接url
    public static final String DEVICE_STATUS_REFRESH_GET_ACCEPTED = "aws/fleet/gateway/scanStatus/get/accepted";

    public static final String GATEWAY_DEVICE="gatewayDevice";

    /**
     * 以下故障码提示语常量声明 **********************************************
     */
    public static final String ID="id";
    public static final String UID="uid";
    public static final String USER_ID="userId";
    public static final String LANGUAGE="language";
    public static final String COMPANY_ID="companyId";
    public static final String COMPANY_NAME = "companyName";
    public static final String DEVICE_ID="deviceId";
    public static final String REQUEST_DTO="requestDto";
    public static final String TYPE="type";
    public static final String EMAIL="email";
    public static final String FIRST_NAME="firstName";
    public static final String LAST_NAME="lastName";
    public static final String PASSWORD="password";
    public static final String START_TIME="startTime";
    public static final String NEW_PASSWORD="newPassword";
    public static final String OLD_PASSWORD="oldPassword";
    public static final String END_TIME="endTime";
    public static final String VERIFICATION_CODE="verificationCode";
    public static final String MAINTENANCE_LOG_ID="maintenanceLogId";
    public static final String RUNTIME = "runtime";
    public static final String RUNTIME_SHOW = "runtimeShow";

    /**
     * 以下多语言资源key常量声明 **********************************************
     */
    public static final String LANG_KEY_HOURS="Hours";

    public static final String DEFAULT_TAG_KEY="My_Tags";

    //格式化字符:区分欧洲和北美
    public static final String TIME_NA_PATTERN ="MM/dd/yyyy HH:mm:ss";
    public static final String TIME_EU_PATTERN ="dd/MM/yyyy HH:mm:ss";
    //带AM/PM的时间格式
    public static final String TIME_WITH_AM_NA_PATTERN ="hh:mm:ss a MM/dd/yyyy";
    public static final String TIME_WITH_AM_EU_PATTERN ="hh:mm:ss a dd/MM/yyyy";
    //日期格式，区分欧洲和北美
    public static final String DATE_NA_PATTERN ="MM/dd/yyyy";
    public static final String DATE_EU_PATTERN ="dd/MM/yyyy";

    public static final String WAREHOUSE_STATUS_ENUM="WarehouseStatusEnum";
    public static final String MAINTENANCE_LOG_SERVICE_TYPE_ENUM="MaintenanceLogServiceTypeEnum";


    public static final String INVENTORY_TITLE="INVENTORY_TITLE";
    public static final String MAINTENANCE_TITLE="MAINTENANCE_TITLE";
    public static final String USAGE_TITLE="USAGE_TITLE";
    public static final String _EVENT_MAINTENANCE = "event-mts:";

    public static final String GATEWAY_NAME = "gatewayName";
    public static final String COORDINATE = "coordinate";
    public static final String LOCATION = "location";
    public static final String UNIQUE_ID = "uniqueId";
    public static final String GATEWAY_TYPE = "gatewayType";
    public static final String GATEWAY_ID = "gatewayId";
    public static final String SEARCH_TYPE = "searchType";
    public static final String DEVICE_IDS = "deviceIds";
    public static final String SERVICE_TYPE = "serviceType";
    public static final String GROUP_ID = "groupId";
    public static final String TAG_ID = "tagId";

    //"wifi", "4G"
    public static final String NETWORK_WIFI = "wifi";
    public static final String NETWORK_4G = "4G";
    public static final String NOT_IOT_DEVICE = "notIotDevice";

    public static final String BATTERY_TYPE_0="0Ah";
    public static final String UNKNOWN_BATTERY_TYPE="--Ah";

    public static final String SQL_LIMIT = "limit 1";


    //执行汇总统计的指令：
    public static String CMD_BATTERY="battery";
    public static String CMD_TOOL = "tool";
    public static String CMD_CHARGER = "charger";

    public static String HISTORY_DATE="history_date";
}