package com.chervon.fleet.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import com.chervon.fleet.web.api.entity.query.dashboard.DailyCategoryUsageDashboardQuery;
import com.chervon.fleet.web.api.entity.query.dashboard.DeviceErrorListDashboardQuery;
import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;
import com.chervon.fleet.web.api.entity.vo.FleetCategoryVo;
import com.chervon.fleet.web.api.entity.vo.InventoryStatusDashboardVo;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.vo.FleetCategoryFilterVo;
import com.chervon.fleet.web.entity.vo.dashboard.CategoryCountDashboardVo;
import com.chervon.fleet.web.entity.vo.dashboard.DailyCategoryUsageDashboardVo;
import com.chervon.fleet.web.entity.vo.dashboard.DeviceErrorListDashboardVo;
import com.chervon.fleet.web.entity.vo.dashboard.MaintenanceDashboardVo;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.service.translate.TranslateUtils;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * web库存看板相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@Api(tags = "库存看板")
@Slf4j
@RestController
@RequestMapping("/dashboard/inventory")
public class DashboardInventoryController {
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private BiInventoryStatusService biInventoryStatusService;
    @Autowired
    private BiMaintenanceService biMaintenanceService;
    @Autowired
    private BiCategoryCountService biCategoryCountService;
    @Autowired
    private BiDailyCategoryUsageService biDailyCategoryUsageService;
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;
    @Lazy
    @Autowired
    private FilterConditionService filterConditionService;
    @Autowired
    private TranslateUtils translateUtils;

    @ApiOperation("检测库存设备数量,false表示当前公司没有设备")
    @PostMapping("/check")
    @WebApiHeaders
    public Boolean checkInventory() {
        List<String> list = Arrays.asList(FleetFirstCategoryEnum.HANDHELD.getCategoryCode(),
            FleetFirstCategoryEnum.RIDE_ON.getCategoryCode(),
            FleetFirstCategoryEnum.POWER_UNIT.getCategoryCode(),
            FleetFirstCategoryEnum.WALK_BEHIND.getCategoryCode());
        long companyDevices = companyDeviceService.count(new LambdaQueryWrapper<CompanyDevice>()
            .eq(CompanyDevice::getCompanyId, UserContext.getCompanyId())
            .in(CompanyDevice::getFirstCategoryCode, list));
        return companyDevices > 0;
    }

    @ApiOperation(value = "库存状态")
    @PostMapping(value = "/inventory")
    @WebApiHeaders
    public R<List<InventoryStatusDashboardVo>> inventoryStatus() {
        List<InventoryStatusDashboardVo> result = biInventoryStatusService.inventoryStatus(UserContext.getCompanyId());
        translateUtils.translateListForeach(result);
        return R.ok(result);
    }

    @ApiOperation(value = "维保服务")
    @PostMapping(value = "/maintenance")
    @WebApiHeaders
    public R<List<MaintenanceDashboardVo>> maintenance() {
        List<MaintenanceDashboardVo> result = biMaintenanceService.maintenance(UserContext.getCompanyId());
        translateUtils.translateListForeach(result);
        return R.ok(result);
    }

    @ApiOperation(value = "设备分类数量")
    @PostMapping(value = "/category/count")
    @WebApiHeaders
    public R<List<CategoryCountDashboardVo>> categoryCount() {
        List<CategoryCountDashboardVo> result = biCategoryCountService.categoryCount(UserContext.getCompanyId());
        translateUtils.translateListBatch(result);
        return R.ok(result);
    }

    @ApiOperation(value = "inventory overview 看板分类每日使用量柱状图category下拉框")
    @GetMapping(value = "/all/category")
    public List<FleetCategoryFilterVo> filterCondition() {
        Long companyId = UserContext.getCompanyId();
        FilterConditionVo filterConditionVo = filterConditionService.filterCondition(companyId, false, true, false, false, false, null);
        List<FleetCategoryVo> categories = filterConditionVo.getCategories();
        List<FleetCategoryFilterVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(categories)) {
            return result;
        }
        for (FleetCategoryVo category : categories) {
            //排除充电器和电池包
            if(category.getCategoryCode().equals(FleetFirstCategoryEnum.CHARGER.getCategoryCode()) ||
                    category.getCategoryCode().equals(FleetFirstCategoryEnum.BATTERY.getCategoryCode())){
                continue;
            }
            result.add(ConvertUtil.convert(category, FleetCategoryFilterVo.class));
        }
        return result;
    }

    @ApiOperation(value = "每日设备分类使用情况")
    @PostMapping(value = "/daily/category/usage")
    @WebApiHeaders
    public R<List<DailyCategoryUsageDashboardVo>> dailyCategoryUsage(@RequestBody DailyCategoryUsageDashboardQuery query) {
        query.setCompanyId(UserContext.getCompanyId());
        Assert.notEmpty(query.getCategoryCodeList(), ErrorCode.PARAMETER_ERROR, "categoryCodeList");
        Assert.notNull(query.getStartTime(), ErrorCode.PARAMETER_ERROR, StringConst.START_TIME);
        Assert.notNull(query.getEndTime(), ErrorCode.PARAMETER_ERROR, StringConst.END_TIME);
        List<DailyCategoryUsageDashboardVo> result = biDailyCategoryUsageService.dailyCategoryUsage(query);
        return R.ok(result);
    }

    @ApiOperation(value = "设备错误列表")
    @PostMapping(value = "/device/error/list")
    @WebApiHeaders
    public R<DeviceErrorListDashboardVo> deviceErrorList(@RequestBody DeviceErrorListDashboardQuery query) {
        query.setCompanyId(UserContext.getCompanyId());
        DeviceErrorListDashboardVo result = biDeviceErrorListService.inventoryErrorList(query);
        return R.ok(result);
    }
}
