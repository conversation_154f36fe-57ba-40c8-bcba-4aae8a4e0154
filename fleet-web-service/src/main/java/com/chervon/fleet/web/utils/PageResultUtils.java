package com.chervon.fleet.web.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.common.core.domain.PageResult;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
public class PageResultUtils {
    /**
     * 转换mybatis plus page 2 PagedList
     *
     * @param page mybatis plus page
     * @param <E>  类型参数
     * @return 页面列表
     */
    public static <E> PageResult<E> page2PagedList(IPage<E> page) {
        List<E> list = null;
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            list = page.getRecords();
        }
        PageResult<E> resultPage = new PageResult<>();
        resultPage.setTotal((int) page.getTotal());
        resultPage.setPageNum((int) page.getCurrent());
        resultPage.setPageSize((int) page.getSize());
        resultPage.setList(list);

        return resultPage;
    }

    /**
     * 将list列表按指定数量分组成子列表
     *
     * @param list      原始列表
     * @param groupSize 分组数量
     * @return 分组后的列表
     */
    public static <T> List<List<T>> manualPartition(List<T> list, int groupSize) {
        List<List<T>> partitionedList = new ArrayList<>();
        int listSize = list.size();
        for (int i = 0; i < listSize; i += groupSize) {
            partitionedList.add(list.subList(i, Math.min(i + groupSize, listSize)));
        }
        return partitionedList;
    }

}
