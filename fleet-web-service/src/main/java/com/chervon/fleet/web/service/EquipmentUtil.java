package com.chervon.fleet.web.service;

import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.EquipmentSearchDto;
import com.chervon.fleet.web.entity.dto.GatewayCache;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.vo.EquipmentVo;
import com.chervon.fleet.web.utils.EquipmentSettingUtil;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * equipment util
 * <AUTHOR> 2024/2/5
 */
@Service
@Slf4j
public class EquipmentUtil {
    @Autowired
    private RuleEngineService ruleEngineService;
    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;
    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;
    @Autowired
    private AppGatewayService appGatewayService;
    @Lazy
    @Autowired
    private DeviceTagService deviceTagService;

    /**
     * 根据查询条件过滤设备
     * @param req
     * @param list
     * @param companyId
     */
    public void deviceFilterByCondition(EquipmentSearchDto req, List<CompanyDevice> list, Long companyId) {
        // 设备名称条件
        if (!list.isEmpty() && StringUtils.hasText(req.getDeviceName())) {
            list.removeIf(e -> StringUtils.isEmpty(e.getDeviceName()) || !e.getDeviceName().toLowerCase().contains(req.getDeviceName().toLowerCase()));
        }
        // 品类条件
        if (!list.isEmpty() && !CollectionUtils.isEmpty(req.getCategoryCodes())) {
            list.removeIf(e -> StringUtils.isEmpty(e.getSecondCategoryCode()) || !req.getCategoryCodes().contains(e.getSecondCategoryCode()));
        }
        // 在线状态
        EquipmentSettingUtil.filterOnlineStatus(req, list);
        // 维保状态
        EquipmentSettingUtil.filterMaintenanceStatus(req, list);
        // 设备标签条件
        deviceTagService.filterTag(req, list, companyId);
        // 库存条件
        EquipmentSettingUtil.filterInventoryStatus(req, list);
        // 网关条件
        EquipmentSettingUtil.filterGateway(req, list);
    }


    /**
     * 设置设备的产品信息
     * @param e
     * @param productMap
     * @param vo
     */
    public void setProductInfo(CompanyDevice e, Map<Long, ProductCache> productMap, EquipmentVo vo) {
        ProductCache productCache = productMap.get(e.getProductId());
        if (Objects.isNull(productCache)) {
            return;
        }
        vo.setProductIconUrl(productCache.getUrl());
        vo.setCommodityModel(productCache.getCommodityModel());
        // 判断是否是网关设备
        if (!StringConst.GATEWAY_DEVICE.equals(productCache.getType())) {
            return;
        }
        vo.setLocation(null);
        vo.setLatitude(null);
        vo.setLongitude(null);
        // 网关设备取网关信息
        GatewayCache gatewayCache = appGatewayService.getByCache(e.getDeviceId());
        if (Objects.isNull(gatewayCache)) {
            return;
        }
        EquipmentSettingUtil.setGatewayLocation(vo, gatewayCache.getLocation(), gatewayCache.getCoordinate());
    }



    /**
     * 设置设备分类信息
     * @param companyDevice
     * @param equipmentVo
     */
    public void setFleetCategoryInfo(CompanyDevice companyDevice, EquipmentVo equipmentVo) {
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCode(companyDevice.getSecondCategoryCode());
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        Map<String, String> fleetCategoryMap = fleetCategoryList.stream().collect(HashMap::new, (map, ca) -> map.put(ca.getCode(), ca.getCategoryName()), HashMap::putAll);
        equipmentVo.setCategoryName(fleetCategoryMap.get(companyDevice.getSecondCategoryCode()));
        // 设置详情类型
        equipmentVo.setDetailType(companyDevice.getCustomType());
    }

    /**
     * 设置网关信息
     * @param equipmentVo
     * @param companyDevice
     */
    public void setCompanyDeviceInfo(EquipmentVo equipmentVo, CompanyDevice companyDevice) {
        equipmentVo.setGatewayId(companyDevice.getGatewayId());
        if (companyDevice.getGatewayId() != null) {
            AppGateway appGateway = appGatewayService.getById(companyDevice.getGatewayId());
            if (appGateway != null) {
                equipmentVo.setGatewayName(appGateway.getName());
            }
        }
        // 在线状态
        equipmentVo.setOnlineStatus(companyDevice.getOnlineStatus());
        // 库存状态
        equipmentVo.setMaintenanceStatus(companyDevice.getMaintenanceStatus());
        equipmentVo.setDeviceId(companyDevice.getDeviceId());
        equipmentVo.setDeviceSn(companyDevice.getDeviceSn());
        equipmentVo.setDeviceName(companyDevice.getDeviceName());
        equipmentVo.setCategoryCode(companyDevice.getSecondCategoryCode());
        equipmentVo.setProductId(companyDevice.getProductId());
        // 设备添加时间
        equipmentVo.setAddTime(companyDevice.getBindingTime());
    }

    /**
     * 设置产品信息
     * @param companyDevice 租户设备对象
     * @param equipmentVo 设备返回对象
     */
    public void setProductInfo(CompanyDevice companyDevice, EquipmentVo equipmentVo) {
        ProductCache productCache = remoteOperationCacheService.getProduct(companyDevice.getProductId());
        if (Objects.isNull(productCache)) {
            return;
        }
        equipmentVo.setProductIconUrl(productCache.getUrl());
        equipmentVo.setCommodityModel(productCache.getCommodityModel());
        // 判断是否是网关设备
        if (!StringConst.GATEWAY_DEVICE.equals(productCache.getType())) {
            return;
        }
        equipmentVo.setLocation(null);
        equipmentVo.setLatitude(null);
        equipmentVo.setLongitude(null);
        // 网关设备取网关信息
        GatewayCache gatewayCache = appGatewayService.getByCache(companyDevice.getDeviceId());
        if (Objects.isNull(gatewayCache)) {
            return;
        }
        equipmentVo.setGatewayName(gatewayCache.getName());
        EquipmentSettingUtil.setGatewayLocation(equipmentVo, gatewayCache.getLocation(), gatewayCache.getCoordinate());
    }

    /**
     * 设置设备库存状态
     * @param deviceId
     * @param companyId
     * @param equipmentVo
     */
    public void setInventoryStatus(String deviceId, Long companyId, EquipmentVo equipmentVo) {
        InventoryStatusQuery query = new InventoryStatusQuery();
        query.setCompanyId(companyId).setListDeviceId(Collections.singletonList(deviceId));
        List<DeviceStatusVo> deviceInventoryStatus = ruleEngineService.getWarehouseStatusOnly(query);
        if (CollectionUtils.isEmpty(deviceInventoryStatus)) {
            return;
        }
        DeviceStatusVo deviceStatusVo = deviceInventoryStatus.get(0);
        //设备位置信息赋值
        EquipmentSettingUtil.setEquipmentLocation(equipmentVo, deviceStatusVo);
    }
}
