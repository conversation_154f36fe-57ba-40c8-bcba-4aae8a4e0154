package com.chervon.fleet.web.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * 电池包物模型状态枚举
 * 2028
 * <AUTHOR>
 * @date 2024/2/14 14:00
 */
public enum ShadowBatteryStatusEnum implements TypeEnum {
    /**
     * 离线
     */
    DOWN(0, "离线"),
    /**
     * 在线
     */
    UP(1, "在线");

    private int type;
    private String desc;

    ShadowBatteryStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
