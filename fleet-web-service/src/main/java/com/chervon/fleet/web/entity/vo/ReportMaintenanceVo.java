package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: ReportMaintenanceVo
 * @Description:维保报表对象
 * <AUTHOR>
 * @date 2023/7/25 14:06
 */
@Data
@Accessors(chain = true)
public class ReportMaintenanceVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备图片
     */
    @ApiModelProperty("设备图片")
    private String picture;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备sn")
    private String sn;

    @ApiModelProperty("二级品类")
    private String category;

    @ApiModelProperty("商品型号")
    private String model;

    @ApiModelProperty("Service Type")
    private String serviceType;

    @ApiModelProperty("Maintenance Date")
    private Long maintenanceDate;

    @ApiModelProperty("Runtime")
    private String runtime;

    @ApiModelProperty("Labor Time")
    private String laborTime;

    @ApiModelProperty("Expenses")
    private String expenses;

    @ApiModelProperty("Performed By")
    private String performedBy;

    @ApiModelProperty("Contents")
    private String contents;

}
