package com.chervon.fleet.web.utils;

import com.chervon.fleet.web.entity.consts.NumberConst;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2023/8/26 13:55
 */
public class GeoCoordinateUtil {

    public static Map<String, BigDecimal> getCenterPoint(List<Map<String, BigDecimal>> points) {
        if (CollectionUtils.isEmpty(points)) {
            return new HashMap<>();
        }
        int total = points.size();
        if (total == 1) {
            return points.get(0);
        }
        double sumX = 0, sumY = 0, sumZ = 0;
        for (Map<String, BigDecimal> g : points) {
            double lat, lon, x, y, z;
            lat = g.get("lat").doubleValue() * Math.PI / NumberConst.HUNDRED_EIGHTY;
            lon = g.get("lon").doubleValue() * Math.PI / NumberConst.HUNDRED_EIGHTY;
            x = Math.cos(lat) * Math.cos(lon);
            y = Math.cos(lat) * Math.sin(lon);
            z = Math.sin(lat);
            sumX += x;
            sumY += y;
            sumZ += z;
        }
        sumX = sumX / total;
        sumY = sumY / total;
        sumZ = sumZ / total;
        double lon = Math.atan2(sumY, sumX);
        double hyp = Math.sqrt(sumX * sumX + sumY * sumY);
        double lat = Math.atan2(sumZ, hyp);
        Map<String, BigDecimal> res = new ConcurrentHashMap<>();
        res.put("lat", new BigDecimal(String.valueOf(lat * NumberConst.HUNDRED_EIGHTY / Math.PI)));
        res.put("lon", new BigDecimal(String.valueOf(lon * NumberConst.HUNDRED_EIGHTY / Math.PI)));
        return res;
    }

}
