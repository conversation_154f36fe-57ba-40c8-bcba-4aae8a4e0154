package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.po.DailyBatteryUsage;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsBatteryEnergyVo;
import com.chervon.fleet.web.entity.vo.EquipmentStatisticsBatteryTimeVo;
import com.chervon.iot.middle.api.vo.usage.FleetBatteryDailyUsageVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-08-15 17:29
 **/
public interface DailyBatteryUsageService extends IService<DailyBatteryUsage> {

    /**
     * 详情-statistics-电池-Charging & Discharging Time
     *
     * @param req 请求对象
     * @return 看板数据
     */
    List<EquipmentStatisticsBatteryTimeVo> detailStatisticsBatteryTime(StatisticsQueryDto req);

    /**
     * 详情-statistics-电池-Charging & Discharging Energy
     *
     * @param req 请求对象
     * @return 看板数据
     */
    List<EquipmentStatisticsBatteryEnergyVo> detailStatisticsBatteryEnergy(StatisticsQueryDto req);

    /**
     * 获取电量统计数据
     * @param req 请求对象
     * @return 统计数据
     */
    Map<String, DailyBatteryUsage> getStringDailyBatteryUsageMap(StatisticsQueryDto req);

    /**
     * * 批量保存入库
     * @param listBatteryDailyUsageVo
     */
    void saveDataDailyBatteryUsage(List<FleetBatteryDailyUsageVo> listBatteryDailyUsageVo);
}
