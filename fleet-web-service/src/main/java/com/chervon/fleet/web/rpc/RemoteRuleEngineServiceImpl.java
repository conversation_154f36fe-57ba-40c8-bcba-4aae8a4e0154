package com.chervon.fleet.web.rpc;

import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.fleet.web.api.entity.dto.DataReportCompleteDto;
import com.chervon.fleet.web.api.entity.dto.GatewayScanReportingDto;
import com.chervon.fleet.web.api.entity.dto.IotUpdateOnlineStatusDto;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusTypeEnum;
import com.chervon.fleet.web.api.service.RemoteRuleEngineService;
import com.chervon.fleet.web.service.RuleEngineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.Duration;
import java.util.Objects;

/**
 * 远程规则引擎服务
 * <AUTHOR>
 * @date 2023/8/22 17:17
 */
@DubboService
@Service
@Slf4j
public class RemoteRuleEngineServiceImpl implements RemoteRuleEngineService {

    private final static String FLEET_STATE_TIMESTAMP = "fleet_state_timestamp:";

    @Autowired
    private RuleEngineService ruleEngineService;

    /**
     * 刷新仓库状态
     * @param req       入参
     * @param type      1硬件网关 2软网关
     * @param gatewayId 网关id
     */
    @Override
    public void refreshWarehouseStatus(GatewayScanReportingDto req, Integer type, String gatewayId) {
        ruleEngineService.refreshWarehouseStatus(req, type, gatewayId);
    }

    /**
     * 数据上报完成
     * @param req       入参
     * @param gatewayId 网关id
     * @param deviceId  设备id
     */
    @Override
    public void dataReportComplete(DataReportCompleteDto req, String gatewayId, String deviceId) {
        ruleEngineService.dataReportComplete(req, gatewayId, deviceId);
    }

    /**
     * 在线状态上报
     * @param type 类型
     * @param req  入参
     */
    @Override
    public void onlineStatusReport(OnlineStatusTypeEnum type, IotUpdateOnlineStatusDto req) {
        try {
            String key = FLEET_STATE_TIMESTAMP + req.getClientId();
            String lastTimestamp = RedisUtils.getCacheObject(key);
            if (lastTimestamp == null || lastTimestamp.compareTo(req.getTimestamp()) < 0) {
                softGatewayOnlineUpdate(type, req);
                RedisUtils.setCacheObject(key, req.getTimestamp(), Duration.ofMinutes(1));
            } else {
                log.warn("receiveOnlineStatus order unusual clientId:{}", req.getClientId());
            }
        } catch (Exception e) {
            log.error("onlineStatusReport Interrupted:{}", e.getMessage());
        }
    }

    /**
     * 软网关在线状态更新处理
     * @param type
     * @param req
     */
    private void softGatewayOnlineUpdate(OnlineStatusTypeEnum type, IotUpdateOnlineStatusDto req) {
        if (Objects.equals(type.getType(), OnlineStatusTypeEnum.GATEWAY.getType())) {
            ruleEngineService.gatewayOnlineStatusReport(req.getClientId(), req.getStatus());
        }
    }

    /**
     * 故障数据保存
     * @param deviceId 设备id
     * @param reported 报文
     */
    @Override
    public void saveFaultData(String deviceId, Object reported) {
        ruleEngineService.saveFaultData(deviceId, reported);
    }
}
