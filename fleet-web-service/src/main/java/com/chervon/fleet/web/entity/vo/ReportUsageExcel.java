package com.chervon.fleet.web.entity.vo;

import cn.hutool.core.annotation.Alias;
import com.chervon.common.core.utils.CsvUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 报告使用情况Excel
 * <AUTHOR>
 * @date 2023/7/25 11:43
 */
@Data
@Accessors(chain = true)
public class ReportUsageExcel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 名称
     */
    @Alias("Name")
    private String name;

    @Alias("Serial Number")
    private String sn;

    @Alias("Category")
    private String category;

    @Alias("Model#")
    private String model;

    @Alias("Tag")
    private List<String> tags;

    @Alias("Daily average usage time")
    private Integer dailyAverageUsageTime;

    @Alias("Usage time-selected range")
    private Integer usageTimeSelectedRange;

    @Alias("Total usage time")
    private Integer totalUsageTime;

    @Alias("Data start time")
    private String dataStartTime;

    @Alias("Data end time")
    private String dataEndTime;

    public String getName() {
        return CsvUtil.format(this.name);
    }

    public String getSn() {
        return CsvUtil.format(this.sn);
    }

    public String getCategory() {
        return CsvUtil.format(this.category);
    }

    public String getModel() {
        return CsvUtil.format(this.model);
    }

    public String getTags() {
        return CsvUtil.format(this.tags);
    }

    public String getDailyAverageUsageTime() {
        return CsvUtil.format(this.dailyAverageUsageTime);
    }

    public String getUsageTimeSelectedRange() {
        return CsvUtil.format(this.usageTimeSelectedRange);
    }

    public String getTotalUsageTime() {
        return CsvUtil.format(this.totalUsageTime);
    }

    public String getDataStartTime() {
        return CsvUtil.format(this.dataStartTime);
    }

    public String getDataEndTime() {
        return CsvUtil.format(this.dataEndTime);
    }
}
