package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/21 11:10
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "维保日志操作对象")
public class MaintenanceLogDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("维保日志id，新增不传，编辑必穿")
    private Long maintenanceLogId;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("服务类型：1 maintenance 2 inspection 3 repair")
    private Integer serviceType;

    @ApiModelProperty("服务时间")
    private Long serviceTime;

    @ApiModelProperty("运行时长")
    private Integer runtime;

    @ApiModelProperty("工作时长")
    private Integer laborTime;

    @ApiModelProperty("费用")
    private BigDecimal expenses;

    @ApiModelProperty("维护人")
    private String performedBy;

    @ApiModelProperty("维护内容")
    private String contents;
}
