package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备标签绑定关系表(t_device_tag)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-25 10:37:56
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_device_tag")
public class DeviceTag extends Model<DeviceTag> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 分组id
     */
    private Long groupId;
    /**
     * 标签id
     */
    private Long tagId;
    /**
     * 公司id
     */
    private Long companyId;

}