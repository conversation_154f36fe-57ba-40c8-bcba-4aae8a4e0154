package com.chervon.fleet.web.entity.vo.dashboard;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import com.chervon.fleet.web.api.entity.enums.MaintenanceStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备分类数量看板Vo
 * <AUTHOR>
 * @since 2023-07-27 16:50
 **/
@Data
@Accessors(chain = true)
@ApiModel("设备分类数量看板Vo")
public class CategoryCountDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分类编号
     */
    @ApiModelProperty("分类编号")
    @Translate(adapter = "fleetCategory", targetField = {"categoryName"})
    private String categoryCode;
    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String categoryName;
    /**
     * 当前分类数量
     */
    @ApiModelProperty("当前分类数量")
    private Integer categoryCount;
}
