package com.chervon.fleet.web.entity.vo.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 充电电量统计看板Vo
 * <AUTHOR>
 * @since 2023-07-31 17:48
 **/
@Data
@Accessors(chain = true)
@ApiModel("充电电量统计看板Vo")
public class EnergyUsageDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 无参构造函数
     */
    public EnergyUsageDashboardVo() {
        this.energyCharged = 0;
    }

    /**
     * 日期
     */
    @ApiModelProperty("日期,yyyy-MM-dd字符串")
    private String date;
    @ApiModelProperty("每日充电总量(单位：kwh)")
    private Integer energyCharged;
}
