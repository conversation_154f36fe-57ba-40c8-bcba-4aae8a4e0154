package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.entity.dto.ReportInventoryDto;
import com.chervon.fleet.web.entity.dto.ReportMaintenancePageDto;
import com.chervon.fleet.web.entity.dto.ReportUsagePageDto;
import com.chervon.fleet.web.entity.vo.ReportInventoryVo;
import com.chervon.fleet.web.entity.vo.ReportMaintenanceVo;
import com.chervon.fleet.web.entity.vo.ReportUsageVo;
import com.chervon.fleet.web.entity.vo.WebFilterConditionVo;
import com.chervon.fleet.web.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 库存、使用量、维保相关报表接口
 *
 * <AUTHOR>
 * @date 2023/6/27
 */
@Slf4j
@RestController
@RequestMapping("/report")
@AllArgsConstructor
@Api(tags = "报表接口（库存、使用量、维保相关）")
public class ReportController {

    private final ReportService reportService;

    @ApiOperation(value = "库存-分页")
    @PostMapping(value = "inventory/page")
    public PageResult<ReportInventoryVo> inventoryPage(@RequestBody @Valid PageRequest req) {
        return reportService.inventoryPage(req);
    }

    @ApiOperation(value = "库存-导出")
    @PostMapping("inventory/export")
    public void inventoryExport(@RequestBody ReportInventoryDto req, HttpServletResponse response) throws IOException {
        reportService.inventoryExport(req, response);
    }

    @ApiOperation(value = "设备使用-获取filter数据")
    @GetMapping(value = "usage/filterCondition")
    public WebFilterConditionVo usageFilterCondition() {
        return reportService.usageFilterCondition();
    }

    @ApiOperation(value = "equipmentUsage设备使用-分页")
    @PostMapping(value = "usage/page")
    public PageResult<ReportUsageVo> usagePage(@RequestBody ReportUsagePageDto req) {
        return reportService.usagePage(req);
    }

    @ApiOperation(value = "设备使用-导出")
    @PostMapping("usage/export")
    public void usageExport(@RequestBody ReportUsagePageDto req, HttpServletResponse response) throws IOException {
        reportService.usageExport(req, response);
    }

    @ApiOperation(value = "维保-获取filter数据")
    @GetMapping(value = "maintenance/filterCondition")
    public WebFilterConditionVo maintenanceFilterCondition() {
        return reportService.maintenanceFilterCondition();
    }

    @ApiOperation(value = "维保-分页")
    @PostMapping(value = "maintenance/page")
    public PageResult<ReportMaintenanceVo> maintenancePage(@RequestBody ReportMaintenancePageDto req) {
        return reportService.maintenancePage(req);
    }

    @ApiOperation(value = "维保-导出")
    @PostMapping("maintenance/export")
    public void maintenanceExport(@RequestBody ReportMaintenancePageDto req, HttpServletResponse response) throws IOException {
        reportService.maintenanceExport(req, response);
    }

}
