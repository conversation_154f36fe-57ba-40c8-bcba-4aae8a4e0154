package com.chervon.fleet.web.service;

import com.chervon.fleet.web.api.entity.dto.DataReportCompleteDto;
import com.chervon.fleet.web.api.entity.dto.DeviceBindChangeDto;
import com.chervon.fleet.web.api.entity.dto.GatewayChangeDto;
import com.chervon.fleet.web.api.entity.dto.GatewayScanReportingDto;
import com.chervon.fleet.web.api.entity.query.DeviceShadowQuery;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.DeviceShadowVo;
import com.chervon.fleet.web.api.entity.vo.DeviceStatusVo;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.technology.api.toruleengine.IotUpdateOnlineStatusDto;
import java.io.IOException;
import java.util.List;

/**
 * 规则引擎回调服务逻辑
 *
 * <AUTHOR> 2023/7/12
 */
public interface RuleEngineService {
    /**
     * 接收规则引擎转发的网关扫描到的设备列表的状态
     *
     * @param req       入参
     * @param type      1硬件网关 2软网关
     * @param gatewayId 网关id
     */
    void refreshWarehouseStatus(GatewayScanReportingDto req, Integer type, String gatewayId);

    /**
     * 接收规则引擎转发的设备数据上报完成状态
     *
     * @param req       入参
     * @param gatewayId 网关id
     * @param deviceId  设备id
     */
    void dataReportComplete(DataReportCompleteDto req, String gatewayId, String deviceId);

    /**
     * 接收规则引擎转发的网关连接状态更新
     *
     * @param clientId clientId
     * @param status   状态
     */
    void gatewayOnlineStatusReport(String clientId, String status);

    /**
     * * 根据设备id或网关id或租户Id读取设备的库存状态及数据上报完成状态
     *
     * @param query 设备id、网关id、租户Id
     * @return 设备的库存状态及数据上报完成状态
     */
    List<DeviceStatusVo> getWarehouseStatusByCache(InventoryStatusQuery query);

    List<DeviceStatusVo> getWarehouseStatusOnly(InventoryStatusQuery query);
    /**
     * 查询设备上报完成状态
     * @param query
     * @return
     */
    List<DeviceStatusVo> getReportCompleteStatus(InventoryStatusQuery query);

    /**
     * * 根据缓存获取设备影子和故障信息
     *
     * @return 设备影子信息
     */
    List<DeviceShadowVo> getDeviceShadowVoByCache(DeviceShadowQuery query);

    /**
     * 发布mqtt消息通知android端刷新设备数据上报完成云朵状态标记
     *
     * @param request 设备数据上报完成请求对象
     * @throws IOException
     */
    void publishDataReportCompleteTime(DataReportCompleteDto request, String deviceId, String gatewayId) throws IOException;

    /**
     * 发布mqtt消息通知android端设备有绑定和解绑的变化
     *
     * @param request 设备绑定解绑请求对象
     */
    void publishMsgDeviceBindAndUnBind(DeviceBindChangeDto request);

    /**
     * 发布mqtt消息通知android端设备在线离线状态的变化
     *
     * @param request 设备在线离线状态请求参数
     * @throws IOException
     */
    void publishMsgDeviceOnlineStatusChange(IotUpdateOnlineStatusDto request) throws IOException;

    /**
     * 发布设备在线状态
     *
     * @param deviceId     设备id
     * @param onlineStatus 在线状态 0 离线 1 在线
     */
    void publishMsgDeviceOnlineStatusChange(String deviceId, Integer onlineStatus) throws IOException;

    /**
     * 添加移除网关的数据上报处理：
     * 添加：初始化设备库存状态缓存map列表，置为初始状态；发布mqtt通知前端刷新库存列表
     * 移除：移除租户网关关系缓存map
     *
     * @param request
     */
    void addRemoveGatewayDataProcess(GatewayChangeDto request);

    /**
     * 设备绑定和解绑的数据上报处理：
     * 绑定：初始化设备库存状态缓存map列表，置为初始状态；发布mqtt通知前端刷新库存列表
     * 解绑：移除设备库存状态缓存map设备信息和网关下关联的设备关系；发布mqtt通知前端刷新库存列表
     */
    void deviceBindOrUnBindProcess(DeviceBindChangeDto request);

    /**
     * * 保存设备上报的故障信息
     *
     * @param deviceId 设备id
     * @param reported 上报日志
     */
    void saveFaultData(String deviceId, Object reported);

    /**
     * 修改设备基础信息
     *
     * @param companyDevice 租户设备信息
     */
    void updateDeviceInfo(CompanyDevice companyDevice);

    /**
     * 修改网关相关信息
     *
     * @param gateway 网关信息
     */
    void updateGatewayInfo(AppGateway gateway);
}
