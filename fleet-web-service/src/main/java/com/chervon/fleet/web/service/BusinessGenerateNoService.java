package com.chervon.fleet.web.service;

import com.chervon.idgenerator.annotation.GenerateCode;
import com.chervon.idgenerator.generator.IdGenerator;
import com.chervon.idgenerator.util.IdUtils;
import org.springframework.stereotype.Component;

/**
 * 业务单号生成service
 */
@Component
public class BusinessGenerateNoService {

    /**
     * 发货单号
     * @return
     */
    @GenerateCode(key = "TransportNo", template = "key:{?key}$text:{BY}$date$seq:%06d", isReset = true)
    public String generateTransportNo() {
        return null;
    }

    /**
     * 订单单号
     * @return
     */
    //@GenerateCode(key = "OrderNo", template = "key:{?key}$text:{D}$date$seq:%06d", isReset = true)
    public String generateOrderNo() {
        return null;
    }

    /**
     * dealer 短名称
     * @return
     */
    //@GenerateCode(key = "DealerShortName", template = "key:{?key}$text:$seq:%05d")
    public String generateDealerShortName() {
        return null;
    }

    /**
     * store 短名称
     * @return
     */
    //@GenerateCode(key = "StoreShortName", template = "key:{?key}$text:$seq:%05d")
    public String generateStoreShortName() {
        return null;
    }

}
