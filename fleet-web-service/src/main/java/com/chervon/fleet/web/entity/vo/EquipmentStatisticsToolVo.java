package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备统计工具vo
 * <AUTHOR>
 * @date 2023/7/24 16:46
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsToolVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty("总使用时长,1016单位秒")
    private BigDecimal totalUsageTime;

    @ApiModelProperty("总使用次数，1018")
    private Integer totalNumberOfUses;

}
