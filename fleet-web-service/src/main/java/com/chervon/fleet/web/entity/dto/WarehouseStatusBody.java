package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 网关扫描到设备在线状态后发布mqtt消息体负载实体
 * 给 android端订阅刷新设备库存状态 *
 * <AUTHOR> 2023/7/19
 */
@Accessors(chain = true)
@Data
public class WarehouseStatusBody implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("网关id")
    private String gatewayId;

    @ApiModelProperty("网关类型：1硬件网关 2软网关")
    private Integer type;

    @ApiModelProperty(value = "网关类型：1移动网关，2固定网关")
    private Integer gatewayType;

    @ApiModelProperty("网关位置：纬度,经度 逗号分隔")
    private String coordinate;
    /**
     * 设备库存状态：0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location
     */
    private List<DeviceStatusSubBody> listDevice;
}
