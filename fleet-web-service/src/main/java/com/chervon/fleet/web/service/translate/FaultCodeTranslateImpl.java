package com.chervon.fleet.web.service.translate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.core.TranslateService;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.ProductFault;
import com.chervon.fleet.web.service.ProductFaultService;
import com.chervon.fleet.web.utils.DataUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;

/**
 * 故障码多语言翻译
 * <AUTHOR> 2022/12/6
 */
@Component
public class FaultCodeTranslateImpl implements TranslateService<BiDeviceErrorList,ProductFault,Object> {
    @Autowired
    private ProductFaultService productFaultService;
    public String getInstanceCode(){
        return "faultCode";
    }

    @Override
    public ProductFault getSourceValue(Field field, BiDeviceErrorList dataEntity) throws IllegalAccessException {
        String lang= UserContext.getClientInfo().getLanguage();
        if(lang==null){
            lang= I18nController.DEFAULT_LANGUAGE;
        }
        if (Objects.isNull(dataEntity)) {
            return null;
        }
        String faultCode = dataEntity.getErrorCode();
        String deviceId = dataEntity.getDeviceId();
        String productSnCode= DataUtils.getSn(deviceId);
        List<ProductFault> productFaultList = getProductFaults(faultCode, productSnCode, lang);
        if(CollectionUtils.isEmpty(productFaultList)){
            if(!lang.toLowerCase().equals(I18nController.DEFAULT_LANGUAGE)){
                lang=I18nController.DEFAULT_LANGUAGE;
                productFaultList = getProductFaults(faultCode, productSnCode, lang);
            }
            if(!CollectionUtils.isEmpty(productFaultList)){
                return productFaultList.get(0);
            }
            return null;
        }
        return productFaultList.get(0);
    }

    private List<ProductFault> getProductFaults(String faultCode, String productSnCode, String lang) {
        final LambdaQueryWrapper<ProductFault> queryWrapper = new LambdaQueryWrapper<ProductFault>()
                .eq(ProductFault::getFaultCode, faultCode)
                .eq(ProductFault::getProductSnCode, productSnCode)
                .eq(ProductFault::getLanguage, lang);
        List<ProductFault> productFaultList = productFaultService.list(queryWrapper);
        return productFaultList;
    }

    @Override
    public void setTargetValue(BiDeviceErrorList dataDto, ProductFault result, List<String> targetField) throws IllegalAccessException {
        if(Objects.isNull(result)){
            return;
        }
        Class<?> aClass = dataDto.getClass();
        List<Field> fields = TranslateUtils.getBindFields(aClass,targetField);
        for (Field field : fields) {
            field.setAccessible(true);
            if(field.getName().equals("suggestionContent")){
                field.set(dataDto, result.getSuggestionContent());
            }else{
                field.set(dataDto, result.getFaultTitle());
            }
        }
    }
}
