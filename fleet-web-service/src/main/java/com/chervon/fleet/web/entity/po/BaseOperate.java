package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 操作基本信息基类 *
 * <AUTHOR> 2022/10/18
 */
@Data
@Accessors(chain = true)
public class BaseOperate<T extends Model<?>> extends Model<T>  {

    private static final long serialVersionUID = 1L;
    /**
     * 创建人姓名
     */
    private String createUser;
    /**
     * 更新人姓名
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;
}
