package com.chervon.fleet.web.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 设备维保状态数量统计表(看板：Maintenance & Service)(t_bi_maintenance)实体类
 *
 * <AUTHOR>
 * @since 2023-07-27 13:58:00
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_maintenance")
public class BiMaintenance extends Model<BiMaintenance> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
	private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 维保类型名称：Service due、Normal、Off
     */
    private String serviceType;
    /**
     * 维保设备类型数量
     */
    private Integer serviceCount;
    /**
     * 更新时间
     */
    private Date modifyTime;

}