package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.MaintenanceLogServiceTypeEnum;
import com.chervon.fleet.web.api.entity.error.MaintenanceErrorCodeEnum;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.MaintenanceLogDto;
import com.chervon.fleet.web.entity.dto.MaintenanceLogSearchDto;
import com.chervon.fleet.web.entity.po.MaintenanceLog;
import com.chervon.fleet.web.entity.po.MaintenancePlan;
import com.chervon.fleet.web.entity.po.ShadowStatus;
import com.chervon.fleet.web.entity.vo.MaintenanceLogVo;
import com.chervon.fleet.web.mapper.MaintenanceLogMapper;
import com.chervon.fleet.web.service.MaintenanceLogService;
import com.chervon.fleet.web.service.MaintenancePlanService;
import com.chervon.fleet.web.service.ShadowStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * 维保操作记录表服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-15 19:15:52
 */
@Slf4j
@Service
public class MaintenanceLogServiceImpl extends ServiceImpl<MaintenanceLogMapper, MaintenanceLog> implements MaintenanceLogService {
    @Autowired
    private MaintenancePlanService maintenancePlanService;
    @Autowired
    private ShadowStatusService shadowStatusService;

    /**
     * 新增维保操作记录表
     * @param req 查询条件
     * @return PageResult<MaintenanceLogVo>
     */
    @Override
    public PageResult<MaintenanceLogVo> maintenanceLogPage(MaintenanceLogSearchDto req) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "maintenanceLogSearchDto");
        Assert.hasText(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        PageResult<MaintenanceLogVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        Long companyId = UserContext.getCompanyId();
        MaintenancePlan maintenancePlan = maintenancePlanService.getOne(new LambdaQueryWrapper<MaintenancePlan>().eq(MaintenancePlan::getDeviceId, req.getDeviceId()).eq(MaintenancePlan::getCompanyId, companyId));
        if (maintenancePlan == null || maintenancePlan.getId() == null) {
            return res;
        }
        Page<MaintenanceLog> page = page(new Page<>(req.getPageNum(), req.getPageSize()), new LambdaQueryWrapper<MaintenanceLog>()
                .eq(MaintenanceLog::getDeviceId, req.getDeviceId())
                .eq(MaintenanceLog::getPlanId, maintenancePlan.getId())
                .eq(MaintenanceLog::getCompanyId, companyId)
                .eq(req.getServiceType() != null, MaintenanceLog::getServiceType, req.getServiceType())
                .le(req.getServiceTimeEnd() != null, MaintenanceLog::getServiceTime, req.getServiceTimeEnd())
                .ge(req.getServiceTimeStart() != null, MaintenanceLog::getServiceTime, req.getServiceTimeStart())
                .orderByDesc(MaintenanceLog::getServiceTime).orderByDesc(MaintenanceLog::getCreateTime));
        res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            MaintenanceLogVo vo = new MaintenanceLogVo();
            BeanUtils.copyProperties(e, vo);
            vo.setMaintenanceLogId(e.getId());
            return vo;
        }).collect(Collectors.toList()));
        return res;
    }

    /**
     * 获取设备的运行时间
     * @param deviceId 设备id
     * @return 运行时间
     */
    @Override
    public Integer maintenanceLogRuntime(String deviceId) {
        Long companyId=UserContext.getCompanyId();
        // 设置刺客的使用时间
        ShadowStatus shadowStatus = shadowStatusService.getByDeviceId(deviceId,companyId);
        if (shadowStatus != null && shadowStatus.getTotalUsageTime() != null) {
            return shadowStatus.getTotalUsageTime() / NumberConst.ONE_HOUR_SECOND;
        }
        return 0;
    }

    /**
     * 添加维修日志
     * @param req 操作对象
     */
    @Override
    public void maintenanceLogAdd(MaintenanceLogDto req) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "maintenanceLogDto");
        Assert.hasText(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        Assert.notNull(req.getServiceType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.SERVICE_TYPE);
        MaintenanceLogServiceTypeEnum serviceTypeEnum = MaintenanceLogServiceTypeEnum.getEnum(req.getServiceType());
        Assert.notNull(serviceTypeEnum, ErrorCode.PARAMETER_FORMAT_ERROR, StringConst.SERVICE_TYPE);
        Assert.notNull(req.getServiceTime(), ErrorCode.PARAMETER_NOT_PROVIDED, "serviceTime");
        Assert.notNull(req.getRuntime(), ErrorCode.PARAMETER_NOT_PROVIDED, "runtime");
        Assert.notNull(req.getLaborTime(), ErrorCode.PARAMETER_NOT_PROVIDED, "laborTime");
        Assert.notNull(req.getExpenses(), ErrorCode.PARAMETER_NOT_PROVIDED, "expenses");
        Assert.hasText(req.getPerformedBy(), ErrorCode.PARAMETER_NOT_PROVIDED, "performedBy");
        Long companyId = UserContext.getCompanyId();
        MaintenancePlan maintenancePlan = maintenancePlanService.getOne(new LambdaQueryWrapper<MaintenancePlan>().eq(MaintenancePlan::getDeviceId, req.getDeviceId()).eq(MaintenancePlan::getCompanyId, companyId));
        if (maintenancePlan == null || maintenancePlan.getId() == null) {
            throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_NOT_FOUND);
        }
        ShadowStatus shadowStatus = shadowStatusService.getByDeviceId(req.getDeviceId(),companyId);
        int hour;
        if (shadowStatus != null && shadowStatus.getTotalUsageTime() != null) {
            hour = shadowStatus.getTotalUsageTime() / NumberConst.ONE_HOUR_SECOND;
        } else {
            hour = 0;
        }
        if (req.getRuntime() != hour) {
            throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_LOG_RUNTIME_ERROR);
        }
        MaintenanceLog log = new MaintenanceLog();
        BeanUtils.copyProperties(req, log);
        log.setPlanId(maintenancePlan.getId());
        log.setCompanyId(companyId);
        save(log);
    }

    /**
     * 维修日志编辑
     * @param req 操作对象
     */
    @Override
    public void maintenanceLogEdit(MaintenanceLogDto req) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "maintenanceLogDto");
        Assert.notNull(req.getMaintenanceLogId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.MAINTENANCE_LOG_ID);
        Assert.notNull(req.getServiceType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.SERVICE_TYPE);
        MaintenanceLogServiceTypeEnum serviceTypeEnum = MaintenanceLogServiceTypeEnum.getEnum(req.getServiceType());
        Assert.notNull(serviceTypeEnum, ErrorCode.PARAMETER_FORMAT_ERROR, StringConst.SERVICE_TYPE);
        Assert.notNull(req.getServiceTime(), ErrorCode.PARAMETER_NOT_PROVIDED, "serviceTime");
        Assert.notNull(req.getLaborTime(), ErrorCode.PARAMETER_NOT_PROVIDED, "laborTime");
        Assert.notNull(req.getExpenses(), ErrorCode.PARAMETER_NOT_PROVIDED, "expenses");
        Assert.hasText(req.getPerformedBy(), ErrorCode.PARAMETER_NOT_PROVIDED, "performedBy");
        MaintenanceLog log = getById(req.getMaintenanceLogId());
        if (log == null) {
            throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_LOG_NOT_FOUND);
        }
        MaintenanceLog newLog = new MaintenanceLog();
        BeanUtils.copyProperties(req, newLog);
        newLog.setId(req.getMaintenanceLogId());
        newLog.setDeviceId(null);
        newLog.setRuntime(null);
        updateById(newLog);
    }

    /**
     * 维修日志删除
     * @param maintenanceLogId 维保日志id
     */
    @Override
    public void maintenanceLogDelete(Long maintenanceLogId) {
        Assert.notNull(maintenanceLogId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.MAINTENANCE_LOG_ID);
        MaintenanceLog log = getById(maintenanceLogId);
        if (log == null) {
            throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_LOG_NOT_FOUND);
        }
        removeById(maintenanceLogId);
    }

    /**
     * 维修日志详情
     * @param maintenanceLogId 维保日志id
     * @return 维保日志详情
     */
    @Override
    public MaintenanceLogVo maintenanceLogDetail(Long maintenanceLogId) {
        Assert.notNull(maintenanceLogId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.MAINTENANCE_LOG_ID);
        MaintenanceLog log = getById(maintenanceLogId);
        if (log == null) {
            return null;
        }
        MaintenanceLogVo res = new MaintenanceLogVo();
        BeanUtils.copyProperties(log, res);
        res.setMaintenanceLogId(maintenanceLogId);
        return res;
    }
}