package com.chervon.fleet.web.controller;

import com.chervon.fleet.web.service.DealerEuService;
import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerEuVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "经销商管理（欧洲）")
@RestController
@Slf4j
@RequestMapping("dealer/eu")
public class DealerEuController {

    private final DealerEuService dealerEuService;

    public DealerEuController(DealerEuService dealerEuService) {
        this.dealerEuService = dealerEuService;
    }

    @ApiOperation(value = "搜索距离范围内的经销商")
    @PostMapping(value = "search")
    public List<AppDealerEuVo> search(@RequestBody AppDealerSearchDto req) {
        return dealerEuService.search(req == null ? new AppDealerSearchDto() : req);
    }

    @ApiOperation(value = "经销商详情")
    @GetMapping(value = "detail")
    public AppDealerEuVo detail(@RequestParam("dealerId") Long dealerId) {
        return dealerEuService.detail(dealerId);
    }

    @ApiOperation(value = "我收藏的经销商-总数")
    @PostMapping(value = "favorite/count")
    public Integer favoriteCount(@RequestBody AppDealerSearchDto req) {
        return dealerEuService.favoriteCount(req == null ? new AppDealerSearchDto() : req);
    }

    @ApiOperation(value = "我收藏的经销商列表")
    @PostMapping(value = "favorite")
    public List<AppDealerEuVo> favorite(@RequestBody AppDealerSearchDto req) {
        return dealerEuService.favorite(req == null ? new AppDealerSearchDto() : req);
    }

    @ApiOperation(value = "添加收藏")
    @GetMapping(value = "addFavorite")
    public void addFavorite(@RequestParam("dealerId") Long dealerId) {
        dealerEuService.addFavorite(dealerId);
    }

    @ApiOperation(value = "取消收藏")
    @GetMapping(value = "cancelFavorite")
    public void cancelFavorite(@RequestParam("dealerId") Long dealerId) {
        dealerEuService.cancelFavorite(dealerId);
    }

}
