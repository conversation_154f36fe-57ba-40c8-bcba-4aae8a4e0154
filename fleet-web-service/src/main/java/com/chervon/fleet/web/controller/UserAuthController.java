package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.R;
import com.chervon.fleet.user.api.entity.dto.LoginDto;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.service.RemoteAuthService;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * 用户登录、注销、鉴权相关接口
 *
 * <AUTHOR> 2023/6/27 123
 */
@RestController
@Slf4j
@RequestMapping("/web/auth")
@Api(tags = "用户鉴权")
public class UserAuthController {
    @DubboReference
    private RemoteAuthService remoteAuthService;

    @ApiOperation("登录")
    @PostMapping(value = "/login", produces = "application/json")
    @WebApiHeaders
    public R<LoginResultVo> login(@RequestBody LoginDto loginDto) {
        LoginResultVo loginInfo = remoteAuthService.login(loginDto);
        return R.ok(loginInfo);
    }

    @ApiOperation("获取公钥用于加密密码")
    @GetMapping(value = "/publicKey")
    public R<String> getPublicKey() {
        String publicKey = remoteAuthService.getPublicKey();
        return R.ok(publicKey);
    }

    @ApiOperation("根据token获取授权用户信息")
    @GetMapping(value = "/login/info")
    @WebApiHeaders
    public R<LoginResultVo> checkLoginInfo(@RequestHeader("token") String token) {
        final LoginResultVo loginResultVo = remoteAuthService.getUserInfoByDb(token);
        return R.ok(loginResultVo);
    }

    @ApiOperation("退出登录")
    @WebApiHeaders
    @GetMapping(value = "/logout", produces = "application/json")
    public R<Boolean> logout() {
        return R.ok(remoteAuthService.logout());
    }


    @ApiOperation("刷新用户信息")
    @PostMapping("/refresh")
    @WebApiHeaders
    public R<LoginResultVo> refresh(@RequestHeader("token") String token) {
        return R.ok(remoteAuthService.refresh(token));
    }
}
