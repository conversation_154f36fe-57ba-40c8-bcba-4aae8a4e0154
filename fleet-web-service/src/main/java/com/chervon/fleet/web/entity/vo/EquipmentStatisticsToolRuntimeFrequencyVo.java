package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description:工具运行时间统计
 * <AUTHOR>
 * @date 2023/7/24 18:51
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsToolRuntimeFrequencyVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public EquipmentStatisticsToolRuntimeFrequencyVo() {
        this.runtime = 0;
        this.frequency = 0;
    }

    @ApiModelProperty("日期时间戳")
    private String date;

    @ApiModelProperty("运行时间")
    private Integer runtime;

    @ApiModelProperty("运行次数")
    private Integer frequency;

}
