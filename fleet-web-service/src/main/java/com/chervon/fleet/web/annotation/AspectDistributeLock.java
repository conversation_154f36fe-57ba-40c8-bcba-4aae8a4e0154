package com.chervon.fleet.web.annotation;

import com.alibaba.fastjson.JSON;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.redis.utils.RedisUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁实现
 */
@Aspect
@Component
public class AspectDistributeLock {

    private static final ParameterNameDiscoverer DISCOVERER =new LocalVariableTableParameterNameDiscoverer();

    @Pointcut("@annotation(com.chervon.fleet.web.annotation.EnableDistributeLock)")
    public void distributedLockPointCut()
    {
        // Do nothing because of Pointcut
    }

    /**
     * 分布式锁切莫拦截器
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("distributedLockPointCut()")
    public Object doPublicMethodAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        StandardEvaluationContext standardEvaluationContext = new StandardEvaluationContext(args);
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = joinPoint.getTarget().getClass().getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
        final EnableDistributeLock distributeLock = method.getAnnotation(EnableDistributeLock.class);
        String[] parameterNames = DISCOVERER.getParameterNames(method);
        //避免索引越界
        if (!Objects.isNull(parameterNames) && parameterNames.length <= args.length) {
            for (int i = 0; i < args.length; i++) {
                standardEvaluationContext.setVariable(parameterNames[i], args[i]);
            }
        }
        String key = generateLockKey(distributeLock, standardEvaluationContext);
        int timeout = distributeLock.timeout();
        RedissonClient client = RedisUtils.getClient();
        RLock lockInstance = client.getLock(key);
        if (!lockInstance.isLocked()) {
            try {
                lockInstance.lock(timeout, TimeUnit.SECONDS);
                Object proceed = joinPoint.proceed();
                return proceed;
            } finally {
                lockInstance.unlock();
            }
        } else {
            throw new ServiceException(ErrorCode.BUSINESS_OPERATE_WAITING);
        }
    }

    /**
     * 获取分布式锁的key
     * @param distributeLock
     * @param standardEvaluationContext
     * @return
     */
    private static String generateLockKey(EnableDistributeLock distributeLock, StandardEvaluationContext standardEvaluationContext) {
        final Object objValue = standardEvaluationContext.lookupVariable(distributeLock.key());
        String expValue = "";
        if(objValue!=null){
            expValue= JSON.toJSONString(objValue);
        }
        StringJoiner stringJoiner=new StringJoiner("_");
        stringJoiner.add(distributeLock.keyPrefix()).add(expValue);
        return stringJoiner.toString();
    }

}
