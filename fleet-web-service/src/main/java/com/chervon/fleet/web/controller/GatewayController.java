package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.api.entity.vo.GatewayVo;
import com.chervon.fleet.web.entity.dto.GatewayPageDto;
import com.chervon.fleet.web.service.AppGatewayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 网关相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@Slf4j
@RestController
@RequestMapping("/gateway")
@Api(tags = "网关列表和移除")
public class GatewayController {

    @Autowired
    private AppGatewayService appGatewayService;

    @ApiOperation("获取网关列表，参数type：1 charger gateway 2 app gateway-fix 3 app gateway-mobile")
    @PostMapping(value = "/list")
    public PageResult<GatewayVo> getGatewayList(@RequestBody GatewayPageDto req) {
        return appGatewayService.listByType(req);
    }

    @ApiOperation("移除网关")
    @GetMapping(value = "/remove")
    public void remove(@RequestParam("gatewayId") String gatewayId) {
        appGatewayService.removeGateway(gatewayId);
    }
}
