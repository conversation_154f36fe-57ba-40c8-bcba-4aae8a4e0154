package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.dashboard.ChargingSystemDetailDashboardQuery;
import com.chervon.fleet.web.entity.po.BiPowerHubDetail;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingSystemDetailDashboardVo;

import java.util.List;

/**
 * power-hub充电详情统计表(看板详情页：Power Overview Detail图)服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
public interface BiPowerHubDetailService extends IService<BiPowerHubDetail> {

    /**
     * 充电系统详情
     *
     * @param query 查询条件
     * @return 查询结果
     */
    List<ChargingSystemDetailDashboardVo> powerHubDetailRight(ChargingSystemDetailDashboardQuery query);

}
