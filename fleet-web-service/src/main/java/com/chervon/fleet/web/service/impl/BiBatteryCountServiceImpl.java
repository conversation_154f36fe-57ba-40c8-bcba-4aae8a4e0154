package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.entity.po.BiBatteryCount;
import com.chervon.fleet.web.entity.vo.dashboard.BatteryCountDashboardVo;
import com.chervon.fleet.web.mapper.BiBatteryCountMapper;
import com.chervon.fleet.web.service.BiBatteryCountService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 电池数量统计（看板：total batteries饼状图）服务接口实现
 *
 * <AUTHOR>
 * @since 2023-07-27 13:57:59
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiBatteryCountServiceImpl extends ServiceImpl<BiBatteryCountMapper, BiBatteryCount>
    implements BiBatteryCountService {
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Override
    public List<BiBatteryCount> listByCompanyId(Long companyId) {
        return this.list(new LambdaQueryWrapper<BiBatteryCount>()
                .eq(BiBatteryCount::getCompanyId, companyId)
                .gt(BiBatteryCount::getBatteryCount,0)
            .orderByAsc(BiBatteryCount::getBatteryType));
    }

    /**
     * web端 Power-Dashboard第二栏右侧充电器上挂载的电池包数量统计
     * @param companyId 公司ID
     * @return
     */
    @Override
    public List<BatteryCountDashboardVo> batteryCount(Long companyId) {
        List<BiBatteryCount> biBatteryCountList = listByCompanyId(companyId);
        if (CollectionUtils.isEmpty(biBatteryCountList)) {
            return new ArrayList<>();
        }
        if(companyDeviceService.getPowerHubCount(companyId,true)==0L){
            return new ArrayList<>();
        }
        List<BatteryCountDashboardVo> voList=new ArrayList<>();
        for(BiBatteryCount biBatteryCount:biBatteryCountList){
            voList.add(ConvertUtil.convert(biBatteryCount, BatteryCountDashboardVo.class));
        }
        return voList;
    }
}