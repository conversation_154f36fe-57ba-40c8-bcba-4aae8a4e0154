package com.chervon.fleet.web.annotation;

import com.chervon.fleet.web.entity.consts.NumberConst;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface EnableDistributeLock {
    /**
     * 锁前缀
     */
    String keyPrefix() default "DISTRIBUTE_LOCK_";
    /**
     * 分布式锁的key
     */
    String key();

    /**
     * 默认超时时间：秒
     * @return
     */
    int timeout() default 60;
}
