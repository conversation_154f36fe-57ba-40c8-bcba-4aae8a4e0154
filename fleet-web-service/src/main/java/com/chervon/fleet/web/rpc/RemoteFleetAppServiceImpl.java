package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.vo.DashboardErrorVo;
import com.chervon.fleet.web.api.entity.vo.DeviceErrorVo;
import com.chervon.fleet.web.api.service.RemoteFleetAppService;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.service.BiDeviceErrorListService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.technology.api.vo.FleetDeviceProductBasicInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/6 11:49
 */
@Service
@DubboService
@Slf4j
public class RemoteFleetAppServiceImpl implements RemoteFleetAppService {

    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;

    @Override
    public List<String> listDeviceIdLikeCompanyId(String companyId) {
        List<CompanyDevice> list = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .like(StringUtils.hasText(companyId), CompanyDevice::getCompanyId, companyId)
                .select(CompanyDevice::getDeviceId));
        return list.stream().filter(Objects::nonNull).map(CompanyDevice::getDeviceId).filter(StringUtils::hasText)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public Map<String, Long> mapDeviceIdCompanyId(List<String> deviceIds) {
        List<CompanyDevice> list = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>().in(CompanyDevice::getDeviceId, deviceIds));
        list.removeIf(e -> e.getCompanyId() == null);
        return list.stream().collect(Collectors.toMap(CompanyDevice::getDeviceId, CompanyDevice::getCompanyId, (k1, k2) -> k2));
    }

    @Override
    public DeviceErrorVo getLastDeviceError(String deviceId,Integer hourExpire) {
        Long companyId = UserContext.getCompanyId();
        BiDeviceErrorList a = biDeviceErrorListService.getLastDeviceError(companyId, deviceId, hourExpire);
        if (Objects.isNull(a)) {
            return null;
        }
        return new DeviceErrorVo()
                .setDeviceId(a.getDeviceId())
                .setErrorCode(a.getErrorCode())
                .setFaultMessageTitle(a.getErrorMessage())
                .setSuggestionContent(a.getSuggestionContent());
    }
}
