package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet设备在线状态缓存
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
@Accessors(chain = true)
@ApiModel("fleet设备在线状态缓存")
public class DeviceOnlineStatusCache implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 网关在线状态: 1-online   2-offline
     */
    private Integer onlineStatus;
    /**
     * 最后上报时间戳
     */
    private Long reportTime;
}
