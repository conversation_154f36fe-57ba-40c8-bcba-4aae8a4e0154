package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 设备筛选对象
 * <AUTHOR>
 * @date 2023/7/12 14:08
 */
@Data
@ApiModel(description = "设备筛选对象")
public class EquipmentSearchVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 租户下总设备数量
     */
    @ApiModelProperty("租户下总设备数量")
    private Integer totalCount;

    @ApiModelProperty("符合筛选条件的设备数量，如果没有筛选，则为空")
    private Integer searchCount;

    @ApiModelProperty("设备列表")
    private List<EquipmentVo> equipments;

    @ApiModelProperty("是否筛选过，0 没有筛选 1 筛选过")
    private Integer isFilter;

    @ApiModelProperty("地图网关信息")
    private List<MapGatewayVo> mapGateways;

    @ApiModelProperty("中心点经度")
    private BigDecimal centerLongitude;

    @ApiModelProperty("中心点纬度")
    private BigDecimal centerLatitude;

}
