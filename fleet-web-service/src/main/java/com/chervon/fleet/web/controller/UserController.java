package com.chervon.fleet.web.controller;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.dto.*;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import com.chervon.fleet.user.api.entity.enums.EnableEnum;
import com.chervon.fleet.user.api.entity.enums.UserTypeEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.UserPageQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.entity.vo.UserRegisterActiveCheckVo;
import com.chervon.fleet.user.api.entity.vo.UserSyncVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.api.service.RemoteSendMailService;
import com.chervon.fleet.user.api.service.RemoteUserService;
import com.chervon.fleet.web.annotation.EnableDistributeLock;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import com.chervon.fleet.web.entity.consts.StringConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * App用户相关接口
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "用户注册及密码管理相关接口")
@RestController
@RequestMapping("/user")
public class UserController {

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteSendMailService remoteSendMailService;


    @ApiOperation("注册用户-1：注册管理员用户发送邮件")
    @PostMapping(value = "/registerSendMail")
    @WebApiHeaders
    public R<Void> registerSendMail(@RequestBody EmailDto emailDto) {
        Assert.hasText(emailDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        remoteUserService.registerSendMail(emailDto.getEmail());

        return R.ok();
    }

    @ApiOperation("注册用户-2：注册管理员用户点击超链接跳转验证身份")
    @PostMapping(value = "/registerCheck")
    @WebApiHeaders
    public R<Void> registerCheck(@RequestBody UserRegisterCheckDto userRegisterCheckDto) {
        //验证请求参数
        Assert.notNull(userRegisterCheckDto, ErrorCode.PARAMETER_NOT_PROVIDED, UserRegisterCheckDto.class.getSimpleName());
        Assert.hasText(userRegisterCheckDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(userRegisterCheckDto.getVerificationCode(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        //rpc调用验证验证码是否过期和正确
        remoteUserService.registerCheck(userRegisterCheckDto);
        return R.ok();
    }

    @ApiOperation("注册用户-3：注册创建管理员账户")
    @PostMapping(value = "/registerCreateUser")
    @WebApiHeaders
    public UserVo registerCreateUser(@RequestBody UserRegisterDto requestDto) {
        //验证请求参数
        validateRegisterParam(requestDto);
        //rpc调用验证验证码是否过期和正确
        return remoteUserService.register(requestDto);
    }

    private static void validateRegisterParam(UserRegisterDto requestDto) {
        //验证请求参数
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(requestDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(requestDto.getFirstName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.FIRST_NAME);
        Assert.hasText(requestDto.getLastName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LAST_NAME);
        Assert.hasText(requestDto.getPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.PASSWORD);
    }

    /**
     * 底层已增加分布式锁
     * @param requestDto
     * @return
     */
    @ApiOperation("邀请员工注册1：发送邀请邮件并创建用户")
    @PostMapping(value = "/inviteRegister")
    @EnableDistributeLock(key = "requestDto",timeout = 30)
    @WebApiHeaders
    public R<Boolean> inviteRegister(@RequestBody UserRegisterDto requestDto) {
        //验证请求参数
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(requestDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(requestDto.getFirstName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.FIRST_NAME);
        Assert.hasText(requestDto.getLastName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LAST_NAME);
        //rpc调用用户邀请注册接口
        final Boolean result = remoteUserService.inviteRegister(requestDto);
        return R.ok(result);
    }

    @ApiOperation("邀请员工注册1.5: 再次发送邀请邮件")
    @PostMapping(value = "/inviteRegisterAgain")
    @EnableDistributeLock(key = "requestDto",timeout = 30)
    @WebApiHeaders
    public R<Void> inviteRegisterAgain(@RequestBody UserRegisterAgainDto requestDto) {
        //验证请求参数
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.notNull(requestDto.getId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.ID);
        //rpc调用用户邀请注册接口
        remoteUserService.inviteRegisterAgain(requestDto.getId());
        return R.ok();
    }

    @ApiOperation("邀请员工注册2：点击链接进行激活验证码过期验证")
    @PostMapping(value = "/activityCheck")
    @WebApiHeaders
    public UserRegisterActiveCheckVo activityCheck(@RequestBody UserRegisterCheckDto userRegisterCheckDto) {
        //验证请求参数
        Assert.notNull(userRegisterCheckDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(userRegisterCheckDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(userRegisterCheckDto.getVerificationCode(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        Assert.hasText(userRegisterCheckDto.getFirstName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.FIRST_NAME);
        Assert.hasText(userRegisterCheckDto.getLastName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LAST_NAME);
        Assert.notNull(userRegisterCheckDto.getCompanyId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        //rpc调用验证验证码是否过期和正确
        return remoteUserService.activityCheck(userRegisterCheckDto);
    }

    @ApiOperation("邀请员工注册3：激活员工账户(邀请员工后)")
    @PostMapping(value = "/activityUser")
    @WebApiHeaders
    public void activityUser(@RequestBody UserActivityDto requestDto) {
        //验证请求参数
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.isId(requestDto.getUserId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.ID);
        //rpc调用用户激活账户接口
        remoteUserService.activityUser(requestDto);
    }

    @ApiOperation("分页")
    @PostMapping(value = "/page")
    @WebApiHeaders
    public PageResult<UserVo> page(@RequestBody UserPageQuery userPageQuery) {
        Assert.notNull(userPageQuery, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        final Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        return remoteUserService.page(userPageQuery, companyId);
    }

    @ApiOperation("编辑")
    @PostMapping("/edit")
    @WebApiHeaders
    public R<Boolean> edit(@RequestBody UserEditDto request) {
        Assert.notNull(request, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.isId(request.getId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.ID);
        // 鉴权
        final Long loginUserId = HeaderUtils.getUserId();
        final Integer userType = HeaderUtils.getUserType();
        if (!request.getId().equals(loginUserId) && userType.equals(CommonConstant.THREE)) {
            log.error("当前登录用户({})没有编辑权限", loginUserId);
            throw new ServiceException(UserErrorCodeEnum.ILLEGAL_GET_USER_PERMISSIONS);
        }
        final Boolean success = remoteUserService.edit(request);
        return R.ok(success);
    }

    @ApiOperation("详情")
    @GetMapping("/detail")
    @WebApiHeaders
    public R<UserVo> detail(@RequestParam Long id) {
        final UserVo userVo = remoteUserService.getDetail(new UserQuery().setId(id));
        return R.ok(userVo);
    }

    @ApiOperation("删除用户")
    @GetMapping("/delete")
    @WebApiHeaders
    public R<Boolean> delete(@RequestParam Long id) {
        Assert.isId(id, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.ID);
        // 鉴权
        Long loginUserId = UserContext.getClientInfo().getUserId();
        Integer loginUserType = UserContext.getClientInfo().getUserType();
        if (id.equals(loginUserId) || loginUserType.equals(UserTypeEnum.EMPLOYEE.getType())) {
            throw new ServiceException(UserErrorCodeEnum.ILLEGAL_GET_USER_PERMISSIONS);
        }
        //合规要求：逻辑删除用户（原来：freeUser）
        remoteUserService.delete(id);
        return R.ok(true);
    }

    @ApiOperation("逻辑删除用户帐号")
    @GetMapping("/cancellation")
    @WebApiHeaders
    public R<Boolean> accountCancellation() {
        remoteUserService.delete(UserContext.getClientInfo().getUserId());
        return R.ok(true);
    }

    /**
     * 用户请求鉴权接口
     * @return
     */
    @PostMapping("/loginInfo")
    @ApiOperation("获取登录用户信息")
    @WebApiHeaders
    public R<LoginResultVo> getLoginUser() {
        final Long userId = UserContext.getUserId();
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        return R.ok(remoteUserService.getLoginResultVoById(userId));
    }

    @PostMapping("/getCompanyUserList")
    @ApiOperation(value = "移交管理员角色-1: 获取某租户下非超管用户列表")
    @WebApiHeaders
    public R<List<UserVo>> getCompanyUserList() {
        final Long companyId = UserContext.getCompanyId();
        Assert.notNull(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        // [2,3] 管理员跟普通员工列表
        List<Integer> noAdminUserTypeList = Arrays.asList(UserTypeEnum.MANAGER.getType(), UserTypeEnum.EMPLOYEE.getType());
        final List<UserVo> list = remoteUserService.getList(new UserQuery().setCompanyId(companyId)
                .setUserTypeList(noAdminUserTypeList)
                .setAccountStatus(AccountStatusEnum.ACTIVATED.getType())
                .setIsEnabled(EnableEnum.ENABLED.getType()));
        return R.ok(list);
    }

    @PostMapping("/roleTransfer")
    @ApiOperation(value = "移交管理员角色-2: 管理员角色转移")
    @WebApiHeaders
    @EnableDistributeLock(key = "transferAdminDto",timeout = 30)
    public R<Boolean> transferAdmin(@RequestBody TransferAdminDto transferAdminDto) {
        Assert.notNull(transferAdminDto.getTargetUserId(), ErrorCode.PARAMETER_NOT_PROVIDED, "targetUserId");
        remoteUserService.transferAdmin(transferAdminDto);
        return R.ok(true);
    }

    @PostMapping("/checkOldPassword")
    @ApiOperation(value = "修改密码-1: 验证旧密码", notes = "仅oldPassword字段必填,userId从请求头取值")
    @WebApiHeaders
    public R<Boolean> checkOldPassword(@RequestBody CheckFleetUserPasswordDto checkFleetUserPasswordDto) {
        Assert.notNull(checkFleetUserPasswordDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(checkFleetUserPasswordDto.getOldPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.OLD_PASSWORD);
        //从请求头读取用户Id
        final Long userId = HeaderUtils.getUserId();
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        ModifyPasswordDto dto = ConvertUtil.convert(checkFleetUserPasswordDto, ModifyPasswordDto.class);
        dto.setUserId(userId);
        //验证旧密码
        remoteUserService.modifyPasswordCheck(dto);
        return R.ok(true);
    }

    @PostMapping("/modifyPassword")
    @ApiOperation("修改密码-2: 设置新密码")
    @WebApiHeaders
    public R<Boolean> modifyPassword(@RequestBody EditFleetUserPasswordDto editFleetUserPasswordDto) {
        Assert.notNull(editFleetUserPasswordDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(editFleetUserPasswordDto.getOldPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.OLD_PASSWORD);
        Assert.hasText(editFleetUserPasswordDto.getNewPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.NEW_PASSWORD);
        // 从请求头读取用户Id
        Long userId = UserContext.getUserId();
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        ModifyPasswordDto dto = ConvertUtil.convert(editFleetUserPasswordDto, ModifyPasswordDto.class);
        dto.setUserId(userId);
        // 修改旧密码
        remoteUserService.modifyPassword(dto);
        return R.ok(true);
    }

    @ApiOperation("忘记密码-1：忘记密码发送超文本链接修改密码邮件")
    @PostMapping(value = "/forgetSendMail")
    @WebApiHeaders
    public R<Boolean> forgetPasswordSendMail(@RequestBody EmailDto emailDto) {
        Assert.hasText(emailDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        // 验证是否存在该email的有效帐号
        final UserQuery userQuery = new UserQuery()
                .setEmail(emailDto.getEmail())
                .setIsEnabled(EnableEnum.ENABLED.getType())
                .setListAccountStatus(Arrays.asList(AccountStatusEnum.ACTIVATED.getType(),AccountStatusEnum.NOT_ACTIVATED.getType()));
        UserVo userVo = remoteUserService.getValidUser(userQuery);
        if (Objects.isNull(userVo)) {
            throw new ServiceException(UserErrorCodeEnum.USER_NOT_FOUND);
        }
        // 验证通过发送邮件验证码
        remoteSendMailService.sendForgetPasswordHtml(emailDto.getEmail(), userVo.getFirstName(), userVo.getLastName());
        return R.ok(true);
    }

    @ApiOperation("发送测试邮件")
    @GetMapping(value = "/mailTest")
    public R<String> mailTest(@RequestParam("email") String email,@RequestParam("async") boolean async) {
        Assert.hasText(email, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        MailRequestDto mailRequestDto = new MailRequestDto();
        mailRequestDto.setSendTo(email);
        mailRequestDto.setSubject("这是一封测试邮件");
        mailRequestDto.setText("这是一封测试邮件；请勿回复此邮件；");
        mailRequestDto.setAsyncSend(async);
        return R.ok(remoteSendMailService.sendHtmlMail(mailRequestDto));
    }

    @ApiOperation("忘记密码-2：忘记密码点击超链接跳转验证身份")
    @PostMapping(value = "/forgetCheckUser")
    @WebApiHeaders
    public R<Boolean> forgetCheckUser(@RequestBody ForgetPasswordRequestDto forgetPasswordRequestDto) {
        //验证请求参数
        Assert.notNull(forgetPasswordRequestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(forgetPasswordRequestDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(forgetPasswordRequestDto.getVerificationCode(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        //rpc调用验证验证码是否过期和正确
        remoteUserService.forgetPasswordCheck(forgetPasswordRequestDto);
        return R.ok(true);
    }

    @ApiOperation("忘记密码-3：忘记密码重新设置新密码")
    @PostMapping(value = "/resetPassword")
    @WebApiHeaders
    public R<Boolean> forgetPasswordConfirm(@RequestBody ForgetPasswordRequestDto requestDto) {
        //验证请求参数
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(requestDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(requestDto.getVerificationCode(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        Assert.hasText(requestDto.getNewPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.NEW_PASSWORD);
        //rpc调用设置新密码
        remoteUserService.forgetPasswordSetNewPassword(requestDto);
        return R.ok(true);
    }

    @ApiOperation("power-bi看板权限验证同步最近三天修改的用户信息")
    @PostMapping(value = "/getUserSyncList")
    @WebApiHeaders
    public List<UserSyncVo> getUserSyncList() {
        return remoteUserService.getUserSyncList();
    }
}
