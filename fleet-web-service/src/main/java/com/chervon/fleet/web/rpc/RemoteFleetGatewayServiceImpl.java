package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.dto.GatewayChangeDto;
import com.chervon.fleet.web.api.entity.dto.GatewayDeviceListDto;
import com.chervon.fleet.web.api.entity.dto.GatewayDto;
import com.chervon.fleet.web.api.entity.enums.*;
import com.chervon.fleet.web.api.entity.error.GatewayErrorCodeEnum;
import com.chervon.fleet.web.api.entity.query.InventoryStatusQuery;
import com.chervon.fleet.web.api.entity.vo.*;
import com.chervon.fleet.web.api.service.RemoteFleetGatewayService;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.service.AppGatewayService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.RuleEngineService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.cache.ProductCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/3 11:46
 */
@Service
@DubboService
@Slf4j
public class RemoteFleetGatewayServiceImpl implements RemoteFleetGatewayService {
    @Autowired
    private AppGatewayService appGatewayService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @Autowired
    private RuleEngineService ruleEngineService;

    @Autowired
    private CompanyDeviceService companyDeviceService;

    @Autowired
    private FleetDeviceConfig fleetDeviceConfig;

    @Override
    public void create(GatewayDto req) {
        Long companyId = UserContext.getCompanyId();
        Long userId = UserContext.getUserId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.notNull(req.getType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.TYPE);
        GatewayTypeEnum typeEnum = GatewayTypeEnum.getEnum(req.getType());
        Assert.notNull(typeEnum, ErrorCode.PARAMETER_FORMAT_ERROR, StringConst.TYPE);
        String gatewayId;
        if (typeEnum.getType() == GatewayTypeEnum.HARD.getType()) {
            // 创建硬件网关
            gatewayId = createHard(req, companyId, userId);
        } else {
            // 创建软件网关
            gatewayId = createSoft(req, companyId, userId);
        }
        if (gatewayId != null) {
            // 新增网关缓存信息
            ruleEngineService.addRemoveGatewayDataProcess(new GatewayChangeDto()
                    .setGatewayId(gatewayId)
                    .setStatus(ChangeTypeEnum.ADD.getType()));
        }
    }

    private String createHard(GatewayDto req, Long companyId, Long userId) {
        Assert.hasText(req.getGatewayName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_NAME);
        // 校验位置信息
        Assert.notNull(req.getCoordinate(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COORDINATE);
        Assert.hasText(req.getLocation(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LOCATION);
        Assert.hasText(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        // 校验设备是否未网关设备
        CompanyDevice companyDevice = companyDeviceService.getOne(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getDeviceId, req.getDeviceId())
                .eq(CompanyDevice::getCompanyId, companyId));
        if (companyDevice == null) {
            // 设备未绑定到租户下，不能创建硬件网关
            throw new ServiceException(GatewayErrorCodeEnum.DEVICE_NOT_BOUND_CANNOT_CREATE_HARD_GATEWAY);
        }
        ProductCache product = remoteOperationCacheService.getProduct(companyDevice.getProductId());
        if (product == null || !StringConst.GATEWAY_DEVICE.equals(product.getType())) {
            // 设备未帮到到租户下，不能创建硬件网关
            throw new ServiceException(GatewayErrorCodeEnum.NOT_GATEWAY_DEVICE_CANNOT_CREATE_HARD_GATEWAY);
        }
        // 创建硬件网关，判断网关是否已经存在
        AppGateway appGateway = appGatewayService.getById(req.getDeviceId());
        if (appGateway != null) {
            // 判断新的网关名称是否已经存在
            if (!StringUtils.equals(appGateway.getName(), req.getGatewayName())) {
                long nameCount = appGatewayService.count(new LambdaQueryWrapper<AppGateway>()
                        .eq(AppGateway::getCompanyId, companyId)
                        .eq(AppGateway::getName, req.getGatewayName())
                        .ne(AppGateway::getId, appGateway.getId()));
                if (nameCount > 0) {
                    // 存在名称相同的网关
                    throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NAME_EXIST);
                }
            }
            AppGateway newAppGateway = new AppGateway();
            BeanUtils.copyProperties(req, newAppGateway);
            newAppGateway.setId(appGateway.getId())
                    .setName(req.getGatewayName())
                    .setCompanyId(companyId)
                    .setUserId(userId)
                    .setActive(1)
                    .setOnlineStatus(OnlineStatusEnum.ONLINE.getType())
                    .setLastConnectedTime(System.currentTimeMillis());
            appGatewayService.updateById(newAppGateway);
        } else {
            // 硬件网关不存在
            long nameCount = appGatewayService.count(new LambdaQueryWrapper<AppGateway>()
                    .eq(AppGateway::getCompanyId, companyId)
                    .eq(AppGateway::getName, req.getGatewayName()));
            if (nameCount > 0) {
                // 存在名称相同的网关
                throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NAME_EXIST);
            }
            AppGateway newAppGateway = new AppGateway();
            BeanUtils.copyProperties(req, newAppGateway);
            newAppGateway.setId(req.getDeviceId())
                    .setName(req.getGatewayName())
                    .setCompanyId(companyId)
                    .setUserId(userId)
                    .setActive(1);
            appGatewayService.save(newAppGateway);
        }
        return req.getDeviceId();
    }

    private String createSoft(GatewayDto req, Long companyId, Long userId) {
        Assert.hasText(req.getUniqueId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UNIQUE_ID);
        AppGateway exist = appGatewayService.getOne(new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId).eq(AppGateway::getUserId, userId)
                .eq(AppGateway::getUniqueId, req.getUniqueId())
                .eq(AppGateway::getType, GatewayTypeEnum.SOFT.getType()));
        if (exist != null) {
            // 存在网关，则不能新增网关
            throw new ServiceException(GatewayErrorCodeEnum.SOFT_GATEWAY_ALREADY_EXIST);
        }
        Assert.notNull(req.getGatewayType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_TYPE);
        SoftGatewayTypeEnum gatewayTypeEnum = SoftGatewayTypeEnum.getEnum(req.getGatewayType());
        Assert.notNull(gatewayTypeEnum, ErrorCode.PARAMETER_FORMAT_ERROR, StringConst.GATEWAY_TYPE);
        Assert.hasText(req.getGatewayName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_NAME);
        if (gatewayTypeEnum == SoftGatewayTypeEnum.FIXED) {
            // 固定网关，校验位置信息
            Assert.notNull(req.getCoordinate(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COORDINATE);
            Assert.hasText(req.getLocation(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LOCATION);
        }
        Assert.hasText(req.getDeviceBrand(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceBrand");
        Assert.hasText(req.getDeviceModel(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceModel");
        Assert.hasText(req.getDeviceSysVersion(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceSysVersion");
        Assert.hasText(req.getAppVersion(), ErrorCode.PARAMETER_NOT_PROVIDED, "appVersion");
        LambdaQueryWrapper<AppGateway> wrapper = new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId)
                .eq(AppGateway::getName, req.getGatewayName());
        AppGateway sameName = appGatewayService.getOne(wrapper);
        if (sameName != null) {
            // 存在名称相同的网关
            throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NAME_EXIST);
        }
        AppGateway appGateway = new AppGateway();
        BeanUtils.copyProperties(req, appGateway);
        appGateway.setName(req.getGatewayName()).setDeviceId(null)
                .setCompanyId(companyId)
                .setUserId(userId)
                .setActive(1).setOnlineStatus(OnlineStatusEnum.ONLINE.getType()).setLastConnectedTime(System.currentTimeMillis());
        appGatewayService.save(appGateway);
        return appGateway.getId();
    }

    @Override
    public void edit(GatewayDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        // 编辑模式，网关id必填
        Assert.hasText(req.getGatewayId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_ID);
        Assert.hasText(req.getGatewayName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_NAME);
        AppGateway gateway = appGatewayService.getOne(new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId)
                .eq(AppGateway::getId, req.getGatewayId())
                .eq(AppGateway::getActive, 1));
        if (gateway == null) {
            throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NOT_EXIST);
        }
        LambdaQueryWrapper<AppGateway> wrapper = new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId)
                .eq(AppGateway::getName, req.getGatewayName())
                .ne(AppGateway::getId, req.getGatewayId());
        AppGateway sameName = appGatewayService.getOne(wrapper);
        if (sameName != null) {
            // 存在名称相同的网关
            throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NAME_EXIST);
        }
        AppGateway appGateway = new AppGateway();
        appGateway.setId(gateway.getId())
                .setName(req.getGatewayName())
                .setLocation(req.getLocation())
                .setCoordinate(req.getCoordinate());
        appGatewayService.updateById(appGateway);
        // 修改网关相关缓存信息
        ruleEngineService.updateGatewayInfo(appGatewayService.getById(gateway.getId()));
    }

    @Override
    public void remove(String gatewayId) {
        Long companyId = UserContext.getCompanyId();
        Assert.hasText(gatewayId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_ID);
        AppGateway gateway = appGatewayService.getOne(new LambdaQueryWrapper<AppGateway>().eq(AppGateway::getCompanyId, companyId).eq(AppGateway::getId, gatewayId).eq(AppGateway::getActive, 1));
        if (gateway == null) {
            throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NOT_EXIST);
        }
        // 更新缓存
        ruleEngineService.addRemoveGatewayDataProcess(new GatewayChangeDto()
                .setGatewayId(gatewayId)
                .setCompanyId(gateway.getCompanyId())
                .setStatus(ChangeTypeEnum.DELETED.getType()));
        // 租户设备表修改网关为null，设备状态为未知
        companyDeviceService.update(new CompanyDevice(), new LambdaUpdateWrapper<CompanyDevice>()
                .set(CompanyDevice::getGatewayId, null)
                .eq(CompanyDevice::getGatewayId, gatewayId));
        // 移除网关
        appGatewayService.removeById(gatewayId);
    }

    @Override
    public GatewayVo getGatewayDetail(String gatewayId) {
        Long companyId = UserContext.getCompanyId();
        Assert.hasText(gatewayId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GATEWAY_ID);
        AppGateway gateway = appGatewayService.getOne(new LambdaQueryWrapper<AppGateway>().eq(AppGateway::getCompanyId, companyId).eq(AppGateway::getId, gatewayId).eq(AppGateway::getActive, 1));
        if (gateway == null) {
            return new GatewayVo();
        }
        GatewayVo res = new GatewayVo();
        BeanUtils.copyProperties(gateway, res);
        res.setGatewayId(gatewayId);
        res.setGatewayName(gateway.getName());
        // 如果是mobile网关，则去缓存查询地理位置
        if (gateway.getType() == GatewayTypeEnum.SOFT.getType() && gateway.getGatewayType() == SoftGatewayTypeEnum.MOBILE.getType()) {
            InventoryStatusQuery query = new InventoryStatusQuery();
            query.setGatewayId(gatewayId);
            query.setCompanyId(UserContext.getCompanyId());
            List<DeviceStatusVo> data = ruleEngineService.getWarehouseStatusOnly(query);
            if (data != null && data.size() > 0) {
                DeviceStatusVo deviceStatus = data.get(0);
                res.setCoordinate(deviceStatus.getCoordinate());
                res.setLocation(deviceStatus.getLocation());
            }
        }
        return res;
    }

    @Override
    public GatewayIndexVo gatewayIndex(String uniqueId) {
        Assert.hasText(uniqueId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UNIQUE_ID);
        GatewayIndexVo res = new GatewayIndexVo();
        res.setExisted(false);
        Long companyId = UserContext.getCompanyId();
        Long userId = UserContext.getUserId();
        if(Objects.isNull(userId) || Objects.isNull(companyId)){
            throw new ServiceException(ErrorCode.UNAUTHORIZED);
        }
        AppGateway appGateway = appGatewayService.getOne(new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getActive, 1)
                .eq(AppGateway::getCompanyId, companyId)
                .eq(AppGateway::getUserId, userId)
                .eq(AppGateway::getUniqueId, uniqueId)
                .eq(AppGateway::getType, GatewayTypeEnum.SOFT.getType()));
        if (appGateway != null) {
            BeanUtils.copyProperties(appGateway, res);
            res.setGatewayId(appGateway.getId());
            res.setGatewayName(appGateway.getName());
            res.setExisted(true);
        }
        return res;
    }

    @Override
    public List<GatewayDeviceInfoVo> listDevice(GatewayDeviceListDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        if (CollectionUtils.isEmpty(req.getDeviceIds())) {
            return new ArrayList<>();
        }
        // 排除网关设备
        fleetDeviceConfig.getSub().getExclude().forEach(e -> req.getDeviceIds().removeIf(i -> StringUtils.containsIgnoreCase(i, e)));
        if(CollectionUtils.isEmpty(req.getDeviceIds())){
            return new ArrayList<>();
        }
        // 过滤不是该租户绑定的设备
        List<CompanyDevice> listCompanyDevice = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .in(CompanyDevice::getDeviceId, req.getDeviceIds()));
        if (CollectionUtils.isEmpty(listCompanyDevice)) {
            return new ArrayList<>();
        }
        //查询设备状态
        InventoryStatusQuery query = new InventoryStatusQuery();
        query.setListDeviceId(listCompanyDevice.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList()))
                .setCompanyId(companyId);
        List<DeviceStatusVo> deviceStatusList = ruleEngineService.getWarehouseStatusByCache(query);
        Map<String, DeviceStatusVo> deviceStatusMap = deviceStatusList.stream().collect(Collectors.toMap(DeviceStatusVo::getDeviceId, Function.identity(), (k1, k2) -> k2));
        // 按照入参deviceIds顺序返回
        Map<String, CompanyDevice> companyDeviceMap = listCompanyDevice.stream().collect(Collectors.toMap(CompanyDevice::getDeviceId, Function.identity()));
        //查询产品信息
        List<Long> productIds = listCompanyDevice.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList());
        List<ProductCache> products = remoteOperationCacheService.listProducts(productIds);
        Map<Long, ProductCache> productMap = products.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));

        List<GatewayDeviceInfoVo> res = new ArrayList<>();
        for (String deviceId : req.getDeviceIds()) {
            CompanyDevice companyDevice = companyDeviceMap.get(deviceId);
            if (companyDevice != null) {
                GatewayDeviceInfoVo vo = new GatewayDeviceInfoVo();
                vo.setDeviceId(companyDevice.getDeviceId());
                vo.setDeviceName(companyDevice.getDeviceName());
                ProductCache product = productMap.getOrDefault(companyDevice.getProductId(), new ProductCache());
                vo.setProductId(product.getId());
                vo.setProductIconUrl(product.getUrl());
                vo.setCommodityModel(product.getCommodityModel());
                DeviceStatusVo deviceStatus = deviceStatusMap.get(companyDevice.getDeviceId());
                if(Objects.isNull(deviceStatus)){
                    vo.setReportCompleteStatus(ReportCompleteStatusEnum.REPORT_TIMED_OUT.getType());
                    vo.setRssi(null);
                }else{
                    vo.setReportCompleteStatus(deviceStatus.getReportCompleteStatus());
                    vo.setRssi(deviceStatus.getRssi());
                }
                res.add(vo);
            }
        }
        return res;
    }

    @Override
    public FixGatewayCoordinateVo fixGatewayCoordinate() throws ServiceException {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        final String key = RedisConst.getCompanyFixGatewayCoordinate(companyId);
        FixGatewayCoordinateVo response = RedisUtils.getCacheObject(key);
        if(!Objects.isNull(response)){
            return response;
        }
        List<AppGateway> list = appGatewayService.list(new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getActive, 1)
                .eq(AppGateway::getCompanyId, companyId)
                .eq(AppGateway::getGatewayType, GatewayTypeEnum.HARD.getType())
                .eq(AppGateway::getType, GatewayTypeEnum.HARD.getType())
                .select(AppGateway::getId, AppGateway::getCoordinate));
        FixGatewayCoordinateVo vo = new FixGatewayCoordinateVo();
        vo.setDistance(new BigDecimal(1000));
        if (!CollectionUtils.isEmpty(list)) {
            vo.setList(list.stream().map(a -> {
                return new GatewayCoordinateVo().setGatewayId(a.getId()).setCoordinate(a.getCoordinate());
            }).collect(Collectors.toList()));
        }
        RedisUtils.setWithExpire(key,vo,300L);
        return vo;
    }

}
