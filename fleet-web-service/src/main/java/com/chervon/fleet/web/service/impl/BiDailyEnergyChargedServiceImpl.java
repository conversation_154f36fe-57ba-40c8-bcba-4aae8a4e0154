package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.core.utils.DateUtils;
import com.chervon.fleet.web.api.entity.query.dashboard.EnergyUsageDashboardQuery;
import com.chervon.fleet.web.entity.po.BiDailyEnergyCharged;
import com.chervon.fleet.web.entity.vo.dashboard.EnergyUsageDashboardVo;
import com.chervon.fleet.web.mapper.BiDailyEnergyChargedMapper;
import com.chervon.fleet.web.service.BiDailyEnergyChargedService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 租户下充电电量统计表(看板：Energy Charged图--柱状图使用)
 * ---Energy Charged统计租户所有Power HUB在统计时段内每天给电池充电的总电量，单位为kWh，并展示租户的所有设备（包含已删除设备）的历史总的充给电池的总电量和统计时段内有数据上报的日期的平均充给电池的电量；以下两个参数为基于此表数据汇总实时统计参数：Historical Total ：当前用户绑定的全部Charger在被当前租户绑定后的指定的时间段内的总充电电量(单位:KWh)
 * Daily Average: 设备被当前租户绑定后的指定时间段内的充电电量÷有充电数据的天数服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiDailyEnergyChargedServiceImpl extends ServiceImpl<BiDailyEnergyChargedMapper, BiDailyEnergyCharged>
    implements BiDailyEnergyChargedService {

    @Override
    public List<EnergyUsageDashboardVo> energyCharged(EnergyUsageDashboardQuery query) {
        List<BiDailyEnergyCharged> biDailyEnergyChargedList = this.list(new LambdaQueryWrapper<BiDailyEnergyCharged>()
            .eq(BiDailyEnergyCharged::getCompanyId, query.getCompanyId())
            .ge(BiDailyEnergyCharged::getStrDate, query.getStartTime())
            .le(BiDailyEnergyCharged::getStrDate, query.getEndTime())
            .orderByAsc(BiDailyEnergyCharged::getStrDate));
        Map<String, BiDailyEnergyCharged> dateEnergyChargedMap = biDailyEnergyChargedList
            .stream().collect(Collectors.toMap(BiDailyEnergyCharged::getStrDate, a -> a));
        // 日期字符串列表
        List<String> betweenDateList = DateUtils.getBetweenDateStrList(query.getStartTime(), query.getEndTime());
        List<EnergyUsageDashboardVo> result = new ArrayList<>();
        for (String date : betweenDateList) {
            BiDailyEnergyCharged biDailyEnergyCharged = dateEnergyChargedMap.get(date);
            EnergyUsageDashboardVo vo;
            if (null == biDailyEnergyCharged) {
                vo = new EnergyUsageDashboardVo().setDate(date);
            } else {
                vo = ConvertUtil.convert(biDailyEnergyCharged, EnergyUsageDashboardVo.class);
                vo.setDate(date);
            }
            result.add(vo);
        }
        return result;
    }
}