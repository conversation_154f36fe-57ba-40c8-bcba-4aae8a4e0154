package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet设备上报数据完成缓存结构信息
 *
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@Accessors(chain = true)
@ApiModel("fleet网关维度的设备状态信息")
public class GatewayCache implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 网关id
     */
    private String id;
    /**
     * 网关名称
     */
    private String name;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 软网关类型：1固定网关   2移动网关
     */
    private Integer gatewayType;
    /**
     * 网关软硬件类型：1硬件网关   2软网关
     */
    private Integer type;
    /**
     * 硬件网关对应的设备id
     */
    private String deviceId;
    /**
     * 位置坐标，逗号分隔：纬度，经度
     */
    private String coordinate;
    /**
     * 网关地址信息
     */
    private String location;
    /**
     * 手机唯一码
     */
    private String uniqueId;
    /**
     * 网关在线状态: 1-online   2-offline
     */
    private Integer onlineStatus;
    /**
     * 网关最新上报数据时间
     */
    private Long lastConnectedTime;
}
