package com.chervon.fleet.web.service.translate;

import cn.hutool.core.collection.CollectionUtil;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.core.TranslateService;
import com.chervon.common.web.entity.MetaContext;
import com.chervon.common.web.util.UserContext;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通过注解统一获取国际化后内容*
 * <AUTHOR> 2022/12/6
 */
@Component
public class FleetCategoryTranslateImpl implements TranslateService<Object,FleetCategoryListVo,String> {
    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    public String getInstanceCode(){
        return "fleetCategory";
    }

    /**
     * * 批量获取原字段对应的值列表
     * @param context
     * @return
     * @throws IllegalAccessException
     */
    @Override
    public void batchGetSourceValue(MetaContext<String,FleetCategoryListVo> context){
        String lang=UserContext.getClientInfo().getLanguage();
        if(lang==null || lang.equals(I18nController.DEFAULT_LANGUAGE)){
            return;
        }
        final Set<String> listSourceValue = context.getListSourceValue();
        if(CollectionUtil.isEmpty(listSourceValue)){
            return;
        }
        //批量读取国际化原分类编号
        List<String> categoryCodes=new ArrayList<>(listSourceValue);
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCodes(categoryCodes);
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        //设置目标值
        context.setListTargetValue(fleetCategoryList);
    }

    /**
     * * 批量根据原值结果列表赋值目标结果对象集合
     * @param dataEntity 数据实体列表
     * @throws IllegalAccessException
     */
    @Override
    public void batchSetTargetValue(List<Object> dataEntity, MetaContext<String,FleetCategoryListVo> context) throws IllegalAccessException {
        List<FleetCategoryListVo> fleetCategoryList = context.getListTargetValue();
        if(CollectionUtil.isEmpty(fleetCategoryList)){
            return;
        }
        Field sourceField=context.getSourceField();
        sourceField.setAccessible(true);
        List<Field> targetField=context.getTargetField();
        Map<String, String> categoryMap = fleetCategoryList.stream().collect(Collectors.toMap(FleetCategoryListVo::getCode, FleetCategoryListVo::getCategoryName));
        for(Object dataDto:dataEntity){
            final String sourceValue = sourceField.get(dataDto).toString();
            final String result = categoryMap.get(sourceValue);
            if(Objects.isNull(result)){
                continue;
            }
            for (Field field : targetField) {
                field.setAccessible(true);
                field.set(dataDto, result);
            }
        }
    }
}