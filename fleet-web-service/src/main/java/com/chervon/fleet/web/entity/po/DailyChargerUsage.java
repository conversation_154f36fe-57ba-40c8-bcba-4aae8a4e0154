package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-31 09:50
 **/
@Data
@TableName("t_data_daily_charging")
public class DailyChargerUsage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId
    private Long id;
    @ApiModelProperty("设备ID")
    private String deviceId;
    @ApiModelProperty("设备一级分类编码")
    private String categoryCode;
    @ApiModelProperty("电池二级分类编码")
    private String secondCategoryCode;
    @ApiModelProperty("租户ID")
    private Long companyId;
    /**
     * 数据状态：0 使用数据，  -1 每日最后上报的缓存值
     */
    private Integer status;
    @ApiModelProperty("日期: 2023-07-12")
    private String date;
    @ApiModelProperty("当日总充电能量: 2027: 总充电能量")
    private Integer chargingEnergy;
    @ApiModelProperty("当日充电时长(单位:秒)")
    private Integer chargingTime;
    /**
     * 开始和截止的坐标值
     */
    private String spanChargingEnergy;
    private String spanChargingTime;
    /**
     * 创建时间
     */
    private Long createTime;
    @ApiModelProperty("更新时间")
    private Long modifyTime;

    public void setId(){
        this.id=(long)(this.deviceId+this.companyId+this.date+"0").hashCode();
    }
}
