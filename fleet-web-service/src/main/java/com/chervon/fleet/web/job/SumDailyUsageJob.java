package com.chervon.fleet.web.job;

import com.alibaba.csp.sentinel.util.function.Tuple2;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.*;
import com.chervon.fleet.web.mapper.DailyBatteryUsageMapper;
import com.chervon.fleet.web.mapper.DailyChargerUsageMapper;
import com.chervon.fleet.web.mapper.DailyToolUsageMapper;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.utils.DateUtil;
import com.chervon.iot.middle.api.dto.query.FleetUsageQuery;
import com.chervon.iot.middle.api.service.RemoteFleetDailyUsageService;
import com.chervon.iot.middle.api.vo.usage.FleetBatteryDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetChargerDailyUsageVo;
import com.chervon.iot.middle.api.vo.usage.FleetToolDailyUsageVo;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.chervon.fleet.web.utils.PageResultUtils.manualPartition;

/**
 * 数据报表统计服务
 */
@Component
@Slf4j
@AllArgsConstructor
public class SumDailyUsageJob {
    private final CompanyDeviceService companyDeviceService;
    private final DailyBatteryUsageMapper dailyBatteryUsageMapper;
    private final DailyToolUsageService dailyToolUsageService;
    private final DailyChargerUsageService dailyChargerUsageService;
    private final DailyBatteryUsageService dailyBatteryUsageService;
    private final DailyToolUsageMapper dailyToolUsageMapper;
    private final DailyChargerUsageMapper dailyChargerUsageMapper;
    private final BiDailyBatteryUsageService biDailyBatteryUsageService;
    private final BiDailyCategoryUsageService biDailyCategoryUsageService;
    private final BiDailyEnergyChargedService biDailyEnergyChargedService;
    private final DataDictionaryService dataDictionaryService;

    @DubboReference
    private RemoteFleetDailyUsageService remoteFleetDailyUsageService;
    /**
     * 执行设备历史日统计数据服务
     */
    @XxlJob("SumDailyUsageJob")
    public void sumDailyUsageJob() {
        //执行租户设备日统计数据
        executeDataDailyUsageJob();
        //汇总设备日统计数据生成报表
        executeBiDailyUsageJob();
    }

    public void executeBiDailyUsageJob() {
        log.info("设备BI日统计报表定时作业执行开始！");
        final Set<String> listDate = getRunDateList();
        List<Long> listCompanyId = companyDeviceService.getCompanyIdList();
        if(CollectionUtils.isEmpty(listCompanyId)){
            log.info("无租户设备信息需要统计，直接返回");
            return;
        }
        StringBuilder result=new StringBuilder();
        for(String date:listDate){
            if(StringUtils.isEmpty(date)){
                continue;
            }
            for(Long companyId : listCompanyId) {
                final String msg = jobExecute(null, date, companyId);
                log.info("执行定时事件完成，租户id:{}，日期：{}，执行结果：{}", companyId, date, msg);
                result.append(msg);
            }
        }
        //清除延迟上报日期列表
        dataDictionaryService.setValueByKey(StringConst.HISTORY_DATE,"");
        log.info("设备BI日统计报表定时作业执行完成！结果：{}",result);
    }

    /**
     * 统计设备日使用量：DataDailyUsage
     */
    @XxlJob("DataDailyUsageJob")
    public void executeDataDailyUsageJob() {
        log.info("设备日使用数据统计作业执行开始！");
        final Set<String> listDate = getRunDateList();
        List<Long> listCompanyId = companyDeviceService.getCompanyIdList();
        if(CollectionUtils.isEmpty(listCompanyId)){
            log.info("无租户设备信息需要统计，直接返回");
            return;
        }
        String result="";
        for(String date:listDate){
            if(StringUtils.isEmpty(date)){
                continue;
            }
            for(Long companyId : listCompanyId) {
                final Integer x = doStatisticsToolUsage(date, companyId);
                final Integer y = doStatisticsChargerUsage(date, companyId);
                final Integer z = doStatisticsBatteryUsage(date, companyId);
                result = MessageFormat.format("{0}-{1}-{2}",x,y,z);
                log.info("执行dataDailyUsage定时事件完成，租户id:{}，日期：{}，数量：{}", companyId, date,result);
            }
        }
        log.info("设备日使用数据统计作业执行完成！结果：{}",result);
    }

    @NotNull
    private Set<String> getRunDateList() {
        String param = XxlJobHelper.getJobParam();
        String historyDate = dataDictionaryService.getValueByKeyNoCache(StringConst.HISTORY_DATE);
        if(StringUtils.isNotEmpty(historyDate)){
            log.info("历史延迟上报日期："+historyDate);
            param=param+","+historyDate;
        }
        Set<String> listDate=new HashSet<>();
        LocalDate today = LocalDate.now().minusDays(1L);
        listDate.add(today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        LocalDate yesterday = LocalDate.now().minusDays(1L);
        listDate.add(yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        if(StringUtils.isNotEmpty(param)){
            String[] arrDate = param.split(",");
            listDate.addAll(Arrays.asList(arrDate));
        }
        return listDate;
    }


    /**
     * 根据租户当前绑定的设备统计设备日使用量
     * @param date
     * @param companyId
     */
    public Integer dataDailyUsageByCompany(String date, Long companyId,String category) {
        LambdaQueryWrapper<CompanyDevice> queryWrapper= new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getIsDeleted,0)
                .select(CompanyDevice::getDeviceId,CompanyDevice::getCompanyId,
                        CompanyDevice::getFirstCategoryCode,CompanyDevice::getSecondCategoryCode);
        if(category.equalsIgnoreCase("tool")){
            final List<String> listCategory = Arrays.asList(FleetFirstCategoryEnum.HANDHELD.getCategoryCode(),
                    FleetFirstCategoryEnum.WALK_BEHIND.getCategoryCode(), FleetFirstCategoryEnum.RIDE_ON.getCategoryCode());
            queryWrapper.in(CompanyDevice::getFirstCategoryCode,listCategory);
        }else if(category.equalsIgnoreCase(FleetFirstCategoryEnum.CHARGER.getCategoryCode())){
            queryWrapper.eq(CompanyDevice::getFirstCategoryCode,FleetFirstCategoryEnum.CHARGER.getCategoryCode());
        }else if(category.equalsIgnoreCase(FleetFirstCategoryEnum.BATTERY.getCategoryCode())){
            queryWrapper.eq(CompanyDevice::getFirstCategoryCode,FleetFirstCategoryEnum.BATTERY.getCategoryCode());
        }
        final List<CompanyDevice> list = companyDeviceService.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        final Tuple2<Long, Long> dayBeginEndTs = DateUtil.getDayBeginEndTs(date);
        List<FleetUsageQuery> queryList=new ArrayList<>();
        for(CompanyDevice companyDevice:list){
            FleetUsageQuery query = BeanCopyUtils.copy(companyDevice, FleetUsageQuery.class);
            query.setDate(date);
            query.setTimeStart(dayBeginEndTs.r1 / 1000);
            query.setTimeEnd(dayBeginEndTs.r2 / 1000);
            queryList.add(query);
        }
        if(category.equalsIgnoreCase("tool")){
            return queryAndSaveToolDailyUsage(queryList);
        }else if(category.equalsIgnoreCase(FleetFirstCategoryEnum.CHARGER.getCategoryCode())){
            return queryAndSaveChargerDailyUsage(queryList);
        }else if(category.equalsIgnoreCase(FleetFirstCategoryEnum.BATTERY.getCategoryCode())){
            return queryAndSaveBatteryDailyUsage(queryList);
        }
        return 0;
    }
    /**
     * 统计工具设备日使用量
     * @param date
     * @param companyId
     */
    private Integer doStatisticsToolUsage(String date, Long companyId) {
        try {
            final List<DailyToolUsage> list = dailyToolUsageService.list(new LambdaQueryWrapper<DailyToolUsage>()
                    .eq(DailyToolUsage::getCompanyId, companyId)
                    .eq(DailyToolUsage::getDate,date)
                    .select(DailyToolUsage::getDeviceId,DailyToolUsage::getCompanyId,
                            DailyToolUsage::getCategoryCode));
            if(CollectionUtils.isEmpty(list)){
                return 0;
            }
            List<FleetUsageQuery> queryList=new ArrayList<>();
            final Tuple2<Long, Long> dayBeginEndTs = DateUtil.getDayBeginEndTs(date);
            for(DailyToolUsage toolUsage:list){
                FleetUsageQuery query = BeanCopyUtils.copy(toolUsage, FleetUsageQuery.class);
                query.setDate(date);
                query.setTimeStart(dayBeginEndTs.r1 / 1000);
                query.setTimeEnd(dayBeginEndTs.r2 / 1000);
                query.setFirstCategoryCode(toolUsage.getCategoryCode());
                queryList.add(query);
            }
            return queryAndSaveToolDailyUsage(queryList);
        } catch (Exception e) {
            log.error("doStatisticsToolUsage error:{}",e);
            return -1;
        }
    }

    private int queryAndSaveToolDailyUsage(List<FleetUsageQuery> queryList) {
        int groupSize = 5;
        int count=0;
        List<List<FleetUsageQuery>> partitionedList = manualPartition(queryList, groupSize);
        for (List<FleetUsageQuery> subList : partitionedList) {
            final List<FleetToolDailyUsageVo> toolDayUsage = remoteFleetDailyUsageService.getToolDayUsage(subList);
            dailyToolUsageService.saveDataDailyToolUsage(toolDayUsage);
            count+=toolDayUsage.size();
        }
        return count;
    }

    /**
     * 统计充电器设备日使用量
     * @param date 日期
     * @param companyId 租户
     */
    private Integer doStatisticsChargerUsage(String date, Long companyId) {
        try {
            final List<DailyChargerUsage> list = dailyChargerUsageService.list(new LambdaQueryWrapper<DailyChargerUsage>()
                    .eq(DailyChargerUsage::getCompanyId, companyId)
                    .eq(DailyChargerUsage::getDate,date)
                    .select(DailyChargerUsage::getDeviceId,DailyChargerUsage::getCompanyId,
                            DailyChargerUsage::getSecondCategoryCode));
            if(CollectionUtils.isEmpty(list)){
                return 0;
            }
            List<FleetUsageQuery> queryList = BeanCopyUtils.copyList(list, FleetUsageQuery.class);
            final Tuple2<Long, Long> dayBeginEndTs = DateUtil.getDayBeginEndTs(date);
            queryList.forEach(q-> {
                q.setFirstCategoryCode(FleetFirstCategoryEnum.CHARGER.getCategoryCode());
                q.setDate(date);
                q.setTimeStart(dayBeginEndTs.r1 / 1000);
                q.setTimeEnd(dayBeginEndTs.r2 / 1000);
            });
            return queryAndSaveChargerDailyUsage(queryList);
        } catch (Exception e) {
            log.error("doStatisticsChargerUsage error:{}",e);
            return -1;
        }
    }

    private int queryAndSaveChargerDailyUsage(List<FleetUsageQuery> queryList) {
        if(CollectionUtils.isEmpty(queryList)){
            return 0;
        }
        int groupSize = 5;
        int count=0;
        List<List<FleetUsageQuery>> partitionedList = manualPartition(queryList, groupSize);
        for (List<FleetUsageQuery> subList : partitionedList) {
            final List<FleetChargerDailyUsageVo> chargerDayUsage = remoteFleetDailyUsageService.getChargerDayUsage(subList);
            dailyChargerUsageService.saveDataDailyChargerUsage(chargerDayUsage);
            count+=chargerDayUsage.size();
        }
        return count;
    }

    /**
     * 统计充电器设备日使用量
     * @param date 日期
     * @param companyId 租户
     */
    private Integer doStatisticsBatteryUsage(String date, Long companyId) {
        try {
            final List<DailyBatteryUsage> list = dailyBatteryUsageService.list(new LambdaQueryWrapper<DailyBatteryUsage>()
                    .eq(DailyBatteryUsage::getCompanyId, companyId)
                    .eq(DailyBatteryUsage::getDate,date)
                    .select(DailyBatteryUsage::getDeviceId,DailyBatteryUsage::getCompanyId,
                            DailyBatteryUsage::getSecondCategoryCode));
            if(CollectionUtils.isEmpty(list)){
                return 0;
            }
            List<FleetUsageQuery> queryList = BeanCopyUtils.copyList(list, FleetUsageQuery.class);
            final Tuple2<Long, Long> dayBeginEndTs = DateUtil.getDayBeginEndTs(date);
            queryList.forEach(q-> {
                q.setFirstCategoryCode(FleetFirstCategoryEnum.BATTERY.getCategoryCode());
                q.setDate(date);
                q.setTimeStart(dayBeginEndTs.r1 / 1000);
                q.setTimeEnd(dayBeginEndTs.r2 / 1000);
            });
            return queryAndSaveBatteryDailyUsage(queryList);
        } catch (Exception e) {
            log.error("doStatisticsBatteryUsage error:{}",e);
            return -1;
        }
    }

    private int queryAndSaveBatteryDailyUsage(List<FleetUsageQuery> queryList) {
        if(CollectionUtils.isEmpty(queryList)){
            return 0;
        }
        int groupSize = 5;
        int count=0;
        List<List<FleetUsageQuery>> partitionedList = manualPartition(queryList, groupSize);
        for (List<FleetUsageQuery> subList : partitionedList) {
            final List<FleetBatteryDailyUsageVo> batteryDayUsage = remoteFleetDailyUsageService.getBatteryDayUsage(subList);
            dailyBatteryUsageService.saveDataDailyBatteryUsage(batteryDayUsage);
            count+=batteryDayUsage.size();
        }
        return count;
    }


    /**
     * KeyedProcessFunction.OnTimerContext
     * @param cmd
     * @param date
     * @param companyId
     */
    public String jobExecute(String cmd, String date, Long companyId) {
        StringBuilder sb = new StringBuilder();
        sb.append("ts:" + System.currentTimeMillis() + "任务开始执行,date:" + date + " companyId:" + companyId);
        try {
            if (StringUtils.isEmpty(date) || companyId == null) {
                return "date、companyId is empty,not execute job!!!" + date + companyId;
            }
            //统计日电池二级分类使用量：入库：t_bi_daily_battery_usage
            if (StringUtils.isEmpty(cmd) || StringConst.CMD_BATTERY.equals(cmd)) {
                sumDailyBatteryUsage(date, companyId, sb);
            }

            //统计每日工具一级分类使用时长：入库：t_bi_daily_category_usage
            if (StringUtils.isEmpty(cmd) || StringConst.CMD_TOOL.equals(cmd)) {
                sumDailyCategoryUsage(date, companyId, sb);
            }

            //统计日日充电能量：入库：t_bi_daily_energy_charged
            if (StringUtils.isEmpty(cmd) || StringConst.CMD_CHARGER.equals(cmd)) {
                sumDailyCharged(date, companyId, sb);
            }
            return sb.toString();
        } catch (Exception e) {
            final String format = MessageFormat.format("定时作业执行发生未捕捉异常：作业日期：{},租户：{},错误原因：{}", date, companyId, e);
            log.error(format);
            return format;
        }
    }

    /**
     * * getBiDailyCategoryUsage
     * @param date
     * @return
     */
    public List<BiDailyCategoryUsage> getBiDailyCategoryUsage(String date,Long companyId){
        try{
            final List<DailyToolUsageCount> dailyToolUsageCounts = dailyToolUsageMapper.countDailyToolUsageByDate(date,companyId);
            List<BiDailyCategoryUsage> list=new ArrayList<>();
            for(DailyToolUsageCount item:dailyToolUsageCounts){
                BiDailyCategoryUsage biDailyCategoryUsage=new BiDailyCategoryUsage();
                biDailyCategoryUsage.setCategoryCode(item.getCategoryCode());
                biDailyCategoryUsage.setCategoryName(item.getCategoryCode());
                biDailyCategoryUsage.setCompanyId(item.getCompanyId());
                biDailyCategoryUsage.setDailyDeviceUsageCount(item.getUsageCount());
                BigDecimal dailyAverageUsageTime = BigDecimal.ZERO;
                if (item.getUsageCount() > 0){
                    dailyAverageUsageTime = new BigDecimal(item.getUsageDuration()).divide(new BigDecimal(item.getUsageCount()*60), 0, RoundingMode.HALF_UP);
                }
                biDailyCategoryUsage.setDailyAverageUsageTime(dailyAverageUsageTime.intValue());
                Integer categoryDeviceCount = companyDeviceService.getCategoryCountCache(item.getCompanyId(),item.getCategoryCode());
                Integer companyDeviceCount = companyDeviceService.getCategoryCountCache(item.getCompanyId(), "Tools");
                biDailyCategoryUsage.setDeviceCategoryCount(categoryDeviceCount);
                biDailyCategoryUsage.setDailyDeviceTotalCount(companyDeviceCount);
                biDailyCategoryUsage.setDailyUsageRate(new BigDecimal(item.getUsageCount()/companyDeviceCount));
                final BigDecimal dailyTotalUsageMinute = new BigDecimal(item.getUsageDuration()).divide(new BigDecimal(60), 0, RoundingMode.HALF_UP);
                biDailyCategoryUsage.setDailyTotalUsageTime(dailyTotalUsageMinute.intValue());
                biDailyCategoryUsage.setDate(DateUtil.getDateByStr(item.getDate()));
                biDailyCategoryUsage.setStrDate(item.getDate());
                biDailyCategoryUsage.setModifyTime(new Date());
                biDailyCategoryUsage.setId();
                list.add(biDailyCategoryUsage);
            }
            return list;
        }
        catch (Throwable e){
            log.error("getBiDailyCategoryUsage cause Exception,sqlSession transaction rollback:{}",e);
        }
        return new ArrayList<>();
    }

    /**
     * * SinkToBiDailyBatteryUsage
     * @param date
     * @return
     */
    public List<BiDailyBatteryUsage> getBiDailyBatteryUsage(String date,Long companyId){
        try{
            final List<DailyBatteryUsageCount> dailyBatteryUsageCounts = dailyBatteryUsageMapper.countDailyBatteryUsageByDate(date,companyId);
            List<BiDailyBatteryUsage> list=new ArrayList<>();
            for(DailyBatteryUsageCount item:dailyBatteryUsageCounts){
                BiDailyBatteryUsage biDailyBatteryUsage=new BiDailyBatteryUsage();
                biDailyBatteryUsage.setDailyUsageTimes(item.getDailyNumberTimes());
                biDailyBatteryUsage.setBatteryType(item.getSecondCategoryCode());
                biDailyBatteryUsage.setCompanyId(item.getCompanyId());
                biDailyBatteryUsage.setDate(DateUtil.getDateByStr(item.getDate()));
                biDailyBatteryUsage.setStrDate(item.getDate());
                //使用时长大于0的
                biDailyBatteryUsage.setDailyDeviceUsageCount(item.getDeviceCount());
                BigDecimal dailyAverageUsageTime = BigDecimal.ZERO;
                if (item.getDeviceCount() > 0){
                    dailyAverageUsageTime = new BigDecimal(item.getDailyUsageTime()).divide(new BigDecimal(item.getDeviceCount()*60), 0, RoundingMode.HALF_UP);
                }
                biDailyBatteryUsage.setDailyAverageUsageTime(dailyAverageUsageTime.intValue());
                final BigDecimal dailyUsageMinute = new BigDecimal(item.getDailyUsageTime()).divide(new BigDecimal(60), 0, RoundingMode.HALF_UP);
                biDailyBatteryUsage.setDailyUsageDuration(dailyUsageMinute.intValue());
                biDailyBatteryUsage.setModifyTime(new Date());
                biDailyBatteryUsage.setId();
                list.add(biDailyBatteryUsage);
            }
            return list;
        }
        catch (Throwable e){
            log.error("getBiDailyBatteryUsage cause Exception,sqlSession transaction rollback:{}",e);
        }
        return new ArrayList<>();
    }

    /**
     * * SinkToBiDailyEnergyCharged
     * @param date
     * @return
     */
    public List<BiDailyEnergyCharged> getBiDailyEnergyCharged(String date,Long companyId){
        try{
            final List<DailyChargingCount> dailyChargingCounts = dailyChargerUsageMapper.countDailyChargingByDate(date,companyId);
            List<BiDailyEnergyCharged> list=new ArrayList<>();
            for(DailyChargingCount item:dailyChargingCounts){
                BiDailyEnergyCharged biDailyEnergyCharged=new BiDailyEnergyCharged();
                biDailyEnergyCharged.setEnergyCharged(item.getChargingEnergy());
                biDailyEnergyCharged.setDate(DateUtil.getDateByStr(item.getDate()));
                biDailyEnergyCharged.setStrDate(item.getDate());
                biDailyEnergyCharged.setCompanyId(item.getCompanyId());
                biDailyEnergyCharged.setModifyTime(new Date());
                biDailyEnergyCharged.setId();
                list.add(biDailyEnergyCharged);
            }
            return list;
        }
        catch (Throwable e){
            log.error("getBiDailyEnergyCharged cause Exception,sqlSession transaction rollback:{}",e);
        }
        return new ArrayList<>();
    }

    private void sumDailyCharged(String date, Long companyId, StringBuilder sb) {
        log.info("begin execute query BiDailyEnergyCharged");
        final List<BiDailyEnergyCharged> biDailyEnergyCharged = getBiDailyEnergyCharged(date, companyId);
        log.info("query BiDailyEnergyCharged data size:{}",biDailyEnergyCharged.size());
        sb.append(" *** query BiDailyEnergyCharged size: " + biDailyEnergyCharged.size());
        if(!org.apache.commons.collections.CollectionUtils.isEmpty(biDailyEnergyCharged)){
            try {
                biDailyEnergyChargedService.saveOrUpdateBatch(biDailyEnergyCharged);
                sb.append(" insert ok;");
            } catch (Exception e) {
                sb.append(" insert error:"+ e.getMessage());
            }
        }
    }

    private void sumDailyCategoryUsage(String date, Long companyId, StringBuilder sb) {
        log.info("begin execute query biDailyCategoryUsage");
        final List<BiDailyCategoryUsage> biDailyCategoryUsage = getBiDailyCategoryUsage(date, companyId);
        log.info("query BiDailyCategoryUsage data size:{}",biDailyCategoryUsage.size());
        sb.append(" *** query BiDailyCategoryUsage size: " + biDailyCategoryUsage.size());
        if(!org.apache.commons.collections.CollectionUtils.isEmpty(biDailyCategoryUsage)){
            try {
                biDailyCategoryUsageService.saveOrUpdateBatch(biDailyCategoryUsage);
                sb.append(" insert ok;");
            } catch (Exception e) {
                sb.append(" insert error:"+ e.getMessage());
            }
        }
    }

    private void sumDailyBatteryUsage(String date, Long companyId, StringBuilder sb) {
        log.info("begin execute query BiDailyBatteryUsage,date:"+ date +" companyId:"+ companyId);
        final List<BiDailyBatteryUsage> biDailyBatteryUsage = getBiDailyBatteryUsage(date, companyId);
        log.info("query BiDailyBatteryUsage data size:{}",biDailyBatteryUsage.size());
        sb.append("*** query biDailyBatteryUsage size: "+biDailyBatteryUsage.size());
        if(!CollectionUtils.isEmpty(biDailyBatteryUsage)){
            try {
                biDailyBatteryUsageService.saveOrUpdateBatch(biDailyBatteryUsage);
                sb.append(" insert ok;");
            } catch (Exception e) {
                sb.append(" insert error:"+ e.getMessage());
            }
        }
    }
}
