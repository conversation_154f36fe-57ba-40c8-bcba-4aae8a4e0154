package com.chervon.fleet.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备故障列表（看板：Error List）(t_bi_device_error_list)数据Mapper
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Mapper
public interface BiDeviceErrorListMapper extends BaseMapper<BiDeviceErrorList> {

    /**
     * 根据companyId获取错误数量以及最近一条错误的设备名称
     *
     * @param companyId 公司ID
     * @return ChargingStatusDashboardVo, 但是只赋值errors, latestErrorDevice两个字段
     */
    String selectLatestErrorDevice(@Param("companyId") Long companyId,@Param("listDeviceId") List<String> listDeiceId);

    Integer countErrorNumber(@Param("companyId") Long companyId,@Param("listDeviceId") List<String> listDeiceId);
}
