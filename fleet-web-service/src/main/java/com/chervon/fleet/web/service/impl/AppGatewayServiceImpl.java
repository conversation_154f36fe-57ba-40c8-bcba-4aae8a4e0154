package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.api.service.RemoteUserService;
import com.chervon.fleet.web.api.entity.dto.GatewayChangeDto;
import com.chervon.fleet.web.api.entity.enums.ChangeTypeEnum;
import com.chervon.fleet.web.api.entity.enums.GatewayTypeEnum;
import com.chervon.fleet.web.api.entity.error.GatewayErrorCodeEnum;
import com.chervon.fleet.web.api.entity.query.GatewayQuery;
import com.chervon.fleet.web.api.entity.vo.GatewayVo;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.dto.GatewayCache;
import com.chervon.fleet.web.entity.dto.GatewayPageDto;
import com.chervon.fleet.web.entity.enums.CommonStatusEnum;
import com.chervon.fleet.web.entity.po.AppGateway;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.mapper.AppGatewayMapper;
import com.chervon.fleet.web.service.AppGatewayService;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.RuleEngineService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.vo.CommonDeviceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * App网关相关操作
 *
 * <AUTHOR>
 * @date 2023/7/3 11:51
 */
@Service
@Slf4j
public class AppGatewayServiceImpl extends ServiceImpl<AppGatewayMapper, AppGateway> implements AppGatewayService {

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @Autowired
    private RuleEngineService ruleEngineService;

    @Autowired
    private CompanyDeviceService companyDeviceService;

    /**
     * * 查询网关列表
     *
     * @param query
     * @return
     */
    @Override
    public List<AppGateway> getList(GatewayQuery query) {
        LambdaQueryWrapper<AppGateway> queryWrapper = getWrapper(query);
        return list(queryWrapper);
    }

    /**
     * * 查询网关详情
     *
     * @param query
     * @return
     */
    @Override
    public AppGateway get(GatewayQuery query) {
        final List<AppGateway> list = getList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * * 查询网关详情
     *
     * @param gatewayId
     * @return
     */
    @Override
    public GatewayCache getByCache(String gatewayId) {
        Assert.hasText(gatewayId, ErrorCode.PARAMETER_NOT_PROVIDED, "gatewayId");
        final String gatewayKey = RedisConst.getGatewayKey(gatewayId);
        final Object objGatewayCache = RedisUtils.getCacheObject(gatewayKey);
        if (Objects.isNull(objGatewayCache)) {
            final AppGateway appGateway = get(new GatewayQuery().setGatewayId(gatewayId));
            if (appGateway != null) {
                final GatewayCache gatewayCache = BeanCopyUtils.copy(appGateway, GatewayCache.class);
                RedisUtils.setWithExpire(gatewayKey, gatewayCache, RedisConst.GATEWAY_INFO_EXPIRE_SECOND);
                return gatewayCache;
            }
            return null;
        }
        return (GatewayCache) objGatewayCache;
    }

    /**
     * * 拼接综合查询条件
     *
     * @param query
     * @return
     */
    private LambdaQueryWrapper getWrapper(GatewayQuery query) {
        LambdaQueryWrapper<AppGateway> queryWrapper = new QueryWrapper<AppGateway>().lambda();
        queryWrapper.eq(AppGateway::getActive, 1)
                .eq(query.getGatewayId() != null, AppGateway::getId, query.getGatewayId())
                .in(!CollectionUtils.isEmpty(query.getListGatewayId()), AppGateway::getId, query.getListGatewayId())
                .eq(query.getCompanyId() != null, AppGateway::getCompanyId, query.getCompanyId())
                .eq(query.getDeviceId() != null, AppGateway::getDeviceId, query.getDeviceId())
                .eq(query.getUniqueId() != null, AppGateway::getUniqueId, query.getUniqueId())
                .eq(query.getOnlineStatus() != null, AppGateway::getOnlineStatus, query.getOnlineStatus())
                .eq(query.getGatewayType() != null, AppGateway::getGatewayType, query.getGatewayType());
        return queryWrapper;
    }

    @Override
    public PageResult<GatewayVo> listByType(GatewayPageDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "gatewayPageDto");
        Assert.notNull(req.getType(), ErrorCode.PARAMETER_NOT_PROVIDED, "type");
        //参数type：1 charger gateway 2 app gateway-fix 3 app gateway-mobile
        if (req.getType().equals(NumberConst.TYPE_CHARGER_GATEWAY)) {
            return listHard(companyId, req.getPageNum(), req.getPageSize());
        } else {
            return listSoft(req.getType() - 1, companyId, req.getPageNum(), req.getPageSize());
        }
    }

    private PageResult<GatewayVo> listSoft(Integer gatewayType, Long companyId, Integer pageNum, Integer pageSize) {
        IPage<AppGateway> page = this.page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId).eq(AppGateway::getType, GatewayTypeEnum.SOFT.getType())
                .eq(AppGateway::getGatewayType, gatewayType).orderByDesc(AppGateway::getCreateTime));
        PageResult<GatewayVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<Long> userIds = page.getRecords().stream().map(AppGateway::getUserId).distinct().collect(Collectors.toList());
        UserQuery query = new UserQuery();
        query.setIdList(userIds);
        List<UserVo> users = remoteUserService.getList(query);
        Map<Long, String> idEmailMap = users.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getEmail));
        List<GatewayVo> collect = page.getRecords().stream().map(e -> {
            return getGatewayVo(companyId, e, idEmailMap);
        }).collect(Collectors.toList());
        res.setList(collect);
        return res;
    }

    @NotNull
    private static GatewayVo getGatewayVo(Long companyId, AppGateway e, Map<Long, String> idEmailMap) {
        GatewayVo vo = new GatewayVo();
        vo.setGatewayId(e.getId()).setGatewayName(e.getName()).setOnlineStatus(e.getOnlineStatus())
                .setDeviceBrand(e.getDeviceBrand()).setDeviceSysVersion(e.getDeviceSysVersion()).setDeviceModel(e.getDeviceModel())
                .setAppVersion(e.getAppVersion()).setUniqueId(e.getUniqueId()).setLastConnectedTime(e.getLastConnectedTime())
                .setLocation(e.getLocation()).setEmail(idEmailMap.get(e.getUserId())).setCoordinate(e.getCoordinate());
        String companyGatewayKey = RedisConst.getCompanyGatewayMapKey(companyId, e.getId());
        Map<String, Object> cacheMap = RedisUtils.getCacheMap(companyGatewayKey);
        if (cacheMap == null) {
            vo.setConnectedDeviceNumber(0);
        } else {
            vo.setConnectedDeviceNumber(cacheMap.size());
        }
        return vo;
    }

    private PageResult<GatewayVo> listHard(Long companyId, Integer pageNum, Integer pageSize) {
        IPage<AppGateway> page = this.page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId).eq(AppGateway::getType, GatewayTypeEnum.HARD.getType()).eq(AppGateway::getActive, 1).orderByDesc(AppGateway::getCreateTime));
        PageResult<GatewayVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<String> deviceIds = page.getRecords().stream().map(AppGateway::getDeviceId).distinct().collect(Collectors.toList());
        List<CommonDeviceVo> deviceList = remoteDeviceManageService.batchDevice(deviceIds);
        Map<String, CommonDeviceVo> deviceMap = deviceList.stream().collect(Collectors.toMap(CommonDeviceVo::getDeviceId, Function.identity()));
        List<Long> productIds = deviceList.stream().map(CommonDeviceVo::getProductId).distinct().collect(Collectors.toList());
        List<ProductCache> products = remoteOperationCacheService.listProducts(productIds);
        Map<Long, String> idModelMap = products.stream().collect(Collectors.toMap(ProductCache::getId, ProductCache::getCommodityModel));
        List<GatewayVo> collect = page.getRecords().stream().map(e -> {
            return getGatewayVo(companyId, e, deviceMap, idModelMap);
        }).collect(Collectors.toList());
        res.setList(collect);
        return res;
    }

    @NotNull
    private static GatewayVo getGatewayVo(Long companyId, AppGateway e, Map<String, CommonDeviceVo> deviceMap, Map<Long, String> idModelMap) {
        GatewayVo vo = new GatewayVo();
        CommonDeviceVo device = deviceMap.getOrDefault(e.getDeviceId(), new CommonDeviceVo());
        vo.setGatewayId(e.getId()).setGatewayName(e.getName()).setOnlineStatus(e.getOnlineStatus())
                .setLastConnectedTime(e.getLastConnectedTime()).setLocation(e.getLocation())
                .setCommodityModel(idModelMap.get(device.getProductId()))
                .setSn(device.getSn()).setTechnologyVersion(device.getTechnologyVersion()).setCoordinate(e.getCoordinate());
        String companyGatewayKey = RedisConst.getCompanyGatewayMapKey(companyId, e.getId());
        Map<String, Object> cacheMap = RedisUtils.getCacheMap(companyGatewayKey);
        if (cacheMap == null) {
            vo.setSubDeviceNumber(0);
        } else {
            vo.setSubDeviceNumber(cacheMap.size());
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeGateway(String gatewayId) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(gatewayId, ErrorCode.PARAMETER_NOT_PROVIDED, "gatewayId");
        Assert.notNull(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        AppGateway gateway = this.getOne(new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getCompanyId, companyId)
                .eq(AppGateway::getId, gatewayId));
        if (gateway == null) {
            throw new ServiceException(GatewayErrorCodeEnum.GATEWAY_NOT_EXIST);
        }
        // 更新缓存
        ruleEngineService.addRemoveGatewayDataProcess(new GatewayChangeDto()
                .setGatewayId(gatewayId)
                .setCompanyId(gateway.getCompanyId())
                .setStatus(ChangeTypeEnum.DELETED.getType()));
        // 租户设备表修改网关为null，设备状态为未知
        companyDeviceService.update(new CompanyDevice(), new LambdaUpdateWrapper<CompanyDevice>()
                .set(CompanyDevice::getGatewayId, null)
                .eq(CompanyDevice::getGatewayId, gatewayId));
        // 移除网关
        // 如果是硬件网关则不删除
        if (gateway.getType() != GatewayTypeEnum.HARD.getType()) {
            this.removeById(gatewayId);
        } else {
            this.update(new AppGateway(), new LambdaUpdateWrapper<AppGateway>()
                    .set(AppGateway::getActive, 0)
                    .eq(AppGateway::getId, gatewayId));
        }
    }
}
