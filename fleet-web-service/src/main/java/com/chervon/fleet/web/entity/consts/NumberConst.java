package com.chervon.fleet.web.entity.consts;

/**
 * 数字常量类
 * <AUTHOR> 2022/10/15
 */
public class NumberConst {
    /**
     * 12小时毫秒
     */
    public static final Long TIMESPAN_12_HOURS_MS = 12*3600*1000L;
    /**
     * 12小时（秒）
     */
    public static final Long TIMESPAN_12_HOURS_SECOND = 12*3600L;

    /**
     * 一小时秒
     */
    public static final Integer ONE_HOUR_SECOND=3600;
    /**
     * 一分钟毫秒
     */
    public static final Long ONE_MINUTE_MS = 60 * 1000L;
    /**
     * 一分钟秒
     */
    public static final Integer ONE_MINUTE_SECOND = 60;
    /**
     * 二分钟毫秒
     */
    public static final Long TWO_MINUTE_MS = 120 * 1000L;
    /**
     * 时区8
     */
    public static final Integer TIME_ZONE_8 = 8;
    /**
     * 百分比
     */
    public static final Integer PERCENTAGE = 100;
    /**
     * 数字30
     */
    public static final Integer THIRTY = 30;
    public static final Integer SIXTY = 60;
    /**
     * 数字180
     */
    public static final Integer HUNDRED_EIGHTY = 180;
    /**
     * 数字40
     */
    public static final Integer FORTY = 40;
    /**
     * 数字100
     */
    public static final Integer ONE_HUNDRED = 100;
    //维保状态，指绑定到fleet的所有设备，具体按一级归纳品类（tool，battery），charger不适用  tool默认时长，50小时  battery默认日期，一年
    public static final Integer MAINTENANCE_DEFAULT_HOURS = 50;

    //参数type：1 charger gateway 2 app gateway-fix 3 app gateway-mobile
    public static final Integer TYPE_CHARGER_GATEWAY = 1;

}
