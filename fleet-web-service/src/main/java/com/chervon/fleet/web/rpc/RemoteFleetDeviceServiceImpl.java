package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyVo;
import com.chervon.fleet.user.api.service.RemoteFleetUserCenterService;
import com.chervon.fleet.web.api.entity.consts.IotConstant;
import com.chervon.fleet.web.api.entity.dto.*;
import com.chervon.fleet.web.api.entity.enums.*;
import com.chervon.fleet.web.api.entity.error.DeviceErrorCodeEnum;
import com.chervon.fleet.web.api.entity.vo.*;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.po.*;
import com.chervon.fleet.web.service.*;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import com.chervon.operation.api.vo.FleetProductCategoryVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import com.chervon.technology.api.RemoteDeviceManageService;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.enums.DeviceStatusEnum;
import com.chervon.technology.api.vo.DeviceBindBo;
import com.chervon.technology.api.vo.DeviceDetailInfoRpcVo;
import com.chervon.technology.api.vo.ProductRpcVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 远程设备服务实现
 * <AUTHOR>
 * @date 2023/7/3 15:34
 */
@Service
@DubboService
@Slf4j
public class RemoteFleetDeviceServiceImpl implements RemoteFleetDeviceService {

    @Autowired
    private CompanyDeviceService companyDeviceService;

    @Autowired
    private AppCompanyUserDeviceAsyncService appCompanyUserDeviceAsyncService;

    @Autowired
    private DeviceBindLogService deviceBindLogService;

    @Autowired
    private RuleEngineService ruleEngineService;
    @Autowired
    private DeviceTagService deviceTagService;
    @Autowired
    private EquipmentService equipmentService;
    @DubboReference
    private RemoteDeviceManageService remoteDeviceManageService;
    @DubboReference
    private RemoteProductService remoteProductService;
    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    @Autowired
    private MaintenancePlanService maintenancePlanService;

    @Autowired
    private ShadowStatusService shadowStatusService;

    @Autowired
    private AppGatewayService appGatewayService;

    @DubboReference
    private RemoteFleetUserCenterService remoteFleetUserCenterService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bind(BaseDeviceDto req) {
        Long companyId = UserContext.getCompanyId();
        Long userId = UserContext.getUserId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "baseDeviceDto");
        if (org.springframework.util.StringUtils.hasText(req.getDeviceId()) || StringUtils.hasText(req.getDeviceSn())) {
            DeviceBindBo deviceBindBo;
            if (StringUtils.hasText(req.getDeviceId())) {
                deviceBindBo = remoteDeviceManageService.bindByDeviceId(req.getDeviceId(), BusinessTypeEnum.FLEET.getType(), userId, companyId);
            } else {
                deviceBindBo = remoteDeviceManageService.bindBySn(req.getDeviceSn(), BusinessTypeEnum.FLEET.getType(), userId, companyId);
            }
            if (deviceBindBo.getResult() == BindingResultEnum.DEVICE_BOUND_OTHER_APP.getType()) {
                throw new ServiceException(DeviceErrorCodeEnum.DEVICE_BOUND_OTHER_APP);
            }
            if (deviceBindBo.getResult() == BindingResultEnum.DEVICE_BOUND_OTHER_COMPANY.getType()) {
                throw new ServiceException(DeviceErrorCodeEnum.DEVICE_BOUND_OTHER_COMPANY);
            }//管理后台设备已停用场景，不可以绑定
            if (deviceBindBo.getResult() == BindingResultEnum.DEVICE_NOT_ENABLED.getType()) {
                throw new ServiceException(DeviceErrorCodeEnum.DEVICE_NOT_ENABLED);
            }
            appBind(deviceBindBo, req.getMac());
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId and deviceSn");
        }
    }

    /**
     * 绑定设备
     * @param deviceBindBo
     * @param mac
     */
    private void appBind(DeviceBindBo deviceBindBo, String mac) {
        Long companyId = UserContext.getCompanyId();
        Long userId = UserContext.getUserId();
        // 查看是否已绑定
        LambdaQueryWrapper<CompanyDevice> wrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getDeviceId, deviceBindBo.getDeviceId());
        if (companyDeviceService.count(wrapper) > 0) {
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_BOUND_OTHER_COMPANY);
        }
        //查询设备分类信息
        List<FleetProductCategoryVo> productCategoryList = remoteFleetCategoryService.listProductCategory(Collections.singletonList(deviceBindBo.getCommodityModel()));
        FleetProductCategoryVo productCategory = productCategoryList.stream()
                .filter(e -> deviceBindBo.getCommodityModel() != null && deviceBindBo.getCommodityModel().equals(e.getProductModel())).findFirst().orElse(null);
        if(Objects.isNull(productCategory)){
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_NOT_FOUND_CATEGORY);
        }
        //构建租户设备基础信息
        CompanyDevice companyDevice = new CompanyDevice();
        buildCompanyDevice(deviceBindBo, mac, companyDevice, companyId, userId, productCategory);

        // 设置维保
        // 维保状态，指绑定到fleet的所有设备，具体按一级归纳品类（tool，battery），charger不适用  tool默认时长，50小时  battery默认日期，一年
        if (productCategory.getCustomType() != null) {
            if (productCategory.getCustomType() == BizCategoryTypeEnum.TOOL.getType()) {
                saveToolMaintenancePlan(deviceBindBo, companyId, productCategory, companyDevice);
            } else if (productCategory.getCustomType().intValue() == BizCategoryTypeEnum.BATTERY.getType()) {
                saveBatteryMantenancePlan(deviceBindBo, companyId, productCategory, companyDevice);
            }else{
                companyDevice.setMaintenanceStatus(MaintenanceStatusEnum.OFF.getType());
            }
        }
        // 如果设备不是网关设备，默认库存状态 0 never seen
        if (!StringConst.GATEWAY_DEVICE.equals(deviceBindBo.getProductType())) {
            companyDevice.setWarehouseStatus(WarehouseStatusEnum.NEVER_SEEN.getType());
        }
        // 如果设备有联网能力，默认在线状态为offline
        if (deviceBindBo.getNetworkModes() != null && Arrays.asList(StringConst.NETWORK_WIFI, StringConst.NETWORK_4G).contains(deviceBindBo.getNetworkModes())) {
            companyDevice.setOnlineStatus(FleetDeviceOnlineStatusEnum.OFFLINE.getType());
        }
        companyDeviceService.save(companyDevice);
        // 添加绑定记录
        saveBindLog(deviceBindBo, companyId, userId);
        // Async 回调更新device表昵称,激活用户,激活时间字段
        remoteDeviceManageService.callBackAfterEditDevice(companyDevice.getDeviceName(), deviceBindBo.getDeviceId(), companyId);
        // Async 将用户信息同步到新绑定设备
        if (!StringConst.NOT_IOT_DEVICE.equals(deviceBindBo.getProductType())) {
            appCompanyUserDeviceAsyncService.syncCompanyUserInfo2Device(companyId, userId, deviceBindBo.getDeviceId());
        }
        // 添加非网关设备刷新缓存
        if (!StringConst.GATEWAY_DEVICE.equals(deviceBindBo.getProductType())) {
            ruleEngineService.deviceBindOrUnBindProcess(new DeviceBindChangeDto()
                    .setDeviceId(deviceBindBo.getDeviceId())
                    .setCompanyId(companyDevice.getCompanyId())
                    .setStatus(ChangeTypeEnum.ADD.getType()));
        }
        // 绑定后设置设备绑定关系
        remoteDeviceManageService.setCurrentBusinessTypeAfterBind(deviceBindBo.getDeviceId(), userId, companyId, BusinessTypeEnum.FLEET.getType());
    }

    private void buildCompanyDevice(DeviceBindBo deviceBindBo, String mac, CompanyDevice companyDevice, Long companyId, Long userId, FleetProductCategoryVo productCategory) {
        companyDevice.setCompanyId(companyId)
                .setDeviceId(deviceBindBo.getDeviceId())
                .setMac(mac)
                .setDeviceSn(deviceBindBo.getSn())
                .setProductId(deviceBindBo.getProductId())
                .setBindingUserId(userId)
                .setBindingTime(System.currentTimeMillis());
        // 设置品类
        companyDevice.setFirstCategoryCode(productCategory.getFirstCategoryCode());
        companyDevice.setSecondCategoryCode(productCategory.getSecondCategoryCode());
        companyDevice.setCustomType(productCategory.getCustomType());
        //设备昵称
        String nickName = getNickName(companyId, deviceBindBo.getDeviceNickName());
        companyDevice.setDeviceName(nickName);
    }

    private void saveBindLog(DeviceBindBo deviceBindBo, Long companyId, Long userId) {
        DeviceBindLog deviceBindLog = new DeviceBindLog();
        deviceBindLog.setDeviceId(deviceBindBo.getDeviceId())
                .setCompanyId(companyId)
                .setStatus(ChangeTypeEnum.ADD.getType())
                .setBindTime(System.currentTimeMillis())
                .setBindOperator(userId);
        deviceBindLogService.save(deviceBindLog);
    }

    private void saveBatteryMantenancePlan(DeviceBindBo deviceBindBo, Long companyId, FleetProductCategoryVo productCategory, CompanyDevice companyDevice) {
        MaintenancePlan plan = new MaintenancePlan();
        plan.setPlanType(MaintenancePlanTypeEnum.DATE.getType());
        plan.setPlanBegin(System.currentTimeMillis());
        plan.setDeviceId(deviceBindBo.getDeviceId());
        plan.setCompanyId(companyId);
        plan.setFirstCategoryCode(productCategory.getFirstCategoryCode());
        // 日期不变，时间到23时59分59秒
        LocalDateTime deadline = LocalDateTime.of(LocalDateTime.now().plusYears(1).minusDays(1).toLocalDate(), LocalTime.MAX);
        plan.setDeadlineTime(deadline.toInstant(ZoneOffset.UTC).toEpochMilli());
        maintenancePlanService.save(plan);
        companyDevice.setMaintenanceStatus(MaintenanceStatusEnum.ON.getType());
    }

    private void saveToolMaintenancePlan(DeviceBindBo deviceBindBo, Long companyId, FleetProductCategoryVo productCategory, CompanyDevice companyDevice) {
        MaintenancePlan plan = new MaintenancePlan();
        plan.setPlanType(MaintenancePlanTypeEnum.ENGINE_HOURS.getType());
        plan.setPlanBegin(System.currentTimeMillis());
        plan.setDeviceId(deviceBindBo.getDeviceId());
        plan.setCompanyId(companyId);
        plan.setFirstCategoryCode(productCategory.getFirstCategoryCode());
        plan.setUsageHour(NumberConst.MAINTENANCE_DEFAULT_HOURS);
        // 设置此刻的使用时间，单位秒
        ShadowStatus shadowStatus = shadowStatusService.getByDeviceId(deviceBindBo.getDeviceId(), companyId);
        if (shadowStatus != null && shadowStatus.getTotalUsageTime() != null) {
            plan.setWorkHoursUsed(shadowStatus.getTotalUsageTime());
        } else {
            plan.setWorkHoursUsed(0);
        }
        maintenancePlanService.save(plan);
        companyDevice.setMaintenanceStatus(MaintenanceStatusEnum.ON.getType());
    }

    /**
     * 获取设备昵称
     *
     * @param companyId  租户ID
     * @param deviceName 设备昵称
     * @return 设备昵称
     */
    private String getNickName(Long companyId, String deviceName) {
        // Redisson锁控制相同 UserId_Model 则不允许10秒内重复绑定
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(companyId + "_" + deviceName);
        if (!lock.isLocked()) {
            lock.lock(CommonConstant.TEN, TimeUnit.SECONDS);
            List<CompanyDevice> data = Optional.ofNullable(companyDeviceService.selectMaxOrder(companyId, deviceName)).orElse(new ArrayList<>());
            List<String> existNickNames = data.stream().map(CompanyDevice::getDeviceName).filter(Objects::nonNull).collect(Collectors.toList());
            String nickName;
            if (!existNickNames.contains(deviceName)) {
                nickName = deviceName;
            } else {
                existNickNames.remove(deviceName);
                if (existNickNames.isEmpty()) {
                    nickName = deviceName + "(2)";
                } else {
                    Integer count = existNickNames.stream()
                            .map(e -> Integer.parseInt(e.split("\\(")[1].split("\\)")[0])).max(Integer::compareTo).orElse(0);
                    nickName = deviceName + "(" + (count + 1) + ")";
                }
            }
            lock.unlock();
            return nickName;
        } else {
            throw new ServiceException(ErrorCode.BUSINESS_OPERATE_WAITING);
        }
    }

    @Override
    public DeviceInfoVo detail(BaseDeviceDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "baseDeviceDto");
        DeviceInfoVo res = new DeviceInfoVo();
        if (StringUtils.hasText(req.getDeviceId()) || StringUtils.hasText(req.getDeviceSn())) {
            LambdaQueryWrapper<CompanyDevice> wrapper = new LambdaQueryWrapper<CompanyDevice>().eq(CompanyDevice::getCompanyId, companyId)
                    .eq(StringUtils.hasText(req.getDeviceId()), CompanyDevice::getDeviceId, req.getDeviceId())
                    .eq(StringUtils.hasText(req.getDeviceSn()), CompanyDevice::getDeviceSn, req.getDeviceSn());
            CompanyDevice companyDevice = companyDeviceService.getOne(wrapper);
            if (companyDevice == null) {
                return res;
            }
            res.setDeviceId(companyDevice.getDeviceId());
            res.setDeviceName(companyDevice.getDeviceName());
            ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(companyDevice.getDeviceSn());
            if (productRpcVo != null) {
                res.setProductId(productRpcVo.getId());
                ProductCache product = remoteOperationCacheService.getProduct(productRpcVo.getId());
                if (product != null) {
                    res.setCommodityModel(product.getCommodityModel());
                    res.setProductIconUrl(product.getUrl());
                }
            }
            return res;
        } else {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId and deviceSn");
        }
    }

    @Override
    public RnDetailVo getRnDetail(RnDetailRequest req) throws ServiceException {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "baseDeviceDto");
        RnDetailVo res = new RnDetailVo();
        res.setDeviceId(req.getDeviceId());
        res.setPeriod(NumberConst.TIMESPAN_12_HOURS_SECOND);
        LambdaQueryWrapper<CompanyDevice> wrapper = new LambdaQueryWrapper<CompanyDevice>().eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getDeviceId, req.getDeviceId());
        CompanyDevice companyDevice = companyDeviceService.getOne(wrapper);
        if (Objects.isNull(companyDevice)) {
            return res;
        }
        res.setDeviceName(companyDevice.getDeviceName());
        res.setMac(companyDevice.getMac());
        res.setSn(companyDevice.getDeviceSn());
        res.setBindTime(companyDevice.getBindingTime());
        res.setDeviceName(companyDevice.getDeviceName());
        res.setIsOnline(companyDevice.getOnlineStatus());
        res.setProductId(companyDevice.getProductId());
        if(!Objects.isNull(companyDevice.getOfflineTime())){
            long days=(System.currentTimeMillis()-companyDevice.getOfflineTime())/86400000L;
            res.setOfflineDays(days);
        }

        ProductCache product = remoteOperationCacheService.getProduct(companyDevice.getProductId());
        if (product != null) {
            res.setCommodityModel(product.getCommodityModel());
            res.setDeviceIcon(product.getUrl());
        }
        LastRnPackageDto rnPackageRequest = new LastRnPackageDto();
        rnPackageRequest.setAppType(req.getAppType());
        rnPackageRequest.setAppVersion(req.getAppVersion());
        // 设置rn适用app类型为fleet
        rnPackageRequest.setBusinessType(req.getBusinessType());
        DeviceDetailInfoRpcVo deviceRpcVo = remoteDeviceManageService.getDeviceDetailByDeviceId(req.getDeviceId(), rnPackageRequest, UserContext.getUserId());
        if (!Objects.isNull(deviceRpcVo)) {
            res.setAssemblySnList(deviceRpcVo.getAssemblySnList());
            res.setCommunicateMode(deviceRpcVo.getCommunicateMode());
            res.setProductType(deviceRpcVo.getProductType());
            res.setRnBundleName(deviceRpcVo.getRnBundleName());
            res.setVersion(deviceRpcVo.getVersion());
            res.setEnableStatus(deviceRpcVo.getStatus() == DeviceStatusEnum.NORMAL ? CommonConstant.ONE : CommonConstant.ZERO);
        }
        return res;
    }

    @Override
    public BasicStatusVo getDeviceStatus(BaseDeviceDto req) throws ServiceException {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "baseDeviceDto");
        Assert.notNull(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
        BasicStatusVo res = new BasicStatusVo();
        res.setDeviceId(req.getDeviceId());
        res.setIsOnline(OnlineStatusEnum.OFFLINE.getType());
        LambdaQueryWrapper<CompanyDevice> wrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getDeviceId, req.getDeviceId())
                .select(CompanyDevice::getDeviceId, CompanyDevice::getOnlineStatus);
        CompanyDevice companyDevice = companyDeviceService.getOne(wrapper);
        if (!Objects.isNull(companyDevice)) {
            res.setDeviceId(companyDevice.getDeviceId());
            res.setIsOnline(companyDevice.getOnlineStatus());
        }
        return res;
    }

    @Override
    public void editName(DeviceEditDto req) throws ServiceException {
        equipmentService.editName(req);
    }

    /**
     * 根据SN查询产品ID
     * @param sn 设备sn
     * @return
     */
    @Override
    public Long getProductIdBySn(String sn) {
        // 根据SN不同位数截取不同SN_CODE，查找对应产品类型以及产品ID
        String snCode;

        if (IotConstant.DEVICE_CODE_REG.matcher(sn).matches()) {
            snCode = sn.substring(CommonConstant.ONE, CommonConstant.FIVE);
        } else if (IotConstant.NO_IOT_CODE_REG.matcher(sn).matches()) {
            snCode = sn.substring(CommonConstant.TWO, CommonConstant.SIX);
        } else {
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_SN_CODE_ERROR);
        }
        ProductRpcVo productRpcVo = remoteProductService.getProductIdBySnCode(snCode);
        if (null == productRpcVo) {
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_SN_CANNOT_FIND_PRODUCT);
        }
        // 配网不需要多码校验
        return productRpcVo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbind(String deviceId) {
        Long companyId = UserContext.getCompanyId();
        Long userId = UserContext.getUserId();
        LambdaQueryWrapper<CompanyDevice> wrapper = new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getDeviceId, deviceId);
        CompanyDevice companyDevice = companyDeviceService.getOne(wrapper);
        if (null == companyDevice) {
            throw new ServiceException(DeviceErrorCodeEnum.DEVICE_BOUND_NOT_FOUND);
        }
        this.unbind(companyDevice, userId);
    }

    private void unbind(CompanyDevice companyDevice, Long userId) throws ServiceException {
        Long companyId = companyDevice.getCompanyId();
        String deviceId = companyDevice.getDeviceId();
        // 删除租户设备关系表
        companyDeviceService.removeById(companyDevice.getId());
        //移除设备上报缓存信息
        final String warehouseStatusKey = RedisConst.getDeviceWarehouseStatusKey(deviceId);
        RedisUtils.deleteObject(warehouseStatusKey);
        // 变更租户绑定记录表
        deviceBindLogService.update(new DeviceBindLog(), new LambdaUpdateWrapper<DeviceBindLog>()
                .set(DeviceBindLog::getStatus, ChangeTypeEnum.DELETED.getType())
                .set(DeviceBindLog::getUnbindOperator, userId)
                .set(DeviceBindLog::getUnbindTime, System.currentTimeMillis())
                .eq(DeviceBindLog::getCompanyId, companyId)
                .eq(DeviceBindLog::getDeviceId, deviceId)
                .eq(DeviceBindLog::getStatus, ChangeTypeEnum.ADD.getType()));
        // 删除租户设备标签数据
        deviceTagService.removeByDeviceIdAndCompany(deviceId, companyId);
        // 删除维保计划
        maintenancePlanService.remove(new LambdaQueryWrapper<MaintenancePlan>().eq(MaintenancePlan::getDeviceId, deviceId).eq(MaintenancePlan::getCompanyId, companyId));
        // 如果设备是网关设备，删除该网关
        AppGateway appGateway = appGatewayService.getOne(new LambdaQueryWrapper<AppGateway>()
                .eq(AppGateway::getDeviceId, companyDevice.getDeviceId())
                .eq(AppGateway::getCompanyId, companyDevice.getCompanyId()));
        if (appGateway != null) {
            appGatewayService.removeGateway(appGateway.getId());
        }
        // 变更技术平台的设备记录表
        remoteDeviceManageService.setCurrentBusinessTypeAfterUnbind(deviceId);
        // 处理数据链路上报缓存的设备的信息及发布mqtt主题通知android端刷新设备库存列表
        // 非网关设备刷新缓存
        ProductCache product = remoteOperationCacheService.getProduct(companyDevice.getProductId());
        if (product == null || !StringConst.GATEWAY_DEVICE.equals(product.getType())) {
            ruleEngineService.deviceBindOrUnBindProcess(new DeviceBindChangeDto()
                    .setDeviceId(deviceId)
                    .setCompanyId(companyDevice.getCompanyId())
                    .setStatus(ChangeTypeEnum.DELETED.getType()));
        }
    }

    @Override
    @Async
    public void unbindByCompanyId(Long companyId) {
        List<CompanyDevice> list = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>().eq(CompanyDevice::getCompanyId, companyId));
        for (CompanyDevice e : list) {
            if (StringUtils.hasText(e.getDeviceId())) {
                try {
                    this.unbind(e, null);
                } catch (NullPointerException ex) {
                    log.error("unbind device NullPointerException error,deviceId:{}", e.getDeviceId());
                } catch (IllegalArgumentException ex) {
                    log.error("IllegalArgumentException in unbind device ", ex);
                } catch (Exception ex) {
                    log.error("unbind device error,deviceId:{}", e.getDeviceId());
                }
            }
        }
    }

    @Override
    public List<CompanyDeviceVo> batchCompanyDeviceByCompanyId(List<Long> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return new ArrayList<>();
        }
        List<CompanyDevice> list = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>().in(CompanyDevice::getCompanyId, companyIds));
        return list.stream().map(e -> {
            CompanyDeviceVo vo = new CompanyDeviceVo();
            vo.setDeviceId(e.getDeviceId()).setCompanyId(e.getCompanyId());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 硬网关在线离线更新状态处理
     * @param deviceId     设备id
     * @param onlineStatus 设备在线状态 1 在线 0 离线
     */
    @Override
    public void updateDeviceOnlineStatus(String deviceId, Integer onlineStatus) {
        if (StringUtils.isEmpty(deviceId) || Objects.isNull(onlineStatus) || !Arrays.asList(0, 1).contains(onlineStatus)) {
            return;
        }
        LambdaUpdateWrapper<CompanyDevice> updateWrapper = new LambdaUpdateWrapper<CompanyDevice>()
                .set(CompanyDevice::getOnlineStatus, onlineStatus)
                .eq(CompanyDevice::getDeviceId, deviceId)
                .eq(CompanyDevice::getFirstCategoryCode, FleetFirstCategoryEnum.CHARGER.getCategoryCode());
        if(OnlineStatusEnum.ONLINE.getType()==onlineStatus.intValue()){
            updateWrapper.set(CompanyDevice::getOnlineTime,System.currentTimeMillis());
        }else{
            updateWrapper.set(CompanyDevice::getOfflineTime,System.currentTimeMillis());
        }
        companyDeviceService.update(updateWrapper);
        // 如果设备是网关设备，更新网关在线状态
        appGatewayService.update(new AppGateway(), new LambdaUpdateWrapper<AppGateway>()
                .set(AppGateway::getOnlineStatus, onlineStatus)
                .eq(AppGateway::getId, deviceId));
        // 给client通知在线状态
        try {
            ruleEngineService.publishMsgDeviceOnlineStatusChange(deviceId, onlineStatus);
        } catch (NullPointerException e) {
            log.error("发布mqtt消息通知android端刷新设备在线离线状态发生NullPointerException异常", e);
        } catch (IllegalArgumentException e) {
            log.error("发布mqtt消息通知android端刷新设备在线离线状态发生IllegalArgumentException异常", e);
        } catch (Exception e) {
            log.error("发布mqtt消息通知android端刷新设备在线离线状态发生异常", e);
        }
    }

    @Override
    public int batchUpdateCompanyDeviceInfo(List<CompanyDeviceEditDto> listEditDto) throws ServiceException {
        if (listEditDto == null || listEditDto.isEmpty()) {
            return 0;
        }
        int success=0;
        for (CompanyDeviceEditDto companyDeviceEditDto : listEditDto) {
            if (updateCompanyDeviceInfo(companyDeviceEditDto)) {
                success++;
            }
        }
        return success;
    }

    @Override
    public boolean updateCompanyDeviceInfo(CompanyDeviceEditDto companyDeviceEditDto) throws ServiceException {
        if (companyDeviceEditDto == null || companyDeviceEditDto.getDeviceId() == null) {
            return false;
        }
        LambdaUpdateWrapper<CompanyDevice> updateWrapper = new LambdaUpdateWrapper<CompanyDevice>()
                .eq(CompanyDevice::getDeviceId, companyDeviceEditDto.getDeviceId())
                .set(CompanyDevice::getModifier, companyDeviceEditDto.getModifier())
                .set(CompanyDevice::getModifyTime, System.currentTimeMillis());
        if (companyDeviceEditDto.getDeviceSn() != null) {
            updateWrapper.set(CompanyDevice::getDeviceSn, companyDeviceEditDto.getDeviceSn());
        }
        if (companyDeviceEditDto.getDeviceName() != null) {
            updateWrapper.set(CompanyDevice::getDeviceName, companyDeviceEditDto.getDeviceName());
        }
        if (companyDeviceEditDto.getProductId() != null) {
            updateWrapper.set(CompanyDevice::getProductId, companyDeviceEditDto.getProductId());
        }
        try {
            final boolean update = companyDeviceService.update(updateWrapper);
            return update;
        }catch (NullPointerException e) {
            log.error("NullPointerException in process ", e);
            return false;
        } catch (IllegalArgumentException e) {
            log.error("IllegalArgumentException in process ", e);
            return false;
        }
        catch (Exception e) {
            log.error("管理平台添加设备同步fleet平台设备信息失败：{}", e);
            return false;
        }
    }

    @Override
    public void updateDeviceReportPowerAndTimestamp(DeviceShadowDataDto req) {

    }

    @Override
    public List<FleetCompanyDeviceVo> companyDeviceList(FleetCompanyDevicePageDto req) throws ServiceException {
        List<FleetCompanyVo> companies = remoteFleetUserCenterService.companyList(req.getCompanyId(), req.getCompanyName());
        if (CollectionUtils.isEmpty(companies)) {
            return new ArrayList<>();
        }
        List<Long> companyIds = companies.stream().map(FleetCompanyVo::getCompanyId).distinct().collect(Collectors.toList());
        List<CompanyDevice> companyDevices = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .in(CompanyDevice::getCompanyId, companyIds));

        Map<Long, List<CompanyDevice>> map = companyDevices.stream().collect(Collectors.groupingBy(CompanyDevice::getCompanyId));

        List<ProductCache> productCacheList = remoteOperationCacheService.listProducts(companyDevices.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCacheList.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));

        List<FleetCompanyDeviceVo> list = new ArrayList<>();
        for(FleetCompanyVo e : companies) {
            FleetCompanyDeviceVo vo = new FleetCompanyDeviceVo();
            vo.setCompanyId(e.getCompanyId());
            vo.setCompanyName(e.getCompanyName());
            List<CompanyDevice> devices = map.get(e.getCompanyId());
            if (!CollectionUtils.isEmpty(devices)) {
                boolean deviceIdMatch = true;
                deviceIdMatch = isDeviceIdMatch(req, deviceIdMatch, devices);
                boolean deviceSnMatch = true;
                deviceSnMatch = isDeviceSnMatch(req, deviceSnMatch, devices);
                boolean categoryMatch = true;
                categoryMatch = isCategoryMatch(req, categoryMatch, devices);

                final boolean brandIdMatch = isBrandIdMatch(req, devices, productCacheMap);

                final boolean modelMatch = isModelMatch(req, devices, productCacheMap);

                buildList(deviceIdMatch, deviceSnMatch, categoryMatch, brandIdMatch, modelMatch, devices, productCacheMap, vo, list);
            } else if (isaBoolean(req)) {
                list.add(vo);
            }else{
                continue;
            }
        }
        return list;
    }

    private static boolean isaBoolean(FleetCompanyDevicePageDto req) {
        return StringUtils.isEmpty(req.getDeviceId()) && StringUtils.isEmpty(req.getDeviceSn()) && StringUtils.isEmpty(req.getCategoryCode()) && req.getBrandId() == null && StringUtils.isEmpty(req.getModel());
    }

    private static boolean isCategoryMatch(FleetCompanyDevicePageDto req, boolean categoryMatch, List<CompanyDevice> devices) {
        if (StringUtils.hasText(req.getCategoryCode())) {
            categoryMatch = devices.stream().anyMatch(i -> !Objects.isNull(i.getSecondCategoryCode()) && i.getSecondCategoryCode().equalsIgnoreCase(req.getCategoryCode()));
        }
        return categoryMatch;
    }

    private static boolean isDeviceSnMatch(FleetCompanyDevicePageDto req, boolean deviceSnMatch, List<CompanyDevice> devices) {
        if (StringUtils.hasText(req.getDeviceSn())) {
            deviceSnMatch = devices.stream().anyMatch(i ->!Objects.isNull(i.getDeviceSn()) && i.getDeviceSn().equalsIgnoreCase(req.getDeviceSn()));
        }
        return deviceSnMatch;
    }

    private static boolean isDeviceIdMatch(FleetCompanyDevicePageDto req, boolean deviceIdMatch, List<CompanyDevice> devices) {
        if (StringUtils.hasText(req.getDeviceId())) {
            deviceIdMatch = devices.stream().anyMatch(i ->!Objects.isNull(i.getDeviceId()) && i.getDeviceId().equalsIgnoreCase(req.getDeviceId()));
        }
        return deviceIdMatch;
    }

    private static boolean isModelMatch(FleetCompanyDevicePageDto req, List<CompanyDevice> devices, Map<Long, ProductCache> productCacheMap) {
        boolean modelMatch = true;
        if (StringUtils.hasText(req.getModel())) {
            modelMatch = devices.stream().anyMatch(i -> {
                if (i.getProductId() == null) {
                    return false;
                }
                ProductCache productCache = productCacheMap.get(i.getProductId());
                if (productCache == null) {
                    return false;
                }
                return productCache.getCommodityModel().equalsIgnoreCase(req.getModel());
            });
        }
        return modelMatch;
    }

    private static boolean isBrandIdMatch(FleetCompanyDevicePageDto req, List<CompanyDevice> devices, Map<Long, ProductCache> productCacheMap) {
        boolean brandIdMatch = true;
        if (req.getBrandId() != null) {
            brandIdMatch = devices.stream().anyMatch(i -> {
                if (i.getProductId() == null) {
                    return false;
                }
                ProductCache productCache = productCacheMap.get(i.getProductId());
                if (productCache == null) {
                    return false;
                }
                return Objects.equals(productCache.getBrandId(), req.getBrandId());
            });
        }
        return brandIdMatch;
    }

    private static void buildList(boolean deviceIdMatch, boolean deviceSnMatch, boolean categoryMatch, boolean brandIdMatch, boolean modelMatch, List<CompanyDevice> devices, Map<Long, ProductCache> productCacheMap, FleetCompanyDeviceVo vo, List<FleetCompanyDeviceVo> list) {
        if (deviceIdMatch && deviceSnMatch && categoryMatch && brandIdMatch && modelMatch) {
            List<String> deviceIds = new ArrayList<>();
            List<String> deviceSns = new ArrayList<>();
            List<String> categoryCodes = new ArrayList<>();
            List<Long> brandIds = new ArrayList<>();
            List<String> models = new ArrayList<>();
            devices.forEach(i -> {
                deviceIds.add(i.getDeviceId());
                deviceSns.add(i.getDeviceSn());
                categoryCodes.add(i.getSecondCategoryCode());
                ProductCache productCache = productCacheMap.getOrDefault(i.getProductId(), new ProductCache());
                brandIds.add(productCache.getBrandId());
                models.add(productCache.getCommodityModel());
            });
            vo.setDeviceId(deviceIds);
            vo.setDeviceSn(deviceSns);
            vo.setCategoryCode(categoryCodes);
            vo.setBrandId(brandIds);
            vo.setModel(models);
            list.add(vo);
        }
    }


}
