package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 维保操作记录表(t_maintenance_log)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-15 19:15:52
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_maintenance_log")
public class MaintenanceLog extends Model<MaintenanceLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 维保计划id
     */
    private Long planId;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 服务类型：1 maintenance 2 inspection 3 repair
     */
    private Integer serviceType;
    /**
     * 服务时间
     */
    private Long serviceTime;
    /**
     * 运行时长
     */
    private Integer runtime;
    /**
     * 工作时长
     */
    private Integer laborTime;
    /**
     * 费用
     */
    private BigDecimal expenses;
    /**
     * 维护人
     */
    private String performedBy;
    /**
     * 维护内容
     */
    private String contents;

}