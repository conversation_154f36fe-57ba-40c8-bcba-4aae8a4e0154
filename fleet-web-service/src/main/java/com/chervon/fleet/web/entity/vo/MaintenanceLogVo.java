package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 维保日志对象
 * <AUTHOR>
 * @date 2023/7/21 11:01
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "维保日志对象")
public class MaintenanceLogVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("维保日志id")
    private Long maintenanceLogId;

    @ApiModelProperty("服务类型：1 maintenance 2 inspection 3 repair")
    private Integer serviceType;

    @ApiModelProperty("服务时间")
    private Long serviceTime;

    @ApiModelProperty("运行时长")
    private Integer runtime;

    @ApiModelProperty("工作时长")
    private Integer laborTime;

    @ApiModelProperty("费用")
    private BigDecimal expenses;

    @ApiModelProperty("维护人")
    private String performedBy;

    @ApiModelProperty("维护内容")
    private String contents;
}
