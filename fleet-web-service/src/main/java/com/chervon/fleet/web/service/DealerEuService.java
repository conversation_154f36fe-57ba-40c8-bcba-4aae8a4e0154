package com.chervon.fleet.web.service;

import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerEuVo;

import java.util.List;

public interface DealerEuService {

    /**
     * 搜索距离范围内的经销商
     *
     * @param req 查询条件
     * @return 数据列表
     */
    List<AppDealerEuVo> search(AppDealerSearchDto req);

    /**
     * 经销商详情
     *
     * @param dealerId 经销商id
     * @return 详情数据
     */
    AppDealerEuVo detail(Long dealerId);

    /**
     * 我的收藏-总数
     *
     * @param req 查询条件
     * @return 总数
     */
    Integer favoriteCount(AppDealerSearchDto req);

    /**
     * 我的收藏列表
     *
     * @param req 查询条件
     * @return 列表数据
     */
    List<AppDealerEuVo> favorite(AppDealerSearchDto req);

    /**
     * 添加收藏
     *
     * @param dealerId 经销商id
     */
    void addFavorite(Long dealerId);

    /**
     * 取消收藏
     *
     * @param dealerId 经销商id
     */
    void cancelFavorite(Long dealerId);
}
