package com.chervon.fleet.web.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/21 10:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(description = "维保日志查询对象")
public class MaintenanceLogSearchDto extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("服务类型：1 maintenance 2 inspection 3 repair")
    private Integer serviceType;

    @ApiModelProperty("维保时间-开始")
    private Long serviceTimeStart;

    @ApiModelProperty("维保时间-结束")
    private Long serviceTimeEnd;

}
