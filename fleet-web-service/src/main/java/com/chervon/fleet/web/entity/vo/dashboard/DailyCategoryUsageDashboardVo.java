package com.chervon.fleet.web.entity.vo.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备分类日使用量看板Vo
 * <AUTHOR>
 * @since 2023-07-27 17:18
 **/
@Data
@Accessors(chain = true)
@ApiModel("设备分类日使用量看板Vo")
public class DailyCategoryUsageDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 无参构造函数
     */
    public DailyCategoryUsageDashboardVo() {
        this.dailyAverageUsageTime = 0;
        this.dailyUsageRate = BigDecimal.ZERO;
    }

    /**
     * 设备一级分类:如果复选不同的category用管道符|分割
     */
    @ApiModelProperty("设备一级分类:如果复选不同的category用管道符|分割")
    private String categoryCode;
    @ApiModelProperty("分类名称:如果复选不同的category用管道符|分割")
    private String categoryName;
    @ApiModelProperty("日期,yyyy-MM-dd字符串")
    private String date;
    @ApiModelProperty("日均使用时长(Daily Average Usage Time)=设备日总使用时长÷设备数量,统计单位为分钟")
    private Integer dailyAverageUsageTime=0;
    @ApiModelProperty("设备使用率(Daily Usage Rate)=日设备使用数量÷当日租户的设备总数量,统计单位为百分比")
    private BigDecimal dailyUsageRate=BigDecimal.ZERO;
    @ApiModelProperty("复选的数据是否全部有效,如果选中的某个标签当天为空值,则为false")
    private Boolean isFullyCalculated;
}
