package com.chervon.fleet.web.service;

import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.entity.dto.ReportInventoryDto;
import com.chervon.fleet.web.entity.dto.ReportMaintenancePageDto;
import com.chervon.fleet.web.entity.dto.ReportUsagePageDto;
import com.chervon.fleet.web.entity.vo.ReportInventoryVo;
import com.chervon.fleet.web.entity.vo.ReportMaintenanceVo;
import com.chervon.fleet.web.entity.vo.ReportUsageVo;
import com.chervon.fleet.web.entity.vo.WebFilterConditionVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface ReportService {

    /**
     * 库存-分页
     *
     * @param req 请求参数
     * @return 分页数据
     */
    PageResult<ReportInventoryVo> inventoryPage(PageRequest req);

    /**
     * 库存-列表-导出
     *
     * @param req     时区
     * @param response response对象
     */
    void inventoryExport(ReportInventoryDto req, HttpServletResponse response) throws IOException;

    /**
     * 设备使用-获取filter数据
     *
     * @return filter数据
     */
    WebFilterConditionVo usageFilterCondition();

    /**
     * 设备使用-分页
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<ReportUsageVo> usagePage(ReportUsagePageDto req);

    /**
     * 设备使用-列表
     *
     * @param req      查询条件
     * @param response response对象
     */
    void usageExport(ReportUsagePageDto req, HttpServletResponse response) throws IOException;

    /**
     * 维保-获取filter数据
     *
     * @return filter数据
     */
    WebFilterConditionVo maintenanceFilterCondition();

    /**
     * 维保-分页
     *
     * @param req 查询条件
     * @return 分页数据
     */
    PageResult<ReportMaintenanceVo> maintenancePage(ReportMaintenancePageDto req);

    /**
     * 维保-列表
     *
     * @param req      查询条件
     * @param response response对象
     */
    void maintenanceExport(ReportMaintenancePageDto req, HttpServletResponse response) throws IOException;
}
