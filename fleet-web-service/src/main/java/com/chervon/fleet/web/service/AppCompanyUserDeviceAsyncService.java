package com.chervon.fleet.web.service;

import org.springframework.scheduling.annotation.Async;

/**
 * <AUTHOR>
 * @since 2023-02-16 11:40
 **/
public interface AppCompanyUserDeviceAsyncService {
    /**
     * 回调将用户信息同步到AWS IOT-CORE物品属性
     *
     * @param companyId 租户id
     * @param userId    用户id
     * @param deviceId  设备ID
     */
    @Async
    void syncCompanyUserInfo2Device(Long companyId, Long userId, String deviceId);
}
