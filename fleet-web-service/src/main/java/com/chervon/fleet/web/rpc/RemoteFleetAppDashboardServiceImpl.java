package com.chervon.fleet.web.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.ChargerCategoryEnum;
import com.chervon.fleet.web.api.entity.enums.ChargerStateEnum;
import com.chervon.fleet.web.api.entity.query.CompanyDeviceQuery;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.api.entity.vo.*;
import com.chervon.fleet.web.api.service.RemoteFleetAppDashboardService;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.dto.HubDeviceIdDto;
import com.chervon.fleet.web.entity.po.*;
import com.chervon.fleet.web.entity.vo.dashboard.CategoryCountDashboardVo;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.service.translate.TranslateUtils;
import com.chervon.fleet.web.utils.DataUtils;
import com.chervon.operation.api.enums.FleetFirstCategoryEnum;
import com.chervon.technology.api.RemoteProductService;
import com.chervon.technology.api.vo.FleetDeviceProductBasicInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/24 15:22
 */
@DubboService
@Service
@Slf4j
public class RemoteFleetAppDashboardServiceImpl implements RemoteFleetAppDashboardService {
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;
    @Autowired
    private BiInventoryStatusService biInventoryStatusService;
    @Autowired
    private BiCategoryCountService biCategoryCountService;
    @Autowired
    private BiChargingStatusService biChargingStatusService;
    @Autowired
    private BiChargerCountService biChargerCountService;
    @Autowired
    private BiBatteryCountService biBatteryCountService;
    @Autowired
    private BiPowerHubChargingService biPowerHubChargingService;
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private TagService tagService;
    @Autowired
    private FleetDeviceConfig fleetDeviceConfig;
    @Autowired
    private TranslateUtils translateUtils;
    @DubboReference
    private RemoteProductService remoteProductService;

    @Override
    public DashboardInventoryVo inventory() {
        Long companyId = UserContext.getCompanyId();
        InventoryQuery inventoryQuery = new InventoryQuery();
        inventoryQuery.setCompanyId(companyId);

        DashboardInventoryVo result = new DashboardInventoryVo();
        List<String> toolExcludeCategory=Arrays.asList("Charger","Battery");
        // 先判断当前公司下是否有设备
        long deviceCount = companyDeviceService.count(new LambdaQueryWrapper<CompanyDevice>()
            .eq(CompanyDevice::getCompanyId, companyId)
            .notIn(CompanyDevice::getFirstCategoryCode, toolExcludeCategory));
        result.setDeviceCount((int) deviceCount);
        if (deviceCount == 0) {
            // 如果公司下没有设备直接返回deviceCount字段
            return result;
        }
        // ErrorNumber部分
        Integer count = biDeviceErrorListService.inventoryErrorCount(companyId);
        result.setErrorNumber(count);
        // InventoryStatus部分
        getInventoryStatus(result, companyId);
        // Machine Category部分
        getCategoryCount(result, companyId);
        return result;
    }

    /**
     * 获取库存饼图方法
     *
     * @param target    目标返回体
     * @param companyId companyId
     */
    private void getInventoryStatus(DashboardInventoryVo target, Long companyId) {
        List<InventoryStatusDashboardVo> inventoryStatusDashboardVos = biInventoryStatusService.inventoryStatus(companyId);
        Integer deviceSum = 0;
        for (InventoryStatusDashboardVo inventoryStatusDashboardVo : inventoryStatusDashboardVos) {
            deviceSum += inventoryStatusDashboardVo.getInventoryCount();
            switch (inventoryStatusDashboardVo.getInventoryType()) {
                case "In Warehouse":
                    target.setInWarehouseNumber(inventoryStatusDashboardVo.getInventoryCount());
                    break;
                case "Out for Work":
                    target.setOutForWorkNumber(inventoryStatusDashboardVo.getInventoryCount());
                    break;
                case "Unknown Current Location":
                    target.setUnknownLocationNumber(inventoryStatusDashboardVo.getInventoryCount());
                    break;
                case "Never Seen":
                    target.setNeverSeenNumber(inventoryStatusDashboardVo.getInventoryCount());
                    break;
                default:
                    log.error("RemoteFleetAppDashboardServiceImpl#inventory -> 库存类型出现非约定值:{}",
                        inventoryStatusDashboardVo.getInventoryType());
                    break;
            }
        }
        target.setTotalNumber(deviceSum);
    }

    /**
     * 获取设备分类柱状图方法
     *
     * @param target    目标返回体
     * @param companyId 公司ID
     */
    private void getCategoryCount(DashboardInventoryVo target, Long companyId) {
        List<CategoryCountDashboardVo> categoryCountDashboardVos = biCategoryCountService.categoryCount(companyId);
        List<DashboardCategoryCountVo> dashboardCategoryCountVoList = new ArrayList<>();
        // 求和
        int categorySum = categoryCountDashboardVos.stream().mapToInt(CategoryCountDashboardVo::getCategoryCount).sum();
        for (CategoryCountDashboardVo categoryCountDashboardVo : categoryCountDashboardVos) {
            // 转换
            DashboardCategoryCountVo dashboardCategoryCountVo = ConvertUtil.convert(categoryCountDashboardVo,
                DashboardCategoryCountVo.class);
            // 求百分比
            BigDecimal value = new BigDecimal(dashboardCategoryCountVo.getCategoryCount());
            BigDecimal sum = new BigDecimal(categorySum);
            BigDecimal percentage = sum.equals(BigDecimal.ZERO)?BigDecimal.ZERO: value.divide(sum, CommonConstant.TWO, RoundingMode.HALF_UP).multiply(new BigDecimal(NumberConst.PERCENTAGE));
            dashboardCategoryCountVo.setCategoryRate(percentage);
            dashboardCategoryCountVoList.add(dashboardCategoryCountVo);
        }
        //国际化转换
        translateUtils.translateListBatch(dashboardCategoryCountVoList);
        target.setCategoryCountList(dashboardCategoryCountVoList);
    }

    @Override
    public List<DashboardErrorVo> inventoryError() {
        Long companyId = UserContext.getCompanyId();
        List<DashboardErrorVo> result = new ArrayList<>();
        List<BiDeviceErrorList> biDeviceErrorList = biDeviceErrorListService.listInventoryErrorsByCompanyId(companyId);
        // 获取deviceId列表
        List<String> deviceIds = biDeviceErrorList.stream()
            .map(BiDeviceErrorList::getDeviceId).collect(Collectors.toList());
        // RPC调用iot-platform获取各个设备deviceId对应设备产品信息
        Map<String, FleetDeviceProductBasicInfoBo> boMap = remoteProductService.getFleetDeviceProductBasicInfoBoMap(deviceIds);
        for (BiDeviceErrorList biDeviceError : biDeviceErrorList) {
            // 获取设备产品信息,如果在iot-platform未查询到则使用默认空值
            FleetDeviceProductBasicInfoBo bo = boMap.getOrDefault(biDeviceError.getDeviceId(), new FleetDeviceProductBasicInfoBo());
            DashboardErrorVo vo = ConvertUtil.convert(biDeviceError, DashboardErrorVo.class);
            vo.setCommodityModel(bo.getCommodityModel());
            vo.setDeviceSn(bo.getDeviceSn());
            vo.setProductIconUrl(bo.getProductIconUrl());
            vo.setProductModel(bo.getProductModel());
            vo.setProductId(bo.getProductId());
            vo.setFaultMessageTitle(biDeviceError.getErrorMessage());
            vo.setSuggestionContent(biDeviceError.getSuggestionContent());
            result.add(vo);
        }
        return result;
    }

    /**
     * fleet-gateway app power概览信息（租户级别）
     * @return
     */
    @Override
    public DashboardPowerVo power() {
        Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED,"companyId");
        DashboardPowerVo result = new DashboardPowerVo();

        // 先判断当前公司下是否有设备
        long deviceCount = companyDeviceService.count(new LambdaQueryWrapper<CompanyDevice>()
            .eq(CompanyDevice::getCompanyId, companyId)
                .eq(CompanyDevice::getFirstCategoryCode, FleetFirstCategoryEnum.CHARGER.getCategoryCode()));
        result.setDeviceCount((int) deviceCount);
        if (deviceCount == 0) {
            result.setHaveData(false);
            // 如果公司下没有设备直接返回deviceCount字段
            return result;
        }
        BiChargingStatus biChargingStatus = biChargingStatusService.getByCompanyId(companyId);
        if (null == biChargingStatus || companyDeviceService.getPowerHubCount(companyId,true)==0L) {
            result.setHaveData(false);
            return result;
        }
        //获取租户下在线设备id集合
        final List<String> allOnlineDeviceId = getCompanyOnlineDeviceList(companyId);

        Integer count = CollectionUtils.isEmpty(allOnlineDeviceId)?0: biDeviceErrorListService.getChargerErrorCount(companyId,allOnlineDeviceId);
        long timestamp = biChargingStatus.getRemainingChargingTimestamp()==0? 0: (System.currentTimeMillis() + (biChargingStatus.getRemainingChargingTimestamp() * NumberConst.ONE_MINUTE_MS));
        result.setCompletionTime(timestamp);
        result.setRemainingChargingTime(biChargingStatus.getRemainingChargingTimestamp().toString());
        result.setErrorNumber(count);
        //此字段是CH7000的充电百分比，如果百分比大于0，不显示下面的详情：==0 true ,>0 false
        result.setIsShowOverview(Objects.equals(biChargingStatus.getChargingPercentage(), 0));
        result.setChargeStatus(biChargingStatus.getChargerState());
        return result;
    }

    @NotNull
    private List<String> getCompanyOnlineDeviceList(Long companyId) {
        final List<String> hubOnlineList = companyDeviceService.getHubOnlineList(companyId);
        final List<HubDeviceIdDto> hubLoadAllDeviceIds = biDeviceErrorListService.getMoreHubLoadAllDeviceIds(companyId, hubOnlineList);
        final List<String> allOnlineDeviceId = hubLoadAllDeviceIds.stream().flatMap(a -> a.getAllDevice().stream()).collect(Collectors.toList());
        return allOnlineDeviceId;
    }

    /**
     * fleet-gateway app powerAvailability概览信息（租户级别）
     * @return
     */
    @Override
    public DashboardPowerAvailabilityVo powerAvailability() {
        Long companyId = UserContext.getCompanyId();
        InventoryQuery inventoryQuery = new InventoryQuery();
        inventoryQuery.setCompanyId(companyId);
        DashboardPowerAvailabilityVo result = new DashboardPowerAvailabilityVo();
        long onlineCount= companyDeviceService.getPowerHubCount(companyId,true);
        BiChargingStatus biChargingStatus = biChargingStatusService.getByCompanyId(companyId);
        if (biChargingStatus != null  && onlineCount>0L) {
            haveDataSetValue(result, biChargingStatus);
        }else{//过期或无数据
            nothingDataSetValue(result);
        }

        // charger部分
        setChargerResult(companyId, result);

        // batteries部分  有充电器且powerHub有在线的才统计电池包数量
        setBatteryResult(result, onlineCount, companyId);

        return result;
    }

    private static void nothingDataSetValue(DashboardPowerAvailabilityVo result) {
        result.setHaveData(false);
        result.setChargingNumber(0);
        result.setChargingAh(BigDecimal.ZERO);
        result.setReadyNumber(0);
        result.setAvailableAh(BigDecimal.ZERO);
        result.setReadyAh(BigDecimal.ZERO);
        result.setReadyRate(BigDecimal.ZERO);
        result.setBatteries(new ArrayList<>());
        result.setTotalBatteryNumber(0);
    }

    private static void haveDataSetValue(DashboardPowerAvailabilityVo result, BiChargingStatus biChargingStatus) {
        result.setHaveData(true);
        result.setChargingNumber(biChargingStatus.getChargingBatteryCount());
        result.setChargingAh(biChargingStatus.getChargingBatteryAh());
        result.setReadyNumber(biChargingStatus.getReadyBatteryCount());
        result.setAvailableAh(biChargingStatus.getReadyBatteryAh());
        result.setReadyAh(biChargingStatus.getReadyBatteryAh());
        if (biChargingStatus.getChargingPercentage() != 0) {
            // CH7000
            result.setReadyRate(BigDecimal.valueOf(biChargingStatus.getChargingPercentage()));
        } else {
            // 充电能量百分比= AvailableAh / AvailableAh + ChargingAh
            BigDecimal sum = result.getAvailableAh().add(result.getChargingAh());
            if(sum.compareTo(BigDecimal.ZERO)==0){
                result.setReadyRate(BigDecimal.ZERO);
            }else{
                BigDecimal percentage = result.getAvailableAh().multiply(new BigDecimal(NumberConst.PERCENTAGE)).divide(sum, CommonConstant.ZERO, RoundingMode.HALF_UP);
                result.setReadyRate(percentage);
            }
        }
    }

    private void setBatteryResult(DashboardPowerAvailabilityVo result, long onlineCount, Long companyId) {
        if(result.getTotalChargerNumber()>0 && onlineCount >0L){
            List<BiBatteryCount> biBatteryCountList = biBatteryCountService.listByCompanyId(companyId);
            List<DashboardBatteryVo> batteries = new ArrayList<>();
            if (!CollectionUtils.isEmpty(biBatteryCountList)) {
                for (BiBatteryCount batteryCount : biBatteryCountList) {
                    DashboardBatteryVo vo = new DashboardBatteryVo();
                    vo.setName(batteryCount.getBatteryType());
                    vo.setCount(batteryCount.getBatteryCount());
                    vo.setTotalAh(batteryCount.getTotalAh());
                    batteries.add(vo);
                }
            }
            result.setBatteries(batteries);
            result.setTotalBatteryNumber(batteries.stream().mapToInt(DashboardBatteryVo::getCount).sum());
        }
    }

    private void setChargerResult(Long companyId, DashboardPowerAvailabilityVo result) {
        List<BiChargerCount> biChargerCountList = biChargerCountService.listByCompanyId(companyId);
        List<DashboardChargerVo> chargers = new ArrayList<>();
        if (!CollectionUtils.isEmpty(biChargerCountList)) {
            translateUtils.translateListBatch(biChargerCountList);
            chargers = biChargerCountList.stream().map(biChargerCount -> {
                DashboardChargerVo vo = new DashboardChargerVo();
                vo.setCount(biChargerCount.getChargerCount());
                vo.setName(biChargerCount.getChargerName());
                return vo;
            }).collect(Collectors.toList());
        }
        result.setChargers(chargers);
        result.setTotalChargerNumber(chargers.stream().mapToInt(DashboardChargerVo::getCount).sum());
    }

    /**
     * fleet-gateway PowerHub列表(租户级别)
     * @return
     */
    @Override
    public List<DashboardPowerHubVo> powerOverview() {
        Long companyId = UserContext.getCompanyId();
        Assert.isId(companyId,ErrorCode.PARAMETER_NOT_PROVIDED,"companyId");
        long hubCount = companyDeviceService.count(new LambdaUpdateWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId,companyId)
                .eq(CompanyDevice::getSecondCategoryCode,ChargerCategoryEnum.PGX_HUB.getCode()));
        List<DashboardPowerHubVo> result = new ArrayList<>();
        if(hubCount==0){
            //无PowerHub设备不展示列表
            return result;
        }
        List<BiPowerHubCharging> biPowerHubChargingList = biPowerHubChargingService.listByCompanyId(companyId);
        // errors
        List<String> hubDeviceIds=biPowerHubChargingList.stream().map(BiPowerHubCharging::getDeviceId).distinct().collect(Collectors.toList());
        final ConcurrentMap<String, CompanyDevice> mapOnline = companyDeviceService.getDeviceOnlineInfoMap(new CompanyDeviceQuery().setDeviceIds(hubDeviceIds));
        List<String> onlineFaultHub = CollectionUtils.isEmpty(mapOnline)?new ArrayList<>():mapOnline.keySet().stream().collect(Collectors.toList());
        Map<String, BiDeviceErrorList> biDeviceErrorMap = biDeviceErrorListService.batchGetLatestErrorForPowerHub(companyId,onlineFaultHub);
        // tags
        Map<String, List<TagVo>> tagVoMap = tagService.getTagVoMap(companyId, hubDeviceIds);
        boolean tagVoMapEmptyFlag = CollectionUtils.isEmpty(tagVoMap);

        for (BiPowerHubCharging biPowerHubCharging : biPowerHubChargingList) {
            DashboardPowerHubVo vo = convertFromPo(biPowerHubCharging);
            // tags
            if (!tagVoMapEmptyFlag) {
                vo.setTags(tagVoMap.get(biPowerHubCharging.getDeviceId()));
            }
            if(!DataUtils.isOffline(mapOnline.get(biPowerHubCharging.getDeviceId()))) {
                // errors
                BiDeviceErrorList hubFirstError = biDeviceErrorMap.getOrDefault(biPowerHubCharging.getDeviceId(), null);
                List<DashboardPowerHubVo.Error> listFirstError = new ArrayList<>();
                if (hubFirstError != null) {
                    DashboardPowerHubVo.Error error = new DashboardPowerHubVo.Error();
                    error.setErrorCode(hubFirstError.getErrorCode());
                    error.setFaultMessageTitle(hubFirstError.getErrorMessage());
                    error.setSuggestionContent(hubFirstError.getSuggestionContent());
                    listFirstError.add(error);
                }
                vo.setErrors(listFirstError);
                final Integer hour = biPowerHubCharging.getRemainingChargingTimestamp() / 60; //换算小时
                final Integer minutes = biPowerHubCharging.getRemainingChargingTimestamp() % 60; //换算成分钟
                //预计充电完成剩余时间小时分钟字符串表示：(4h 37min)
                vo.setReadyInTime(MessageFormat.format("{0}:{1}",hour,minutes));
            }else{
                vo.setChargerState(ChargerStateEnum.NO_DATA.getValue());
                vo.setChargingRate(BigDecimal.ZERO);
                vo.setHighChargingNumber(0);
                vo.setHighReadyNumber(0);
                vo.setHighWaitingNumber(0);
                vo.setPortableChargingNumber(0);
                vo.setPortableReadyNumber(0);
                vo.setPortableWaitingNumber(0);
                vo.setPortTotalNumber(0);
                vo.setPortUsedNumber(0);
                vo.setReadyInTime("");
            }
            result.add(vo);
        }
        return result;
    }

    /**
     * 实体类转换为看板VO
     * 其中看板VO的tag列表, error列表需要额外写逻辑赋值
     *
     * @param biPowerHubCharging PO实体类
     * @return 看板VO
     */
    private static DashboardPowerHubVo convertFromPo(BiPowerHubCharging biPowerHubCharging) {
        DashboardPowerHubVo vo = new DashboardPowerHubVo();
        vo.setDeviceId(biPowerHubCharging.getDeviceId());
        vo.setDeviceName(biPowerHubCharging.getDeviceName());
        vo.setChargingRate(biPowerHubCharging.getChargingPercentage());
        vo.setHighChargingNumber(biPowerHubCharging.getHighCapacityBatteryCharging());
        vo.setHighReadyNumber(biPowerHubCharging.getHighCapacityBatteryReady());
        vo.setHighWaitingNumber(biPowerHubCharging.getHighCapacityBatteryStandby());
        vo.setPortableChargingNumber(biPowerHubCharging.getPortableBatteryCharging());
        vo.setPortableReadyNumber(biPowerHubCharging.getPortableBatteryReady());
        vo.setPortableWaitingNumber(biPowerHubCharging.getPortableBatteryStandby());
        vo.setPortTotalNumber(biPowerHubCharging.getChargingPortCount());
        vo.setPortUsedNumber(biPowerHubCharging.getChargingPortOccupy());
        vo.setChargerState(biPowerHubCharging.getChargerState());
        return vo;
    }
}
