package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 服务状态展示实体
 *
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ServerStatusVo展示服务状态实体", description = "服务状态")
public class ServiceStatusVo {
    private static final long serialVersionUID = 1L;
    /**
     * 实例ID
     */
    @ApiModelProperty(value = "实例ID")
    private String id;
    /**
     * 服务名称
     */
    @ApiModelProperty(value = "服务名称")
    private String name;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;
    /**
     * 运行时间
     */
    @ApiModelProperty(value = "运行时间")
    private String runTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 状态
     */
    @ApiModelProperty(value = "tags")
    private String tags;
}
