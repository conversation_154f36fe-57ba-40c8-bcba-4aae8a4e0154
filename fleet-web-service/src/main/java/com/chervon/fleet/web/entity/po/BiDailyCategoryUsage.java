package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备日使用量统计表（看板：Usage Statistics）(t_bi_daily_category_usage)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_bi_daily_category_usage")
public class BiDailyCategoryUsage extends Model<BiDailyCategoryUsage> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 设备一级分类
     */
    private String categoryCode;
    /**
     * 分类名称
     */
    private String categoryName;
    /**
     * 日期：2023-07-12
     */
    private Date date;
    /**
     * 日期字符串,yyyy-MM-dd
     */
    private String strDate;
    /**
     * 日均使用时长(Daily Average Usage Time)=设备日总使用时长÷设备数量，统计单位为分钟
     */
    private Integer dailyAverageUsageTime;
    /**
     * Daily Usage Rate=日设备使用数量÷当日租户的设备总数量，统计单位为百分比
     */
    private BigDecimal dailyUsageRate;
    /**
     * 设备日总使用时长
     */
    private Integer dailyTotalUsageTime;
    /**
     * 当日设备总使用数量
     */
    private Integer dailyDeviceUsageCount;
    /**
     * 当日租户工具分类下设备总数量
     */
    private Integer dailyDeviceTotalCount;
    /**
     * 当日租户分类下设备总数量
     */
    private Integer deviceCategoryCount;
    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 检测PO的所有数值参数不为空
     *
     * @param biDailyCategoryUsage PO
     * @return 是否所有数值参数不为空
     */
    public static Boolean propertyNotNull(BiDailyCategoryUsage biDailyCategoryUsage) {
        return biDailyCategoryUsage.getDailyDeviceUsageCount() != null &&
            biDailyCategoryUsage.getDailyTotalUsageTime() != null &&
            biDailyCategoryUsage.getDailyDeviceTotalCount() != null;
    }

    public void setId(){
        this.id=(long)(this.companyId.toString().concat(categoryCode).concat(this.strDate)).hashCode();
    }
}