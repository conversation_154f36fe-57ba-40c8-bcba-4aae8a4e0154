package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.error.GroupTagErrorCodeEnum;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.GroupDto;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.entity.po.Group;
import com.chervon.fleet.web.entity.po.Tag;
import com.chervon.fleet.web.api.entity.vo.GroupVo;
import com.chervon.fleet.web.mapper.GroupMapper;
import com.chervon.fleet.web.service.DeviceTagService;
import com.chervon.fleet.web.service.GroupService;
import com.chervon.fleet.web.service.TagService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * {@code @date} 2023/7/11 15:29
 */
@Service
@Slf4j
@AllArgsConstructor
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Group> implements GroupService {

    @Lazy
    private final DeviceTagService deviceTagService;

    @Override
    public void createDefaultGroup(Long companyId) {
        long count = this.count(new LambdaQueryWrapper<Group>().eq(Group::getIsDefault, 1)
                .eq(Group::getCompanyId, companyId));
        if (count > 0) {
            throw new ServiceException(GroupTagErrorCodeEnum.COMPANY_HAD_DEFAULT_TAG_GROUP);
        }
        final String language = UserContext.getLanguage();
        Assert.notNull(language,ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LANGUAGE);
        String defaultTagName= I18nController.getResourceById(language, StringConst.DEFAULT_TAG_KEY);
        Group group = new Group();
        group.setIsDefault(1)
                .setCompanyId(companyId)
                .setName(defaultTagName);
        this.save(group);
    }

    @Override
    public List<GroupVo> listAll() {
        Long companyId = UserContext.getCompanyId();
        List<Group> list = this.list(new LambdaQueryWrapper<Group>().eq(Group::getCompanyId, companyId).orderByDesc(Group::getIsDefault).orderByDesc(Group::getCreateTime));
        return list.stream().map(e -> {
            GroupVo vo = new GroupVo();
            vo.setGroupId(e.getId()).setGroupName(e.getName()).setIsDefault(e.getIsDefault());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void add(GroupDto req) {
        Long companyId = UserContext.getCompanyId();
        long count = this.count(new LambdaQueryWrapper<Group>().eq(Group::getCompanyId, companyId));
        if (count >= GROUP_MAX) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_EXCEEDS_100);
        }
        check(req, companyId, true);
        Group group = new Group();
        group.setCompanyId(companyId).setName(req.getGroupName()).setIsDefault(0);
        this.save(group);
    }

    private void check(GroupDto req, Long companyId, boolean create) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "groupDto");
        if (!create) {
            // 编辑模式，分组id必填
            Assert.notNull(req.getGroupId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GROUP_ID);
        }
        Assert.hasText(req.getGroupName(), ErrorCode.PARAMETER_NOT_PROVIDED, "groupName");
        LambdaQueryWrapper<Group> wrapper = new LambdaQueryWrapper<Group>().eq(Group::getName, req.getGroupName()).eq(Group::getCompanyId, companyId);
        if (!create) {
            wrapper.ne(Group::getId, req.getGroupId());
        }
        Group exist = this.getOne(wrapper);
        if (exist != null) {
            // 存在名称相同的分组
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_NAME_EXISTED);
        }
    }

    @Override
    public GroupVo detail(Long groupId) {
        Assert.notNull(groupId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GROUP_ID);
        Group group = this.getById(groupId);
        if (group == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_NOT_EXIST);
        }
        GroupVo res = new GroupVo();
        res.setGroupId(group.getId()).setGroupName(group.getName()).setIsDefault(group.getIsDefault());
        return res;
    }

    @Override
    public void edit(GroupDto req) {
        Long companyId = UserContext.getCompanyId();
        check(req, companyId, false);
        Group group = this.getById(req.getGroupId());
        if (group == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_NOT_EXIST);
        }
        if (group.getIsDefault() == 1) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_DEFAULT_CANNOT_EDITED);
        }
        Group newOne = new Group();
        newOne.setId(req.getGroupId()).setName(req.getGroupName());
        this.updateById(newOne);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long groupId) {
        Assert.notNull(groupId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.GROUP_ID);
        Group group = this.getById(groupId);
        if (group == null) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_NOT_EXIST);
        }
        if (group.getIsDefault() == 1) {
            throw new ServiceException(GroupTagErrorCodeEnum.GROUP_DEFAULT_CANNOT_DELETED);
        }
        this.removeById(groupId);
        // 删除分组下的标签
        TagService tagService=SpringUtils.getBean(TagService.class);
        tagService.remove(new LambdaQueryWrapper<Tag>().eq(Tag::getGroupId, groupId));
        // 删除设备标签中属于该分组的数据
        deviceTagService.remove(new LambdaQueryWrapper<DeviceTag>().eq(DeviceTag::getGroupId, groupId));
    }
}
