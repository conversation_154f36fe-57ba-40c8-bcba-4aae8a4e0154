package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/11 16:27
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "分组操作对象")
public class GroupDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("分组id，新增不必填，编辑必填")
    private Long groupId;

    @ApiModelProperty("分组名称")
    private String groupName;

}
