package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.MaintenanceDto;
import com.chervon.fleet.web.entity.dto.MaintenanceLogDto;
import com.chervon.fleet.web.entity.dto.MaintenanceLogSearchDto;
import com.chervon.fleet.web.entity.vo.MaintenanceLogVo;
import com.chervon.fleet.web.entity.vo.MaintenanceVo;
import com.chervon.fleet.web.service.MaintenanceLogService;
import com.chervon.fleet.web.service.MaintenancePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Maintenance设备维保信息相关接口
 */
@Api(tags = "Maintenance设备维保信息相关接口")
@RestController
@Slf4j
@RequestMapping("/equipment")
@AllArgsConstructor
public class MaintenanceController {
    private final MaintenanceLogService maintenanceLogService;
    private final MaintenancePlanService maintenancePlanService;

    @ApiOperation("设备维保信息")
    @GetMapping("maintenance")
    @WebApiHeaders
    public MaintenanceVo maintenance(@RequestParam("deviceId") String deviceId) {
        return maintenancePlanService.maintenance(deviceId);
    }

    @ApiOperation("设备维保信息-编辑")
    @PostMapping("maintenance/edit")
    @WebApiHeaders
    public void maintenanceEdit(@RequestBody MaintenanceDto req) {
        maintenancePlanService.maintenanceEdit(req);
    }

    @ApiOperation("设备维保日志-分页")
    @PostMapping("maintenance/log/page")
    @WebApiHeaders
    public PageResult<MaintenanceLogVo> maintenanceLogPage(@RequestBody MaintenanceLogSearchDto req) {
        return maintenanceLogService.maintenanceLogPage(req);
    }

    /**
     * 查询设备使用时长：从设备影子状态表：t_data_shadow_status查询
     */
    @ApiOperation("设备维保日志-查询截止当前的运行时间-传入deviceId")
    @GetMapping("maintenance/log/runtime")
    @WebApiHeaders
    public Map<String, Object> maintenanceLogRuntime(@RequestParam("deviceId") String deviceId) {
        Integer runtime = maintenanceLogService.maintenanceLogRuntime(deviceId);
        final String language = UserContext.getLanguage();
        Assert.notNull(language, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LANGUAGE);
        String resourceHours= I18nController.getResourceById(language, StringConst.LANG_KEY_HOURS);
        Map<String, Object> res = new ConcurrentHashMap<>();
        res.put(StringConst.RUNTIME, runtime);
        res.put(StringConst.RUNTIME_SHOW, runtime + " "+resourceHours);
        return res;
    }

    @ApiOperation("设备维保日志-新增")
    @PostMapping("maintenance/log/add")
    @WebApiHeaders
    public void maintenanceLogAdd(@RequestBody MaintenanceLogDto req) {
        maintenanceLogService.maintenanceLogAdd(req);
    }

    @ApiOperation("设备维保日志-编辑")
    @PostMapping("maintenance/log/edit")
    @WebApiHeaders
    public void maintenanceLogEdit(@RequestBody MaintenanceLogDto req) {
        maintenanceLogService.maintenanceLogEdit(req);
    }

    @ApiOperation("设备维保日志-删除-传入maintenanceLogId")
    @GetMapping("maintenance/log/delete")
    @WebApiHeaders
    public void maintenanceLogDelete(@RequestParam("maintenanceLogId") Long maintenanceLogId) {
        Assert.notNull(maintenanceLogId, ErrorCode.PARAMETER_NOT_PROVIDED, "maintenanceLogId");
        maintenanceLogService.maintenanceLogDelete(maintenanceLogId);
    }

    @ApiOperation("设备维保日志-详情-传入maintenanceLogId")
    @GetMapping("maintenance/log/detail")
    @WebApiHeaders
    public MaintenanceLogVo maintenanceLogDetail(@RequestParam("maintenanceLogId") Long maintenanceLogId) {
        Assert.notNull(maintenanceLogId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.MAINTENANCE_LOG_ID);
        return maintenanceLogService.maintenanceLogDetail(maintenanceLogId);
    }

}
