package com.chervon.fleet.web.entity.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-08-23 19:38
 **/
@Data
@TableName("t_product_fault")
public class ProductFault implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    @TableId
    private Long id;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品SN编码
     */
    private String productSnCode;
    /**
     * 故障编号
     */
    private String faultCode;
    /**
     * 故障标题
     */
    private String faultTitle;
    /**
     * 语言编码
     */
    private String language;
    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 处理建议id
     */
    private Long suggestionId;
    /**
     * 处理建议内容
     */
    private String suggestionContent;
}
