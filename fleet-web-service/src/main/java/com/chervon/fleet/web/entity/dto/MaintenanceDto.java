package com.chervon.fleet.web.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/21 10:55
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "维保操作对象")
public class MaintenanceDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("维保id")
    private Long maintenanceId;

    @ApiModelProperty("维保类型：1延保日期  2使用工时  3关闭")
    private Integer planType;

    @ApiModelProperty("延保日期：截止时间，格式yyyy-MM-dd")
    private String deadlineTime;

    @ApiModelProperty("使用工时：使用工时小时数")
    private Integer usageHour;

    @ApiModelProperty("浏览器所在时区，比如中国就是8，西方的就是负数，默认0时区")
    private int zone = 0;

}
