package com.chervon.fleet.web.service.translate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.core.TranslateService;
import com.chervon.common.web.core.TranslateServiceFactory;
import com.chervon.common.web.entity.MetaContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 国际化翻译工具类
 * <AUTHOR> 2022/11/30
 */
@Slf4j
@Component
public class TranslateUtils {

    /**
     * 根据code翻译字段名称
     * @param list 需要翻译的集合
     */
    public <T> void translateListForeach(List<T> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (T dataEntity : list) {
            doConvert(dataEntity);
        }
    }

    public <T> void translateListBatch(List<T> list){
        final List<MetaContext> batchSourceValue = batchReadSourceMetaInfo(list);
        doConvertBatch(list,batchSourceValue);
    }

    private <T> List<MetaContext> batchReadSourceMetaInfo(List<T> list){
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        final T dataDto = list.get(0);
        Class<?> aClass = dataDto.getClass();
        List<Field> fields = getAllFieldList(aClass);
        Map<String,MetaContext> sourceValueMap=new ConcurrentHashMap<>();
        try {
            for(T dataEntity : list){
                readMetaInfo(fields, sourceValueMap, dataEntity);
            }
        } catch (IllegalAccessException e) {
            return Collections.emptyList();
        }
        return sourceValueMap.values().stream().collect(Collectors.toList());
    }

    private static  <T> void readMetaInfo(List<Field> fields, Map<String, MetaContext> sourceValueMap, T dataEntity) throws IllegalAccessException {
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(Translate.class)) {
                Translate annotation = field.getAnnotation(Translate.class);
                Object objValue = field.get(dataEntity);
                final List<Field> targetFields = filterField(fields, Arrays.asList(annotation.targetField()));
                if (objValue != null) {
                    final String mapKey = field.getName();
                    MetaContext metaContext = sourceValueMap.get(mapKey);
                    if(Objects.isNull(metaContext)){
                        metaContext= MetaContext.instance();
                        metaContext.setAdapter(annotation.adapter())
                                .setSourceField(field)
                                .setTargetField(targetFields);
                        sourceValueMap.put(mapKey,metaContext);
                    }
                    metaContext.getListSourceValue().add(objValue);
                }
            }
        }
    }

    /**
     * *  根据注解实现自定义转换并赋值
     * @param dataDto 转换目标对象
     * @param <T> 泛型
     * @return
     */
    private <T> void doConvert(T dataDto) {
        try {
            if (ObjectUtil.isEmpty(dataDto)) {
                return;
            }
            Class<?> aClass = dataDto.getClass();
            List<Field> fields = getAllFieldList(aClass);
            for (Field field : fields) {
                if (field.isAnnotationPresent(Translate.class)) {
                    Translate annotation = field.getAnnotation(Translate.class);
                    final TranslateService translateService = TranslateServiceFactory.getInstance(annotation.adapter());
                    field.setAccessible(true);
                    final Object result = translateService.getSourceValue(field, dataDto);
                    if (!ObjectUtil.isNull(result)) {
                        final List<String> targetField = Arrays.asList(annotation.targetField());
                        if (CollectionUtil.isEmpty(targetField)) {
                            continue;
                        }
                        translateService.setTargetValue(dataDto, result, targetField);
                    }
                }
            }
        } catch (IllegalArgumentException e) {
            log.error("translate binding field IllegalArgumentException:{}", e);
        } catch (IllegalAccessException e) {
            log.error("translate binding field IllegalAccessException:{}", e);
        }
    }

    /**
     * *  根据注解实现自定义转换并赋值
     * @param <T> 泛型
     * @return
     */
    private <T> void doConvertBatch(List<T> list,List<MetaContext> contexts) {
        try {
            if (CollectionUtil.isEmpty(contexts)) {
                return;
            }
            for (MetaContext entry : contexts) {
                final TranslateService translateService = TranslateServiceFactory.getInstance(entry.getAdapter());
                translateService.batchGetSourceValue(entry);
                translateService.batchSetTargetValue(list,entry);
            }
        } catch (IllegalArgumentException e) {
            log.error("translate binding field IllegalArgumentException:{}", e);
        } catch (IllegalAccessException e) {
            log.error("translate binding field IllegalAccessException:{}", e);
        }
    }

    /**
     * 获取所有字段含父级
     * @param aClass 实体类
     * @return 所有字段信息
     */
    public static List<Field> getBindFields(Class<?> aClass,List<String> targetFieldName) {
        List<Field> fieldList = new ArrayList<>();
        Class tempClass = aClass;
        //当父类为null的时候说明到达了最上层的父类(Object类).
        while (tempClass != null) {
            fieldList.addAll(Arrays.asList(tempClass.getDeclaredFields()));
            tempClass = tempClass.getSuperclass(); //得到父类,然后赋给自己
        }
        return filterField(fieldList, targetFieldName);
    }

    private static List<Field> filterField(List<Field> allFields,List<String> targetFieldName) {
        return allFields.stream().filter(a->targetFieldName.contains(a.getName())).collect(Collectors.toList());
    }

    /**
     * 获取所有字段含父级
     * @param aClass 实体类
     * @return 所有字段信息
     */
    public static List<Field> getAllFieldList(Class<?> aClass) {
        List<Field> fieldList = new ArrayList<>();
        Class tempClass = aClass;
        //当父类为null的时候说明到达了最上层的父类(Object类).
        while (tempClass != null) {
            fieldList.addAll(Arrays.asList(tempClass.getDeclaredFields()));
            tempClass = tempClass.getSuperclass(); //得到父类,然后赋给自己
        }
        return fieldList;
    }
}
//https://blog.csdn.net/u010012932/article/details/125621347
