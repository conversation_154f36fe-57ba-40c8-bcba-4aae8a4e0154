package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.MaintenancePlanTypeEnum;
import com.chervon.fleet.web.api.entity.enums.MaintenanceStatusEnum;
import com.chervon.fleet.web.api.entity.error.MaintenanceErrorCodeEnum;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.MaintenanceDto;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.MaintenancePlan;
import com.chervon.fleet.web.entity.po.ShadowStatus;
import com.chervon.fleet.web.entity.vo.MaintenanceVo;
import com.chervon.fleet.web.mapper.MaintenancePlanMapper;
import com.chervon.fleet.web.service.CompanyDeviceService;
import com.chervon.fleet.web.service.MaintenancePlanService;
import com.chervon.fleet.web.service.ShadowStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 设备维保计划表服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-15 19:15:52
 */
@Slf4j
@Service
public class MaintenancePlanServiceImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlan> implements MaintenancePlanService {
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private ShadowStatusService shadowStatusService;
    /**
     * 获取设备维保信息
     * @param deviceId 设备id
     * @return
     */
    @Override
    public MaintenanceVo maintenance(String deviceId) {
        Long companyId = UserContext.getCompanyId();
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        MaintenancePlan maintenancePlan = getOne(new LambdaQueryWrapper<MaintenancePlan>()
                .eq(MaintenancePlan::getDeviceId, deviceId).eq(MaintenancePlan::getCompanyId, companyId));
        if (maintenancePlan == null) {
            return null;
        }
        MaintenanceVo res = new MaintenanceVo();
        BeanUtils.copyProperties(maintenancePlan, res);
        res.setMaintenanceId(maintenancePlan.getId());
        CompanyDevice companyDevice = companyDeviceService.getOne(new LambdaQueryWrapper<CompanyDevice>().eq(CompanyDevice::getDeviceId, deviceId).eq(CompanyDevice::getCompanyId, companyId));
        if (companyDevice != null) {
            res.setMaintenanceStatus(companyDevice.getMaintenanceStatus());
        }
        if (maintenancePlan.getPlanType() == MaintenancePlanTypeEnum.ENGINE_HOURS.getType()) {
            // 设置此时的使用时间
            ShadowStatus shadowStatus = shadowStatusService.getByDeviceId(maintenancePlan.getDeviceId(),companyId);
            Integer usedSecond = 0;
            if (shadowStatus != null && shadowStatus.getTotalUsageTime() != null) {
                usedSecond = shadowStatus.getTotalUsageTime()-maintenancePlan.getWorkHoursUsed();
            }
            res.setUsedHour(usedSecond/ NumberConst.ONE_HOUR_SECOND);
        }
        return res;
    }

    /**
     * 维保编辑
     * @param req 维保操作对象
     */
    @Override
    public void maintenanceEdit(MaintenanceDto req) {
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, "maintenanceDto");
        Assert.notNull(req.getMaintenanceId(), ErrorCode.PARAMETER_NOT_PROVIDED, "maintenanceId");
        Assert.notNull(req.getPlanType(), ErrorCode.PARAMETER_NOT_PROVIDED, "planType");
        MaintenancePlanTypeEnum planTypeEnum = MaintenancePlanTypeEnum.getEnum(req.getPlanType());
        Assert.notNull(planTypeEnum, ErrorCode.PARAMETER_FORMAT_ERROR, "planType");
        MaintenancePlan maintenancePlan = getById(req.getMaintenanceId());
        if (maintenancePlan == null) {
            throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_NOT_FOUND);
        }
        long now = System.currentTimeMillis();
        MaintenancePlan newMaintenancePlan = new MaintenancePlan();
        newMaintenancePlan.setId(req.getMaintenanceId()).setPlanType(req.getPlanType()).setPlanBegin(now);
        if (req.getPlanType().equals(MaintenancePlanTypeEnum.DATE.getType())) {
            Assert.hasText(req.getDeadlineTime(), ErrorCode.PARAMETER_NOT_PROVIDED, "deadlineTime");
            LocalDate deadlineDate;
            try {
                deadlineDate = LocalDate.parse(req.getDeadlineTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (DateTimeParseException e) {
                throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_DEADLINE_TIME_ERROR);
            }catch (RuntimeException e) {
                throw new ServiceException(MaintenanceErrorCodeEnum.MAINTENANCE_DEADLINE_TIME_ERROR);
            }
            // 日期不变，时间到23时59分59秒，并减去时区差
            LocalDateTime deadline = LocalDateTime.of(deadlineDate, LocalTime.MAX).plusHours(-req.getZone());
            newMaintenancePlan.setDeadlineTime(deadline.toInstant(ZoneOffset.UTC).toEpochMilli());
        } else if (req.getPlanType().equals(MaintenancePlanTypeEnum.ENGINE_HOURS.getType())) {
            Assert.notNull(req.getUsageHour(), ErrorCode.PARAMETER_NOT_PROVIDED, "usageHour");
            newMaintenancePlan.setUsageHour(req.getUsageHour());
            // 设置刺客的使用时间
            ShadowStatus shadowStatus = shadowStatusService.getByDeviceId(maintenancePlan.getDeviceId(),maintenancePlan.getCompanyId());
            if (shadowStatus != null && shadowStatus.getTotalUsageTime() != null) {
                newMaintenancePlan.setWorkHoursUsed(shadowStatus.getTotalUsageTime());
            } else {
                newMaintenancePlan.setWorkHoursUsed(0);
            }
        }else{
            //nothing to do
        }
        updateById(newMaintenancePlan);
        // 编辑后维保状态设置为在保，如果是关闭则维保状态为off
        LambdaUpdateWrapper<CompanyDevice> wrapper = new LambdaUpdateWrapper<CompanyDevice>()
                .eq(CompanyDevice::getDeviceId, maintenancePlan.getDeviceId())
                .eq(CompanyDevice::getCompanyId, maintenancePlan.getCompanyId());
        if (req.getPlanType().equals(MaintenancePlanTypeEnum.OFF.getType())) {
            wrapper.set(CompanyDevice::getMaintenanceStatus, MaintenanceStatusEnum.OFF.getType());
        } else {
            wrapper.set(CompanyDevice::getMaintenanceStatus, MaintenanceStatusEnum.ON.getType());
        }
        //以下更新人作为维保大数据触发事件判断：event-mts不可修改；
        wrapper.set(CompanyDevice::getModifier,StringConst._EVENT_MAINTENANCE+UserContext.getName());
        companyDeviceService.update(new CompanyDevice(), wrapper);
    }
}