package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.error.DealerErrorCodeEnum;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.po.DealerFavorites;
import com.chervon.fleet.web.service.DealerFavoritesService;
import com.chervon.fleet.web.service.DealerNaService;
import com.chervon.fleet.web.utils.ParseUtil;
import com.chervon.operation.api.RemoteFleetDealerNaService;
import com.chervon.operation.api.dto.AppDealerSearchDto;
import com.chervon.operation.api.vo.AppDealerNaVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/1 10:51
 */
@Service
@Slf4j
public class DealerNaServiceImpl implements DealerNaService {

    @DubboReference
    private RemoteFleetDealerNaService remoteFleetDealerNaService;

    private final DealerFavoritesService dealerFavoritesService;

    public DealerNaServiceImpl(DealerFavoritesService dealerFavoritesService) {
        this.dealerFavoritesService = dealerFavoritesService;
    }

    @Override
    public List<AppDealerNaVo> search(AppDealerSearchDto req) {
        Long companyId = UserContext.getCompanyId();
        if (req.getLat() == null || req.getLng() == null) {
            return null;
        }
        List<AppDealerNaVo> dealerList = remoteFleetDealerNaService.search(UserContext.getClientInfo().getLanguage(), req);
        if (!CollectionUtils.isEmpty(dealerList)) {
            List<DealerFavorites> list = dealerFavoritesService.list(new LambdaQueryWrapper<DealerFavorites>()
                    .eq(DealerFavorites::getCompanyId, companyId));
            if (!CollectionUtils.isEmpty(list)) {
                List<Long> dealerIds = list.stream().map(DealerFavorites::getDealerId).collect(Collectors.toList());
                dealerList.forEach(e -> e.setCollection(dealerIds.contains(e.getDealerId())));
            }
        }
        return dealerList.stream().filter((AppDealerNaVo a) ->{
            double distance;
            try{
                distance = Double.parseDouble(a.getDistance());
            }catch (NumberFormatException e){
                distance=0;
            }
            return distance<=NumberConst.THIRTY;
        }).collect(Collectors.toList());
    }

    @Override
    public AppDealerNaVo detail(Long dealerId) {
        if (dealerId == null) {
            return null;
        }
        return remoteFleetDealerNaService.detail(UserContext.getClientInfo().getLanguage(), dealerId);
    }

    @Override
    public Integer favoriteCount(AppDealerSearchDto req) {
        Long companyId = UserContext.getCompanyId();
        List<DealerFavorites> list = dealerFavoritesService.list(new LambdaQueryWrapper<DealerFavorites>()
                .eq(DealerFavorites::getCompanyId, companyId)
                .select(DealerFavorites::getId,DealerFavorites::getDealerId));
        if(CollectionUtils.isEmpty(list)){
            return 0;
        }
        final List<Long> listDealerIds = list.stream().map(a -> a.getDealerId()).collect(Collectors.toList());
        Integer count= remoteFleetDealerNaService.countByDealerIds(listDealerIds);
        if(count.equals(0) && list.size()>0){//源数据被删除，清除收藏dealer数据
            final List<Long> listIds = list.stream().map(a -> a.getId()).collect(Collectors.toList());
            dealerFavoritesService.removeBatchByIds(listIds);
        }
        return count;
    }

    @Override
    public List<AppDealerNaVo> favorite(AppDealerSearchDto req) {
        Long companyId = UserContext.getCompanyId();
        List<DealerFavorites> list = dealerFavoritesService.list(new LambdaQueryWrapper<DealerFavorites>().eq(DealerFavorites::getCompanyId, companyId));
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        List<Long> dealerIds = list.stream().map(DealerFavorites::getDealerId).distinct().collect(Collectors.toList());
        List<AppDealerNaVo> res = remoteFleetDealerNaService.listByDealerIds(UserContext.getClientInfo().getLanguage(), dealerIds, req.getLat(), req.getLng(), req.getContent());
        res.forEach(e -> {
            e.setCollection(true);
            if(Objects.isNull(e.getDistance())){
                e.setDistance("0");
            }
        });
        return res.stream().sorted(Comparator.comparing(a-> ParseUtil.parseDouble(a.getDistance()))).collect(Collectors.toList());
    }

    @Override
    public void addFavorite(Long dealerId) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(dealerId, ErrorCode.PARAMETER_NOT_PROVIDED, "dealerId");
        List<DealerFavorites> list = dealerFavoritesService.list(new LambdaQueryWrapper<DealerFavorites>()
                .eq(DealerFavorites::getCompanyId, companyId).select(DealerFavorites::getDealerId));
        if (list.size() >= NumberConst.ONE_HUNDRED) {
            throw new ServiceException(DealerErrorCodeEnum.DEALER_FAVORITE_MORE_THAN_100);
        }
        boolean flag = list.stream().anyMatch(e -> Objects.equals(e.getDealerId(), dealerId));
        if (flag) {
            throw new ServiceException(DealerErrorCodeEnum.DEALER_ALREADY_COLLECTED);
        }
        DealerFavorites dealerFavorites = new DealerFavorites();
        dealerFavorites.setCompanyId(companyId).setDealerId(dealerId);
        dealerFavoritesService.save(dealerFavorites);
    }

    @Override
    public void cancelFavorite(Long dealerId) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(dealerId, ErrorCode.PARAMETER_NOT_PROVIDED, "dealerId");
        boolean flag = dealerFavoritesService.remove(new LambdaQueryWrapper<DealerFavorites>()
                .eq(DealerFavorites::getDealerId, dealerId)
                .eq(DealerFavorites::getCompanyId, companyId));
        if (!flag) {
            throw new ServiceException(DealerErrorCodeEnum.DEALER_NOT_FOUND);
        }
    }
}
