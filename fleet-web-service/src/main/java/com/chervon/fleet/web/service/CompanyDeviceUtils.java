package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum;
import com.chervon.fleet.web.api.entity.vo.*;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.RedisConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.GatewayCache;
import com.chervon.fleet.web.entity.po.BiDeviceErrorList;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.DeviceTag;
import com.chervon.fleet.web.entity.po.Tag;
import com.chervon.fleet.web.utils.DataUtils;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 租户设备业务工具类
 * <AUTHOR> 2024/2/20
 */
@Service
@Slf4j
public class CompanyDeviceUtils {
    @Autowired
    private CompanyDeviceService companyDeviceService;
    @Autowired
    private DeviceTagService deviceTagService;
    @Autowired
    private BiDeviceErrorListService biDeviceErrorListService;
    @Autowired
    private TagService tagService;
    @Autowired
    private AppGatewayService appGatewayService;
    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;
    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;
    /**
     * 库存计数，对于状态的实时性不高
     *
     * @param companyId       租户id
     * @param deviceName      设备名称
     * @param categoryCodes   二级品类编码集合
     * @param warehouseStatus 库存状态集合
     * @param tagIds          标签id集合
     * @return 库存计数
     */
    public Long countFilterAndSearch(Long companyId, String deviceName, List<String> categoryCodes, List<Integer> warehouseStatus, List<Long> tagIds) {
        boolean flag = StringUtils.isEmpty(deviceName) && CollectionUtils.isEmpty(warehouseStatus)
                && CollectionUtils.isEmpty(categoryCodes) && CollectionUtils.isEmpty(tagIds);
        if (flag) {
            return 0L;
        }
        LambdaQueryWrapper<CompanyDevice> wrapper = new LambdaQueryWrapper<CompanyDevice>().eq(CompanyDevice::getCompanyId, companyId)
                .like(StringUtils.isNotEmpty(deviceName), CompanyDevice::getDeviceName, deviceName)
                .in(!CollectionUtils.isEmpty(categoryCodes), CompanyDevice::getSecondCategoryCode, categoryCodes)
                .in(!CollectionUtils.isEmpty(warehouseStatus), CompanyDevice::getWarehouseStatus, warehouseStatus);
        List<CompanyDevice> list = companyDeviceService.list(wrapper);
        if (list.isEmpty()) {
            return 0L;
        }
        List<String> deviceIds = list.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tagIds)) {
            List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getTagId, tagIds)
                    .in(DeviceTag::getDeviceId, deviceIds));
            deviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            if (deviceIds.isEmpty()) {
                return 0L;
            }
        }
        return (long) deviceIds.size();
    }

    /**
     * 盘点计数，只统计工作中的，对于状态的实时性高
     *
     * @param companyId       租户id
     * @param gatewayId       网关id
     * @param deviceName      设备名称
     * @param categoryCodes   二级品类编码集合
     * @param reqWarehouseStatus 库存状态集合
     * @param requestTagIds          标签id集合
     * @return 盘点计数
     */
    public Long countFilterAndSearchByGatewayId(Long companyId, String gatewayId, String deviceName, List<String> categoryCodes, List<Integer> reqWarehouseStatus, List<Long> requestTagIds) {
        boolean emptyCondition = StringUtils.isEmpty(deviceName) && CollectionUtils.isEmpty(reqWarehouseStatus)
                && CollectionUtils.isEmpty(categoryCodes) && CollectionUtils.isEmpty(requestTagIds);
        //无筛选条件返回空
        if (emptyCondition) {
            return 0L;
        }
        // 判断库存状态是否有交集
        List<Integer> dicWarehouseStatus = new ArrayList<>(Arrays.asList(WarehouseStatusEnum.IN_WAREHOUSE.getType(), WarehouseStatusEnum.OUT_FOR_WORK.getType()));
        if (!CollectionUtils.isEmpty(reqWarehouseStatus)) {
            dicWarehouseStatus.retainAll(reqWarehouseStatus);
        }
        //筛选的库存条件未在集合内，返回空
        if (dicWarehouseStatus.isEmpty()) {
            return 0L;
        }
        final List<String> deviceIdList = getDeviceIdListByGateway(companyId, gatewayId);
        // 与db数据比较
        List<CompanyDevice> listCompanyDeviceId = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .like(StringUtils.isNotEmpty(deviceName), CompanyDevice::getDeviceName, deviceName)
                .in(!CollectionUtils.isEmpty(categoryCodes), CompanyDevice::getSecondCategoryCode, categoryCodes)
                .in(!CollectionUtils.isEmpty(reqWarehouseStatus), CompanyDevice::getWarehouseStatus, reqWarehouseStatus)
                .in(!CollectionUtils.isEmpty(deviceIdList),CompanyDevice::getDeviceId, deviceIdList)
                .select(CompanyDevice::getDeviceId));
        if (listCompanyDeviceId.isEmpty()) {
            return 0L;
        }
        List<String> deviceIds = listCompanyDeviceId.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(requestTagIds)) {
            List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getDeviceId, deviceIds)
                    .select(DeviceTag::getTagId,DeviceTag::getDeviceId));
            deviceTags.removeIf(e -> !requestTagIds.contains(e.getTagId()));
            deviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            if (deviceIds.isEmpty()) {
                return 0L;
            }
        }
        return (long) deviceIds.size();
    }

    /**
     * 获取一分钟内网关上报的设备id
     * @param companyId 租户
     * @param gatewayId 网关
     * @return 设备id列表
     */
    public List<String> getDeviceIdListByGateway(Long companyId,String gatewayId){
        final String companyGatewayKey = RedisConst.getCompanyGatewayMapKey(companyId, gatewayId);
        final Map<String, Long> gatewayMap = RedisUtils.getCacheMap(companyGatewayKey);
        List<String> listDeviceId=new ArrayList<>();
        final long ts = System.currentTimeMillis();
        for (Map.Entry<String, Long> entry:gatewayMap.entrySet()){
            if(ts-entry.getValue()> NumberConst.TWO_MINUTE_MS){
                RedisUtils.delCacheMapValue(companyGatewayKey,entry.getKey());
            }else{
                listDeviceId.add(entry.getKey());
            }
        }
        return listDeviceId;
    }

    /**
     * 盘点计数，只统计工作中的，对于状态的实时性高（没有网关）
     *
     * @param companyId       租户id
     * @param targetDeviceIds 设备id集合
     * @param deviceName      设备名称
     * @param categoryCodes   二级品类编码集合
     * @param reqWarehouseStatus 库存状态集合
     * @param tagIds          标签id集合
     * @return 盘点计数
     */
    public Long countFilterAndSearchByDeviceIds(Long companyId, List<String> targetDeviceIds, String deviceName, List<String> categoryCodes, List<Integer> reqWarehouseStatus, List<Long> tagIds) {
        boolean emptyCondition = StringUtils.isEmpty(deviceName) && CollectionUtils.isEmpty(reqWarehouseStatus)
                && CollectionUtils.isEmpty(categoryCodes) && CollectionUtils.isEmpty(tagIds) && CollectionUtils.isEmpty(targetDeviceIds);
        //无筛选条件返回空
        if (emptyCondition) {
            return 0L;
        }
        // 判断库存状态是否有交集
        List<Integer> dicWarehouseStatus = new ArrayList<>(Arrays.asList(WarehouseStatusEnum.IN_WAREHOUSE.getType(), WarehouseStatusEnum.OUT_FOR_WORK.getType()));
        if (!CollectionUtils.isEmpty(reqWarehouseStatus)) {
            dicWarehouseStatus.retainAll(reqWarehouseStatus);
        }
        //筛选的库存状态不在在库和外出状态内，返回空
        if (dicWarehouseStatus.isEmpty()) {
            return 0L;
        }
        // 与db数据比较
        List<CompanyDevice> listCompanyDeviceId = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .like(StringUtils.isNotEmpty(deviceName), CompanyDevice::getDeviceName, deviceName)
                .in(!CollectionUtils.isEmpty(categoryCodes), CompanyDevice::getSecondCategoryCode, categoryCodes)
                .in(!CollectionUtils.isEmpty(reqWarehouseStatus), CompanyDevice::getWarehouseStatus, reqWarehouseStatus)
                .in(!CollectionUtils.isEmpty(targetDeviceIds),CompanyDevice::getDeviceId, targetDeviceIds)
                .select(CompanyDevice::getDeviceId));
        if (listCompanyDeviceId.isEmpty()) {
            return 0L;
        }
        List<String> dbDeviceIds = listCompanyDeviceId.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tagIds)) {
            List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getDeviceId, dbDeviceIds)
                    .select(DeviceTag::getTagId,DeviceTag::getDeviceId));
            deviceTags.removeIf(e -> !tagIds.contains(e.getTagId()));
            dbDeviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            if (dbDeviceIds.isEmpty()) {
                return 0L;
            }
        }
        return (long) dbDeviceIds.size();
    }

    /**
     * 处理库存查询
     * @param res 返回对象
     * @param list 设备列表
     * @param deviceStatusMap 设备状态map
     * @param deviceName 设备名称
     * @param categoryCodes 分类编码
     * @param warehouseStatus 仓库状态
     * @param tagIds 标签id
     */
    public void handleInventorySearch(InventorySearchVo res, List<CompanyDevice> list, Map<String, DeviceStatusVo> deviceStatusMap, String deviceName, List<String> categoryCodes, List<Integer> warehouseStatus, List<Long> tagIds) {
        // 装配数据
        // 是否有过滤条件
        boolean flag = StringUtils.isEmpty(deviceName) && CollectionUtils.isEmpty(warehouseStatus)
                && CollectionUtils.isEmpty(categoryCodes) && CollectionUtils.isEmpty(tagIds);
        if (flag) {
            res.setIsFilter(0);
            res.setSearchCount(0L);
        } else {
            res.setIsFilter(1);
            res.setSearchCount((long) list.size());
        }
        if (list.isEmpty()) {
            return;
        }
        // 获取产品数据
        List<ProductCache> products = remoteOperationCacheService.listProducts(list.stream().map(CompanyDevice::getProductId).collect(Collectors.toList()));
        Map<Long, ProductCache> productMap = products.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));
        // 获取品类信息
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCodes(list.stream().map(CompanyDevice::getSecondCategoryCode).collect(Collectors.toList()));
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        Map<String, FleetCategoryListVo> fleetCategoryMap = fleetCategoryList.stream().collect(Collectors.toMap(FleetCategoryListVo::getCode, Function.identity()));
        //获取故障列表信息
        List<String> deviceIds = list.stream().map(CompanyDevice::getDeviceId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<BiDeviceErrorList> deviceErrorList = new ArrayList<>();
        if (!deviceIds.isEmpty()) {
            deviceErrorList = biDeviceErrorListService.list(new LambdaQueryWrapper<BiDeviceErrorList>()
                    .in(BiDeviceErrorList::getDeviceId, deviceIds)
                    .select(BiDeviceErrorList::getDeviceId,BiDeviceErrorList::getModifyTime));
        }
        Map<String, List<BiDeviceErrorList>> deviceFaultMap = deviceErrorList.stream().collect(Collectors.groupingBy(BiDeviceErrorList::getDeviceId));

        // 数据集合
        List<InventoryVo> inventories = new ArrayList<>();
        for (CompanyDevice e : list) {
            InventoryVo vo = new InventoryInfoVo();
            DeviceStatusVo status = deviceStatusMap.get(e.getDeviceId());
            if (status != null) {
                // 数据上报状态
                vo.setReportCompleteStatus(status.getReportCompleteStatus());
            }
            // 库存状态
            vo.setWarehouseStatus(e.getWarehouseStatus());
            // 在线状态
            vo.setOnlineStatus(e.getOnlineStatus());
            vo.setDeviceId(e.getDeviceId());
            vo.setMac(e.getMac());
            vo.setDeviceSn(e.getDeviceSn());
            vo.setDeviceName(e.getDeviceName());
            vo.setCategoryCode(e.getSecondCategoryCode());
            vo.setProductId(e.getProductId());
            // 故障状态
            DataUtils.setDeviceFaultInfo(e, deviceFaultMap, vo);
            // 获取fleet品类信息
            FleetCategoryListVo fleetCategory = fleetCategoryMap.get(e.getSecondCategoryCode());
            if (fleetCategory != null) {
                vo.setCategoryName(fleetCategory.getCategoryName());
            }
            // 设置产品信息
            ProductCache productCache = productMap.get(e.getProductId());
            if (productCache != null) {
                vo.setProductIconUrl(productCache.getUrl());
                vo.setCommodityModel(productCache.getCommodityModel());
            }
            inventories.add(vo);
        }
        res.setInventories(inventories);
    }
    public void setProductInfo(String deviceId, CompanyDevice companyDevice, InventoryInfoVo res) {
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(UserContext.getClientInfo().getLanguage());
        fleetCategoryQuery.setCode(companyDevice.getSecondCategoryCode());
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        if (fleetCategoryList != null && fleetCategoryList.size() > 0) {
            res.setCategoryName(fleetCategoryList.get(0).getCategoryName());
        }
        res.setProductId(companyDevice.getProductId());
        ProductCache product = remoteOperationCacheService.getProduct(companyDevice.getProductId());
        if (product != null) {
            res.setProductIconUrl(product.getUrl());
            res.setCommodityModel(product.getCommodityModel());
            setGatewayInfo(product,res, companyDevice);
        }

        List<DeviceTag> list = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>().eq(DeviceTag::getDeviceId, deviceId)
                        .select(DeviceTag::getTagId)
                .orderByDesc(DeviceTag::getCreateTime));
        setDeviceTag(list, res);
        // 设置故障状态，故障码，故障标题
        biDeviceErrorListService.setDeviceErrorList(deviceId, companyDevice, res);
    }

    private void setDeviceTag(List<DeviceTag> list, InventoryInfoVo res) {
        if (!list.isEmpty()) {
            final List<Long> tagIds = list.stream().map(DeviceTag::getTagId).collect(Collectors.toList());
            List<Tag> tags = tagService.list(new LambdaQueryWrapper<Tag>().in(Tag::getId, tagIds).select(
                    Tag::getId, Tag::getName
            ));
            if (!tags.isEmpty()) {
                int size = tags.size();
                List<TagVo> sortedTag = tags.stream().map(e -> {
                    TagVo vo = new TagVo();
                    vo.setTagId(e.getId()).setTagName(e.getName());
                    return vo;
                }).sorted((o1, o2) -> {
                    return sort(o1, o2, tagIds, size);
                }).collect(Collectors.toList());

                res.setTags(sortedTag);
            }
        }
    }

    private static int sort(TagVo o1, TagVo o2, List<Long> tagIds, int size) {
        int io1 = tagIds.indexOf(o1.getTagId());
        int io2 = tagIds.indexOf(o2.getTagId());
        if (io1 != -1) {
            io1 = size - io1;
        }
        if (io2 != -1) {
            io2 = size - io2;
        }
        return io2 - io1;
    }

    private void setGatewayInfo(ProductCache product, InventoryInfoVo res, CompanyDevice companyDevice) {
        // 判断是否是网关设备
        if (!StringConst.GATEWAY_DEVICE.equals(product.getType())) {
            return;
        }
        // 网关设备取网关信息
        GatewayCache gatewayCache = appGatewayService.getByCache(companyDevice.getDeviceId());
        if (gatewayCache != null) {
            res.setLocation(gatewayCache.getLocation());
            res.setCoordinate(gatewayCache.getCoordinate());
            res.setCanEdit(1);
        } else {
            res.setLocation(null);
            res.setCoordinate(null);
        }
    }

}
