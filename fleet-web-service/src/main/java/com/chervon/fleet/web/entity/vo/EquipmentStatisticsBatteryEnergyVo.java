package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: EquipmentStatisticsBatteryEnergyVo
 * @Description:电池包和充电器响应对象
 * <AUTHOR>
 * @date 2023/7/25 10:19
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsBatteryEnergyVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 无参构造函数
     */
    public EquipmentStatisticsBatteryEnergyVo() {
        this.chargingEnergy = 0;
        this.dischargingEnergy = 0;
    }

    /**
     * 日期yyyy-MM-dd字符串
     */
    @ApiModelProperty("日期yyyy-MM-dd字符串")
    private String date;

    @ApiModelProperty("Charging energy")
    private Integer chargingEnergy;

    @ApiModelProperty("Discharging energy")
    private Integer dischargingEnergy;

}
