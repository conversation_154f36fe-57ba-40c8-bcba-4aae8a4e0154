package com.chervon.fleet.web.controller;

import com.chervon.fleet.web.api.entity.vo.GroupVo;
import com.chervon.fleet.web.entity.dto.GroupDto;
import com.chervon.fleet.web.service.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "设备分组管理")
@RestController
@Slf4j
@RequestMapping("group")
public class GroupController {

    private final GroupService groupService;

    public GroupController(GroupService groupService) {
        this.groupService = groupService;
    }

    @ApiOperation("查询租户下的所有分组")
    @GetMapping("list")
    public List<GroupVo> list() {
        return groupService.listAll();
    }

    @ApiOperation("新建分组")
    @PostMapping("add")
    public void add(@RequestBody GroupDto req) {
        groupService.add(req);
    }

    @ApiOperation("分组详情")
    @GetMapping("detail")
    public GroupVo detail(@RequestParam("groupId") Long groupId) {
        return groupService.detail(groupId);
    }

    @ApiOperation("编辑分组")
    @PostMapping("edit")
    public void edit(@RequestBody GroupDto req) {
        groupService.edit(req);
    }

    @ApiOperation("删除分组")
    @GetMapping("delete")
    public void groupDelete(@RequestParam("groupId") Long groupId) {
        groupService.delete(groupId);
    }

}
