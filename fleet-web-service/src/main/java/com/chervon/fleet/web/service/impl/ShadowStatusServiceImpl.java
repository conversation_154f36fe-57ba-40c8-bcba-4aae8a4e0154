package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.fleet.web.api.entity.enums.*;
import com.chervon.fleet.web.config.FleetDeviceConfig;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.po.CompanyDevice;
import com.chervon.fleet.web.entity.po.ShadowStatus;
import com.chervon.fleet.web.entity.vo.EquipmentVo;
import com.chervon.fleet.web.mapper.ShadowStatusMapper;
import com.chervon.fleet.web.service.ShadowStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/21 11:22
 */
@Service
@Slf4j
public class ShadowStatusServiceImpl extends ServiceImpl<ShadowStatusMapper, ShadowStatus> implements ShadowStatusService {
    @Autowired
    private FleetDeviceConfig fleetDeviceConfig;

    @Override
    public List<ShadowStatus> listByDeviceIds(Collection<String> deviceIds,Long companyId) {
        List<ShadowStatus> list = this.list(new LambdaQueryWrapper<ShadowStatus>()
                .eq(companyId!=null,ShadowStatus::getCompanyId,companyId)
                .in(!CollectionUtils.isEmpty(deviceIds), ShadowStatus::getDeviceId, deviceIds)
                .orderByDesc(ShadowStatus::getModifyTime));
        List<ShadowStatus> res = new ArrayList<>();
        list.stream().collect(Collectors.groupingBy(ShadowStatus::getDeviceId, LinkedHashMap::new, Collectors.toList())).forEach((k, v) -> res.add(v.get(0)));
        return res;
    }

    /**
     * 设置设备的影子状态
     * @param companyDevice
     * @param vo
     */
    public void setShadowStatusValue(CompanyDevice companyDevice, EquipmentVo vo) {
        ShadowStatus shadowStatus = getByDeviceId(companyDevice.getDeviceId(), companyDevice.getCompanyId());
        if (shadowStatus == null) {
            shadowStatus = new ShadowStatus();
        }
        // 设备如果是charger，需要当前充电进度，如果不是，要当前的剩余电量百分比
        vo.setUsageDuration(shadowStatus.getTotalUsageTime());
        vo.setTotalUsageNumber(shadowStatus.getTotalUsageNumber());
        if (companyDevice.getCustomType().intValue() == BizCategoryTypeEnum.CHARGER.getType()) {
            // charger类型
            setChargerState(vo,companyDevice,shadowStatus);
        } else {
            // 工具、电池包：显示电量
            setToolAndBattery(companyDevice, vo, shadowStatus);
        }
    }

    private void setToolAndBattery(CompanyDevice companyDevice, EquipmentVo vo, ShadowStatus shadowStatus) {
        boolean shadowIsExpired12Hours = fleetDeviceConfig.isExpired12Hours(shadowStatus.getModifyTime());
        if (shadowStatus.getEnergy() != null && !shadowIsExpired12Hours) {
            vo.setSpeed(new BigDecimal(shadowStatus.getEnergy()));
            vo.setBatteryHealthState(Objects.isNull(shadowStatus.getBatteryHealth())? CommonConstant.TWO :(shadowStatus.getBatteryHealth() >= NumberConst.FORTY ? CommonConstant.ONE : CommonConstant.TWO));
        }
        //工具类，返回放电状态
        if (companyDevice.getCustomType().intValue() == BizCategoryTypeEnum.TOOL.getType()){
            vo.setBatteryState(CommonConstant.SEVEN);
        }
        //电池包状态
        setBatteryStateValue(companyDevice, vo, shadowStatus, shadowIsExpired12Hours);
    }

    private static void setBatteryStateValue(CompanyDevice companyDevice, EquipmentVo vo, ShadowStatus shadowStatus, boolean shadowIsExpired12Hours) {
        if (companyDevice.getCustomType().intValue() == BizCategoryTypeEnum.BATTERY.getType()
                && shadowStatus.getBatteryState() != null && !shadowIsExpired12Hours) {
            //电池状态2028 1：充电中  2：放电中  3：充电满 4：充电失败  5：空闲 6：过温等待 7：故障  8均衡
            //数据库中通用状态: 1 正常充电 2：等待充电 3：电池过温  4:电池故障   5空闲 6充电完成  -1 电池类型放电
            //前端电池展示状态：1：正常充电 2：等待充电 3：电池过温  4:电池故障  7：放电
            //根据通用状态转换为前端状态
            final CompartmentStatusEnum batteryStateEnum = CompartmentStatusEnum.getEnum(shadowStatus.getBatteryState());
            switch (batteryStateEnum) {
                case Discharging: //放电
                    vo.setBatteryState(CommonConstant.SEVEN);
                    break;
                case Empty: //空闲
                    vo.setBatteryState(CommonConstant.TWO);
                    break;
                case Complete: //充电完成
                    vo.setBatteryState(CommonConstant.ONE);
                    break;
                default: //其他不需要转换：1，2，3，4
                    vo.setBatteryState(shadowStatus.getBatteryState());
                    break;
            }
        }
    }

    /**
     * 设置充电桩状态
     * @param vo
     * @param companyDevice
     * @param shadowStatus
     */
    public void  setChargerState(EquipmentVo vo,CompanyDevice companyDevice,ShadowStatus shadowStatus) {
        if (companyDevice.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_HUB.getCode()) &&
                (Objects.isNull(companyDevice.getOnlineStatus()) || !companyDevice.getOnlineStatus().equals(OnlineStatusEnum.ONLINE.getType()))) {
            return;
        }
        if (Objects.isNull(shadowStatus)) {
            return;
        }
        boolean shadowIsExpired12Hours = fleetDeviceConfig.isExpired12Hours(shadowStatus.getModifyTime());
        if (companyDevice.getSecondCategoryCode().equals(ChargerCategoryEnum.PGX_POWER_BANK.getCode()) &&
                shadowIsExpired12Hours) {
            return;
        }
        vo.setSpeed(new BigDecimal(shadowStatus.getEnergy()));
        //数据库中通用状态: 1 正常充电 2等待充电 3电池过温 4电池故障 5空闲 6充电完成  (-1 电池类型放电  -4全部设备故障)
        //前端充电器状态    1: 充电中  2: 等待充电 3: 过温 4:故障
        //根据通用状态转换为前端状态
        final CompartmentStatusEnum chargerState = CompartmentStatusEnum.getEnum(shadowStatus.getChargerState());
        switch (chargerState) {
            case Empty: //空闲
                vo.setChargerState(CommonConstant.TWO);
                break;
            case Complete: //充电中
                vo.setChargerState(CommonConstant.ONE);
                break;
            case ALL_Fault: //全部故障
                vo.setChargerState(CommonConstant.FOUR);
                break;
            default:
                vo.setChargerState(shadowStatus.getChargerState());
                break;
        }
    }
}
