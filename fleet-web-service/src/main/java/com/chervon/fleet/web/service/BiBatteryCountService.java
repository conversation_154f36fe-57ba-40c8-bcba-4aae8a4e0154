package com.chervon.fleet.web.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiBatteryCount;
import com.chervon.fleet.web.entity.vo.dashboard.BatteryCountDashboardVo;

import java.util.List;

/**
 * 电池数量统计（看板：total batteries饼状图）服务接口
 *
 * <AUTHOR>
 * @since 2023-07-27 13:57:59
 * @description 
 */
public interface BiBatteryCountService  extends IService<BiBatteryCount> {

    /**
     * 根据companyId获取PO列表
     * @param companyId 租户ID
     * @return PO列表
     */
    List<BiBatteryCount> listByCompanyId(Long companyId);

    /**
     * 电池数量统计看板查询
     * @param companyId 公司ID
     * @return 查询结果
     */
    List<BatteryCountDashboardVo> batteryCount(Long companyId);
}
