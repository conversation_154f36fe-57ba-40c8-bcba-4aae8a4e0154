package com.chervon.fleet.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chervon.fleet.web.entity.po.DailyToolUsage;
import com.chervon.fleet.web.entity.po.DailyToolUsageCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-14 17:28
 **/
@Mapper
public interface DailyToolUsageMapper extends BaseMapper<DailyToolUsage> {
    /**
     * * 按日期和分类分组汇总统计各分类的设备使用情况
     * @param date
     * @return
     */
    List<DailyToolUsageCount> countDailyToolUsageByDate(@Param("date") String date, @Param("companyId")Long companyId);
}
