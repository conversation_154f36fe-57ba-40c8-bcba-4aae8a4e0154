package com.chervon.fleet.web.controller;

import com.chervon.fleet.web.api.entity.vo.TagVo;
import com.chervon.fleet.web.entity.dto.TagDto;
import com.chervon.fleet.web.service.TagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Api(tags = "设备标签tag管理")
@RestController
@Slf4j
@RequestMapping("tag")
public class TagController {

    private final TagService tagService;

    public TagController(TagService tagService) {
        this.tagService = tagService;
    }

    @ApiOperation("查询指定分组下的标签列表")
    @GetMapping("list")
    public List<TagVo> list(@RequestParam("groupId") Long groupId) {
        return tagService.listByGroupId(groupId);
    }

    @ApiOperation("新建标签")
    @PostMapping("add")
    public void add(@RequestBody TagDto req) {
        tagService.add(req);
    }

    @ApiOperation("标签详情")
    @GetMapping("detail")
    public TagVo detail(@RequestParam("tagId") Long tagId) {
        return tagService.detail(tagId);
    }

    @ApiOperation("编辑标签")
    @PostMapping("edit")
    public void edit(@RequestBody TagDto req) {
        tagService.edit(req);
    }

    @ApiOperation("删除标签")
    @GetMapping("delete")
    public void delete(@RequestParam("tagId") Long tagId) {
        tagService.delete(tagId);
    }


}
