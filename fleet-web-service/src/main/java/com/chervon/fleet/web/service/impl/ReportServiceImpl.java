package com.chervon.fleet.web.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageRequest;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.CsvUtil;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.entity.I18nEnumAttribute;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.enums.DeleteStatusEnum;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;
import com.chervon.fleet.user.api.service.RemoteCompanyService;
import com.chervon.fleet.web.api.entity.enums.BizCategoryTypeEnum;
import com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum;
import com.chervon.fleet.web.entity.consts.NumberConst;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.ReportInventoryDto;
import com.chervon.fleet.web.entity.dto.ReportMaintenancePageDto;
import com.chervon.fleet.web.entity.dto.ReportUsagePageDto;
import com.chervon.fleet.web.entity.po.*;
import com.chervon.fleet.web.entity.vo.ReportInventoryVo;
import com.chervon.fleet.web.entity.vo.ReportMaintenanceVo;
import com.chervon.fleet.web.entity.vo.ReportUsageVo;
import com.chervon.fleet.web.entity.vo.WebFilterConditionVo;
import com.chervon.fleet.web.service.*;
import com.chervon.fleet.web.utils.DateUtil;
import com.chervon.operation.api.RemoteFleetCategoryService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.query.FleetCategoryQuery;
import com.chervon.operation.api.vo.FleetCategoryListVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.entity.ContentType;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/25 11:10
 */
@Service
@Slf4j
public class ReportServiceImpl implements ReportService {
    /**
     * 数据库中日使用数据通用存储格式
     */
    private static final String DATE_YMD_PATTERN = "yyyy-MM-dd";
    private static final String COMPANY_NAME ="Company Name:";
    private static final String CATEGORY = "category";
    private static final String MODEL="model";
    private static final String HOURS = " hours";
    private static final String UTF_8 = "UTF-8";
    private static final String EU="eu";
    @Autowired
    private WebFilterConditionService webFilterConditionService;

    @Autowired
    private CompanyDeviceService companyDeviceService;

    @DubboReference
    private RemoteCompanyService remoteCompanyService;

    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    @DubboReference
    private RemoteFleetCategoryService remoteFleetCategoryService;

    @Autowired
    private MaintenanceLogService maintenanceLogService;

    @Autowired
    private DeviceTagService deviceTagService;

    @Autowired
    private DeviceBindLogService deviceBindLogService;

    @Autowired
    private DailyToolUsageService dailyToolUsageService;

    @Autowired
    private DailyChargerUsageService dailyChargerUsageService;

    @Autowired
    private DailyBatteryUsageService dailyBatteryUsageService;

    @Autowired
    private TagService tagService;

    @Autowired
    private ShadowStatusService shadowStatusService;

    private List<CompanyDevice> listReportData(Long companyId) {
        List<CompanyDevice> companyDevices = companyDeviceService.listWithDeletedByCompanyId(companyId);
        if (CollectionUtils.isEmpty(companyDevices)) {
            return new ArrayList<>();
        }
        Map<Integer, List<CompanyDevice>> deletedGroup = companyDevices.stream().collect(Collectors.groupingBy(CompanyDevice::getIsDeleted));
        List<CompanyDevice> bind = deletedGroup.getOrDefault(0, new ArrayList<>());
        Map<String, CompanyDevice> bindDeviceIdMap = bind.stream().collect(Collectors.toMap(CompanyDevice::getDeviceId, Function.identity(), (k1, k2) -> k1, LinkedHashMap::new));
        List<CompanyDevice> list = new ArrayList<>(bindDeviceIdMap.values());
        List<String> deviceIds = list.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());

        List<CompanyDevice> unbind = deletedGroup.getOrDefault(1, new ArrayList<>());
        Map<String, CompanyDevice> unbindDeviceIdMap = unbind.stream().filter(e -> !deviceIds.contains(e.getDeviceId()))
                .collect(Collectors.toMap(CompanyDevice::getDeviceId, Function.identity(), (k1, k2) ->{
                    return k1.getBindingTime()>k2.getBindingTime()?k1:k2;
                }, LinkedHashMap::new));

        final List<CompanyDevice> deletedDevices = unbindDeviceIdMap.values().stream().sorted(Comparator.comparing(CompanyDevice::getBindingTime).reversed()).collect(Collectors.toList());
        list.addAll(deletedDevices);
        return list;
    }

    @Override
    public PageResult<ReportInventoryVo> inventoryPage(PageRequest req) {
        Long companyId = UserContext.getCompanyId();
        List<CompanyDevice> list = listReportData(companyId);
        if (CollectionUtils.isEmpty(list)) {
            return new PageResult<>(req.getPageNum(), req.getPageSize());
        }

        PageResult<ReportInventoryVo> res = new PageResult<>(req.getPageNum(), req.getPageSize(), list.size());
        List<CompanyDevice> subList = ListUtil.page((int) res.getPageNum() - 1, (int) res.getPageSize(), list);
        List<ProductCache> productCaches = remoteOperationCacheService.listProducts(subList.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));

        final List<String> categoryCodes = list.stream().map(CompanyDevice::getSecondCategoryCode).distinct().collect(Collectors.toList());
        final Map<String, FleetCategoryListVo> categoryMap = getFleetCategoryMap(categoryCodes);

        List<I18nEnumAttribute> warehouseStatusEnum = I18nController.getEnum(UserContext.getLanguage(), StringConst.WAREHOUSE_STATUS_ENUM);
        Map<String, I18nEnumAttribute> warehouseStatusEnumMap = warehouseStatusEnum.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));

        List<ReportInventoryVo> collect = subList.stream().map(e -> {
            return getReportInventoryVo(e, productCacheMap, categoryMap, warehouseStatusEnumMap);
        }).collect(Collectors.toList());
        res.setList(collect);
        return res;
    }

    @NotNull
    private static ReportInventoryVo getReportInventoryVo(CompanyDevice e, Map<Long, ProductCache> productCacheMap, Map<String, FleetCategoryListVo> categoryMap, Map<String, I18nEnumAttribute> warehouseStatusEnumMap) {
        ReportInventoryVo vo = new ReportInventoryVo();
        ProductCache productCache = productCacheMap.getOrDefault(e.getProductId(), new ProductCache());
        vo.setPicture(productCache.getUrl());
        vo.setName(e.getDeviceName());
        vo.setSn(e.getDeviceSn());
        final FleetCategoryListVo fleetCategoryListVo = categoryMap.get(e.getSecondCategoryCode());
        vo.setCategory(fleetCategoryListVo==null? e.getSecondCategoryCode():fleetCategoryListVo.getCategoryName());
        vo.setModel(productCache.getCommodityModel());
        if (e.getIsDeleted().intValue() == DeleteStatusEnum.DELETE.getType()) {
            vo.setStatus(warehouseStatusEnumMap.getOrDefault(WarehouseStatusEnum.DELETED.getType() + "", new I18nEnumAttribute()).getDesc());
        } else if (e.getIsDeleted().intValue() == DeleteStatusEnum.NORMAL.getType() && e.getWarehouseStatus() != null) {
            vo.setStatus(warehouseStatusEnumMap.getOrDefault(e.getWarehouseStatus() + "", new I18nEnumAttribute()).getDesc());
        }else{
            // nothing to do
        }
        return vo;
    }

    @Override
    public void inventoryExport(ReportInventoryDto req, HttpServletResponse response) throws IOException {
        final int zone = req.getZone();
        Long companyId = UserContext.getCompanyId();
        List<CompanyDevice> list = listReportData(companyId);

        List<ProductCache> productCaches = remoteOperationCacheService.listProducts(list.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));
        final List<String> listSecondCategory = list.stream().map(CompanyDevice::getSecondCategoryCode).collect(Collectors.toList());
        final Map<String, FleetCategoryListVo> categoryMap = getFleetCategoryMap(listSecondCategory);

        List<I18nEnumAttribute> warehouseStatusEnum = I18nController.getEnum(UserContext.getLanguage(), StringConst.WAREHOUSE_STATUS_ENUM);
        Map<String, I18nEnumAttribute> warehouseStatusEnumMap = warehouseStatusEnum.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));

        List<List<String>> data = new ArrayList<>();
        // 第一行，公司名称，导出时间
        List<String> one = new ArrayList<>();
        one.add(COMPANY_NAME);
        one.add(Optional.ofNullable(remoteCompanyService.getDetail(companyId)).orElse(new CompanyVo()).getCompanyName());
        one.add(null);
        one.add(null);
        String format= getTimePatternByRegion(req.getRegion()).format(LocalDateTime.now());
        String s = (format + " UTC") + (zone > 0 ? ("+" + zone) : zone);
        one.add(s);
        data.add(one);
        // 第二行，表头
        List<String> two = new ArrayList<>();
        List<I18nEnumAttribute> inventoryTitle = I18nController.getEnum(UserContext.getLanguage(), StringConst.INVENTORY_TITLE);
        Map<String, I18nEnumAttribute> inventoryTitleMap = inventoryTitle.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
        two.add(inventoryTitleMap.getOrDefault("name", new I18nEnumAttribute()).getDesc());
        two.add(inventoryTitleMap.getOrDefault("sn", new I18nEnumAttribute()).getDesc());
        two.add(inventoryTitleMap.getOrDefault(CATEGORY, new I18nEnumAttribute()).getDesc());
        two.add(inventoryTitleMap.getOrDefault(MODEL, new I18nEnumAttribute()).getDesc());
        two.add(inventoryTitleMap.getOrDefault("status", new I18nEnumAttribute()).getDesc());
        data.add(two);
        // 增加数据
        list.forEach(e -> {
            final List<String> d = getDeviceInfo(e, categoryMap, productCacheMap, warehouseStatusEnumMap);
            data.add(d);
        });
        export(data, response, "INVENTORY");
    }

    @NotNull
    private static List<String> getDeviceInfo(CompanyDevice e, Map<String, FleetCategoryListVo> categoryMap, Map<Long, ProductCache> productCacheMap, Map<String, I18nEnumAttribute> warehouseStatusEnumMap) {
        List<String> d = new ArrayList<>();
        d.add(e.getDeviceName());
        d.add(e.getDeviceSn());
        d.add(categoryMap.getOrDefault(e.getSecondCategoryCode(), new FleetCategoryListVo()).getCategoryName());
        d.add(productCacheMap.getOrDefault(e.getProductId(), new ProductCache()).getCommodityModel());
        if (e.getIsDeleted() == 1) {
            d.add(warehouseStatusEnumMap.getOrDefault(WarehouseStatusEnum.DELETED.getType() + "", new I18nEnumAttribute()).getDesc());
        } else if (e.getIsDeleted() == 0 && e.getWarehouseStatus() != null) {
            d.add(warehouseStatusEnumMap.getOrDefault(e.getWarehouseStatus() + "", new I18nEnumAttribute()).getDesc());
        } else {
            d.add(null);
        }
        return d;
    }

    @Override
    public WebFilterConditionVo usageFilterCondition() {
        return webFilterConditionService.filterCondition(UserContext.getCompanyId(), false, true, true, false, false, null);
    }

    private List<ReportUsageVo> listReportUsage(Long companyId, ReportUsagePageDto req) {
        //tag标签过滤
        List<String> tagDeviceIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(req.getTagIds())) {
            List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>()
                    .eq(DeviceTag::getCompanyId, companyId)
                    .in(DeviceTag::getTagId, req.getTagIds()));
            tagDeviceIds = deviceTags.stream().map(DeviceTag::getDeviceId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tagDeviceIds)) {
                return new ArrayList<>();
            }
        }
        List<String> deviceIds = new ArrayList<>();
        //已绑定设备
        List<CompanyDevice>  companyDevices = companyDeviceService.list(new LambdaQueryWrapper<CompanyDevice>()
                .eq(CompanyDevice::getCompanyId, companyId)
                .in(!CollectionUtils.isEmpty(req.getCategoryCodes()), CompanyDevice::getSecondCategoryCode, req.getCategoryCodes())
                .in(!CollectionUtils.isEmpty(tagDeviceIds), CompanyDevice::getDeviceId, tagDeviceIds));
        if (!CollectionUtils.isEmpty(companyDevices)) {
            deviceIds = companyDevices.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        }
        //已删除设备
        final List<CompanyDevice> listDeletedCompany = companyDeviceService.selectDeletedList(companyId,req.getCategoryCodes(),tagDeviceIds,deviceIds);
        if (!CollectionUtils.isEmpty(listDeletedCompany)) {
            companyDevices.addAll(listDeletedCompany);
            deviceIds.addAll(listDeletedCompany.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList()));
        }
        // 最终判断是否有主数据
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        //所有已绑定和已删除的设备排序
        companyDevices = companyDevices.stream().sorted(Comparator.comparing(CompanyDevice::getIsDeleted).reversed().thenComparing(CompanyDevice::getBindingTime).reversed()).collect(Collectors.toList());

        final List<ReportUsageVo> list = getReportUsageVos(companyId, req, deviceIds, companyDevices);
        return list;
    }

    @NotNull
    private List<ReportUsageVo> getReportUsageVos(Long companyId, ReportUsagePageDto req, List<String> deviceIds, List<CompanyDevice> companyDevices) {
        // 根据设备id查询分组信息
        List<DeviceTag> deviceTags = deviceTagService.list(new LambdaQueryWrapper<DeviceTag>().eq(DeviceTag::getCompanyId, companyId)
                .in(DeviceTag::getDeviceId, deviceIds));
        Map<String, List<DeviceTag>> deviceTagGroup = deviceTags.stream().collect(Collectors.groupingBy(DeviceTag::getDeviceId));

        Map<Long, Tag> tagMap = new ConcurrentHashMap<>();
        if (!CollectionUtils.isEmpty(deviceTags)) {
            List<Tag> tags = tagService.listByIds(deviceTags.stream().map(DeviceTag::getTagId).distinct().collect(Collectors.toList()));
            tagMap = tags.stream().collect(Collectors.toMap(Tag::getId, Function.identity(), (k1, k2) -> k2));
        }
        //获取产品信息字典列表
        List<ProductCache> productCaches = remoteOperationCacheService.listProducts(companyDevices.stream().map(CompanyDevice::getProductId)
                .distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));

        // 按工具、充电器、电池分组
        Map<Integer, List<CompanyDevice>> deviceCustomTypeGroup = companyDevices.stream().collect(Collectors.groupingBy(CompanyDevice::getCustomType));
        // 工具
        List<CompanyDevice> tool = deviceCustomTypeGroup.getOrDefault(BizCategoryTypeEnum.TOOL.getType(), new ArrayList<>());
        List<String> toolDeviceIds = tool.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        // 充电器
        List<CompanyDevice> charger = deviceCustomTypeGroup.getOrDefault(BizCategoryTypeEnum.CHARGER.getType(), new ArrayList<>());
        List<String> chargerDeviceIds = charger.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        // 电池
        List<CompanyDevice> battery = deviceCustomTypeGroup.getOrDefault(BizCategoryTypeEnum.BATTERY.getType(), new ArrayList<>());
        List<String> batteryDeviceIds = battery.stream().map(CompanyDevice::getDeviceId).distinct().collect(Collectors.toList());
        //获取设备二级分类字典
        final List<String> listSecondCategory = companyDevices.stream().map(CompanyDevice::getSecondCategoryCode).distinct().collect(Collectors.toList());
        final Map<String, FleetCategoryListVo> fleetCategoryMap = getFleetCategoryMap(listSecondCategory);

        List<ReportUsageVo> list = new ArrayList<>();
        Map<Long, Tag> finalTagMap = tagMap;
        // 获取每个设备的总使用时间
        List<ShadowStatus> shadowStatuses = shadowStatusService.listByDeviceIds(deviceIds, companyId);
        Map<String, ShadowStatus> shadowStatusMap = shadowStatuses.stream().collect(Collectors.toMap(ShadowStatus::getDeviceId, Function.identity(), (k1, k2) -> k2));
        for(CompanyDevice companyDevice : companyDevices){
            buildResponse(companyId, req, companyDevice, list, productCacheMap, fleetCategoryMap, deviceTagGroup, finalTagMap, shadowStatusMap, toolDeviceIds, chargerDeviceIds, batteryDeviceIds);
        }
        return list;
    }

    private void buildResponse(Long companyId, ReportUsagePageDto req, CompanyDevice companyDevice, List<ReportUsageVo> list, Map<Long, ProductCache> productCacheMap, Map<String, FleetCategoryListVo> fleetCategoryMap, Map<String, List<DeviceTag>> deviceTagGroup, Map<Long, Tag> finalTagMap, Map<String, ShadowStatus> shadowStatusMap, List<String> toolDeviceIds, List<String> chargerDeviceIds, List<String> batteryDeviceIds) {
        String deviceId = companyDevice.getDeviceId();
        ReportUsageVo vo = new ReportUsageVo();
        list.add(vo);
        //设置产品信息
        ProductCache productCache = productCacheMap.getOrDefault(companyDevice.getProductId(), new ProductCache());
        vo.setPicture(productCache.getUrl());
        vo.setName(companyDevice.getDeviceName());
        vo.setSn(companyDevice.getDeviceSn());
        //赋值二级分类名称
        vo.setCategory(fleetCategoryMap.getOrDefault(companyDevice.getSecondCategoryCode(), new FleetCategoryListVo()).getCategoryName());
        vo.setModel(productCache.getCommodityModel());
        //设置tag标签

        vo.setTags(deviceTagGroup.getOrDefault(deviceId, new ArrayList<>())
                .stream().sorted(Comparator.comparing(DeviceTag::getCreateTime).reversed())
                .map(e -> finalTagMap.getOrDefault(e.getTagId(), new Tag()).getName())
                .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        //总使用时长
        vo.setTotalUsageTime(convertSecondsToTime(Optional.ofNullable(shadowStatusMap.getOrDefault(deviceId, new ShadowStatus()).getTotalUsageTime()).orElse(0)));
        String startDate= getStrDateOfZone(req.getStart(), DATE_YMD_PATTERN);
        String endDate= getStrDateOfZone(req.getEnd(), DATE_YMD_PATTERN);
        //工具
        if (toolDeviceIds.contains(deviceId)) {
            setToolUsageInfo(companyId, deviceId, startDate, endDate, vo);
        } else if (chargerDeviceIds.contains(deviceId)) {
            //充电器
            setChargerUsageInfo(companyId, deviceId, startDate, endDate, vo);
        } else if (batteryDeviceIds.contains(deviceId)) {
            // 电池
            setBatteryUsageInfo(companyId, deviceId, startDate, endDate, vo);
        }else{
            // nothing to do
        }
    }

    private void setBatteryUsageInfo(Long companyId, String deviceId, String startDate, String endDate, ReportUsageVo vo) {
        List<DailyBatteryUsage> dailyBatteryUsages = dailyBatteryUsageService.list(new LambdaQueryWrapper<DailyBatteryUsage>()
                .eq(DailyBatteryUsage::getCompanyId, companyId)
                .eq(DailyBatteryUsage::getStatus, 0)
                .eq(DailyBatteryUsage::getDeviceId, deviceId)
                .ge(DailyBatteryUsage::getDate, startDate)
                .le(DailyBatteryUsage::getDate, endDate));
        if (!CollectionUtils.isEmpty(dailyBatteryUsages)) {
            //总秒数
            Integer sumUsageTime = dailyBatteryUsages.stream().map(DailyBatteryUsage::getDailyUsageTime).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
            vo.setDailyAverageUsageTime(convertSecondsToTime(sumUsageTime / dailyBatteryUsages.size()));
            vo.setUsageTimeSelectedRange(convertSecondsToTime(sumUsageTime));
            vo.setDataStartTime(dailyBatteryUsages.stream().map(DailyBatteryUsage::getCreateTime).filter(Objects::nonNull).min(Long::compareTo).orElse(null));
            vo.setDataEndTime(dailyBatteryUsages.stream().map(DailyBatteryUsage::getModifyTime).filter(Objects::nonNull).max(Long::compareTo).orElse(null));
        }
    }

    private void setChargerUsageInfo(Long companyId, String deviceId, String startDate, String endDate, ReportUsageVo vo) {
        List<DailyChargerUsage> dailyChargerUsages = dailyChargerUsageService.list(new LambdaQueryWrapper<DailyChargerUsage>()
                .eq(DailyChargerUsage::getCompanyId, companyId)
                .eq(DailyChargerUsage::getStatus, 0)
                .eq(DailyChargerUsage::getDeviceId, deviceId)
                .ge(DailyChargerUsage::getDate, startDate)
                .le(DailyChargerUsage::getDate, endDate));
        if (!CollectionUtils.isEmpty(dailyChargerUsages)) {
            Integer sumChargingTime = dailyChargerUsages.stream().map(DailyChargerUsage::getChargingTime).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
            vo.setDailyAverageUsageTime(convertSecondsToTime(sumChargingTime / dailyChargerUsages.size()));
            vo.setUsageTimeSelectedRange(convertSecondsToTime(sumChargingTime));
            vo.setDataStartTime(dailyChargerUsages.stream().map(DailyChargerUsage::getCreateTime).filter(Objects::nonNull).min(Long::compareTo).orElse(null));
            vo.setDataEndTime(dailyChargerUsages.stream().map(DailyChargerUsage::getModifyTime).filter(Objects::nonNull).max(Long::compareTo).orElse(null));
        }
    }

    private void setToolUsageInfo(Long companyId, String deviceId, String startDate, String endDate, ReportUsageVo vo) {
        List<DailyToolUsage> dailyToolUsages = dailyToolUsageService.list(new LambdaQueryWrapper<DailyToolUsage>()
                .eq(DailyToolUsage::getCompanyId, companyId)
                .eq(DailyToolUsage::getDeviceId, deviceId)
                .ge(DailyToolUsage::getDate, startDate)
                .le(DailyToolUsage::getDate, endDate));
        if (!CollectionUtils.isEmpty(dailyToolUsages)) {
            Integer sumDuration = dailyToolUsages.stream().map(DailyToolUsage::getUsageDuration).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
            vo.setDailyAverageUsageTime(convertSecondsToTime(sumDuration/dailyToolUsages.size()));
            vo.setUsageTimeSelectedRange(convertSecondsToTime(sumDuration));
            vo.setDataStartTime(dailyToolUsages.stream().map(DailyToolUsage::getCreateTime).filter(Objects::nonNull).min(Long::compareTo).orElse(null));
            vo.setDataEndTime(dailyToolUsages.stream().map(DailyToolUsage::getModifyTime).filter(Objects::nonNull).max(Long::compareTo).orElse(null));
        }
    }

    @Override
    public PageResult<ReportUsageVo> usagePage(ReportUsagePageDto req) {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.notNull(req.getStart(), ErrorCode.PARAMETER_NOT_PROVIDED, "start");
        Assert.notNull(req.getEnd(), ErrorCode.PARAMETER_NOT_PROVIDED, "end");
        List<ReportUsageVo> list = listReportUsage(companyId, req);
        if (CollectionUtils.isEmpty(list)) {
            return new PageResult<>(req.getPageNum(), req.getPageSize());
        }
        PageResult<ReportUsageVo> res = new PageResult<>(req.getPageNum(), req.getPageSize(), list.size());
        res.setList(ListUtil.page((int) res.getPageNum() - 1, (int) res.getPageSize(), list));
        return res;
    }

    @Override
    public void usageExport(ReportUsagePageDto req, HttpServletResponse response) throws IOException {
        Long companyId = UserContext.getCompanyId();
        Assert.notNull(req, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.notNull(req.getStart(), ErrorCode.PARAMETER_NOT_PROVIDED, "start");
        Assert.notNull(req.getEnd(), ErrorCode.PARAMETER_NOT_PROVIDED, "end");
        List<ReportUsageVo> list = listReportUsage(companyId, req);
        final List<List<String>> data = getExportHeader(req, companyId);
        // 第三行，表头
        List<String> three = new ArrayList<>();
        List<I18nEnumAttribute> usageTitle = I18nController.getEnum(UserContext.getLanguage(), StringConst.USAGE_TITLE);
        Map<String, I18nEnumAttribute> usageTitleMap = usageTitle.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
        three.add(usageTitleMap.getOrDefault("name", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("sn", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault(CATEGORY, new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault(MODEL, new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("tag", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("dailyAverageUsageTime", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("usageTimeSelectedRange", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("totalUsageTime", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("dataStartTime", new I18nEnumAttribute()).getDesc());
        three.add(usageTitleMap.getOrDefault("dataEndTime", new I18nEnumAttribute()).getDesc());
        data.add(three);
        // 增加数据
        list.forEach(e -> {
            final List<String> d = getReportHead(req, e);
            data.add(d);
        });
        export(data, response, "EQUIPMENT USAGE");
    }

    @NotNull
    private List<List<String>> getExportHeader(ReportUsagePageDto req, Long companyId) {
        List<List<String>> data = new ArrayList<>();
        // 第一行，公司名称，导出时间
        List<String> one = new ArrayList<>();
        one.add(COMPANY_NAME);
        one.add(Optional.ofNullable(remoteCompanyService.getDetail(companyId)).orElse(new CompanyVo()).getCompanyName());
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        String format= getTimePatternByRegion(req.getRegion()).format(LocalDateTime.now());
        String s = (format + " UTC") + (req.getZone() > 0 ? ("+" + req.getZone()) : req.getZone());
        one.add(s);
        data.add(one);
        // 第二行，选择时间跨度
        List<String> two = new ArrayList<>();
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add("Time range:");
        String range = "";
        if (req.getStart() != null) {
            range += getStrDateByRegionZone(req.getStart(), req.getRegion());
        }
        range += "~";
        if (req.getEnd() != null) {
            range += getStrDateByRegionZone(req.getEnd(), req.getRegion());
        }
        two.add(range);
        data.add(two);
        return data;
    }

    @NotNull
    private static List<String> getReportHead(ReportUsagePageDto req, ReportUsageVo e) {
        List<String> d = new ArrayList<>();
        d.add(e.getName());
        d.add(e.getSn());
        d.add(e.getCategoryName());
        d.add(e.getModel());
        d.add(CollectionUtils.isEmpty(e.getTags()) ? null : e.getTags().stream().collect(Collectors.joining(",", "[", "]")));
        d.add(e.getDailyAverageUsageTime());
        d.add(e.getUsageTimeSelectedRange());
        d.add(e.getTotalUsageTime());
        if (e.getDataStartTime() != null) {
            d.add(getStrTimeOfZone(e.getDataStartTime(), req.getRegion()));
        } else {
            d.add(null);
        }
        if (e.getDataEndTime() != null) {
            d.add(getStrTimeOfZone(e.getDataEndTime(), req.getRegion()));
        } else {
            d.add(null);
        }
        return d;
    }

    @NotNull
    private Map<String, FleetCategoryListVo> getFleetCategoryMap(List<String> list) {
        String language = UserContext.getClientInfo().getLanguage();
        if(StringUtils.isEmpty(language)){
            language=I18nController.DEFAULT_LANGUAGE;
        }
        FleetCategoryQuery fleetCategoryQuery = new FleetCategoryQuery();
        fleetCategoryQuery.setLang(language);
        fleetCategoryQuery.setCodes(list);
        List<FleetCategoryListVo> fleetCategoryList = remoteFleetCategoryService.list(fleetCategoryQuery);
        Map<String, FleetCategoryListVo> categoryMap = fleetCategoryList.stream().collect(Collectors.toMap(FleetCategoryListVo::getCode, Function.identity()));
        return categoryMap;
    }

    private static String convertSecondsToTime(int seconds) {
        int hours = seconds / NumberConst.ONE_HOUR_SECOND;
        int minutes = (seconds % NumberConst.ONE_HOUR_SECOND) / NumberConst.ONE_MINUTE_SECOND;
        int remainingSeconds = (seconds % NumberConst.ONE_HOUR_SECOND) % NumberConst.ONE_MINUTE_SECOND;
        if(remainingSeconds>=NumberConst.THIRTY){
            minutes+=1;
        }
        if(hours==0){
            return minutes + " min";
        }else{
            return hours + " hour " + minutes + " min";
        }
    }

    @Override
    public WebFilterConditionVo maintenanceFilterCondition() {
        return webFilterConditionService.filterCondition(UserContext.getCompanyId(), false, true, false, false, false, null);
    }

    @Override
    public PageResult<ReportMaintenanceVo> maintenancePage(ReportMaintenancePageDto req) {
        Long companyId = UserContext.getCompanyId();
        List<CompanyDevice> companyDevices = Optional.ofNullable(companyDeviceService.listWithDeletedByCompanyId(companyId)).orElse(new ArrayList<>());
        if (!CollectionUtils.isEmpty(req.getCategoryCodes())) {
            companyDevices.removeIf(e -> !req.getCategoryCodes().contains(e.getSecondCategoryCode()));
        }
        Map<String, CompanyDevice> deviceMap = companyDevices.stream().collect(Collectors.toMap(CompanyDevice::getDeviceId, Function.identity(), (k1, k2) -> k1, LinkedHashMap::new));
        LambdaQueryWrapper<MaintenanceLog> wrapper = new LambdaQueryWrapper<MaintenanceLog>().eq(MaintenanceLog::getCompanyId, companyId)
                .le(req.getEnd() != null, MaintenanceLog::getServiceTime, req.getEnd())
                .ge(req.getStart() != null, MaintenanceLog::getServiceTime, req.getStart());
        if (!deviceMap.isEmpty()) {
            wrapper.in(MaintenanceLog::getDeviceId, deviceMap.keySet());
        }
        wrapper.orderByDesc(MaintenanceLog::getServiceTime).orderByDesc(MaintenanceLog::getCreateTime);
        Page<MaintenanceLog> page = maintenanceLogService.page(new Page<>(req.getPageNum(), req.getPageSize()), wrapper);

        List<ProductCache> productCaches = remoteOperationCacheService.listProducts(companyDevices.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));

        List<I18nEnumAttribute> maintenanceLogServiceTypeEnum = I18nController.getEnum(UserContext.getLanguage(), StringConst.MAINTENANCE_LOG_SERVICE_TYPE_ENUM);
        Map<String, I18nEnumAttribute> maintenanceLogServiceTypeEnumMap = maintenanceLogServiceTypeEnum.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
        final List<String> listSecondCategory = companyDevices.stream().map(CompanyDevice::getSecondCategoryCode).distinct().collect(Collectors.toList());
        final Map<String, FleetCategoryListVo> categoryMap = getFleetCategoryMap(listSecondCategory);

        PageResult<ReportMaintenanceVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords().stream().map(e -> {
            return getReportMaintenanceVo(e, deviceMap, productCacheMap, categoryMap, maintenanceLogServiceTypeEnumMap);
        }).collect(Collectors.toList()));
        return res;
    }

    @NotNull
    private static ReportMaintenanceVo getReportMaintenanceVo(MaintenanceLog e, Map<String, CompanyDevice> deviceMap, Map<Long, ProductCache> productCacheMap, Map<String, FleetCategoryListVo> categoryMap, Map<String, I18nEnumAttribute> maintenanceLogServiceTypeEnumMap) {
        ReportMaintenanceVo vo = new ReportMaintenanceVo();
        CompanyDevice device = deviceMap.get(e.getDeviceId());
        if (device != null) {
            ProductCache productCache = productCacheMap.getOrDefault(device.getProductId(), new ProductCache());
            vo.setPicture(productCache.getUrl());
            vo.setModel(productCache.getCommodityModel());
            vo.setName(device.getDeviceName());
            vo.setSn(device.getDeviceSn());
            final String categoryName = categoryMap.getOrDefault(device.getSecondCategoryCode(), new FleetCategoryListVo()).getCategoryName();
            vo.setCategory(categoryName);
        }
        vo.setServiceType(maintenanceLogServiceTypeEnumMap.getOrDefault(e.getServiceType() + "", new I18nEnumAttribute()).getDesc());
        vo.setMaintenanceDate(e.getServiceTime());
        vo.setRuntime(e.getRuntime() == null ? null : (e.getRuntime() + HOURS));
        vo.setLaborTime(e.getLaborTime() == null ? null : (e.getLaborTime() + HOURS));
        if (e.getExpenses() != null) {
            vo.setExpenses(e.getExpenses().setScale(0, RoundingMode.HALF_UP).longValue() + "");
        }
        vo.setPerformedBy(e.getPerformedBy());
        vo.setContents(e.getContents());
        return vo;
    }

    @Override
    public void maintenanceExport(ReportMaintenancePageDto req, HttpServletResponse response) throws IOException {
        Long companyId = UserContext.getCompanyId();
        List<CompanyDevice> companyDevices = Optional.ofNullable(companyDeviceService.listWithDeletedByCompanyId(companyId)).orElse(new ArrayList<>());
        if (!CollectionUtils.isEmpty(req.getCategoryCodes())) {
            companyDevices.removeIf(e -> !req.getCategoryCodes().contains(e.getSecondCategoryCode()));
        }
        Map<String, CompanyDevice> deviceMap = companyDevices.stream().collect(Collectors.toMap(CompanyDevice::getDeviceId, Function.identity(), (k1, k2) -> k1, LinkedHashMap::new));
        LambdaQueryWrapper<MaintenanceLog> wrapper = new LambdaQueryWrapper<MaintenanceLog>().eq(MaintenanceLog::getCompanyId, companyId)
                .le(req.getEnd() != null, MaintenanceLog::getServiceTime, req.getEnd())
                .ge(req.getStart() != null, MaintenanceLog::getServiceTime, req.getStart());
        if (!deviceMap.isEmpty()) {
            wrapper.in(MaintenanceLog::getDeviceId, deviceMap.keySet());
        }
        wrapper.orderByDesc(MaintenanceLog::getServiceTime).orderByDesc(MaintenanceLog::getCreateTime);
        List<MaintenanceLog> list = maintenanceLogService.list(wrapper);

        List<ProductCache> productCaches = remoteOperationCacheService.listProducts(companyDevices.stream().map(CompanyDevice::getProductId).distinct().collect(Collectors.toList()));
        Map<Long, ProductCache> productCacheMap = productCaches.stream().collect(Collectors.toMap(ProductCache::getId, Function.identity()));
        final List<String> listSecondCategory = companyDevices.stream().map(CompanyDevice::getSecondCategoryCode).distinct().collect(Collectors.toList());
        final Map<String, FleetCategoryListVo> categoryMap = getFleetCategoryMap(listSecondCategory);

        List<I18nEnumAttribute> maintenanceLogServiceTypeEnum = I18nController.getEnum(UserContext.getLanguage(), StringConst.MAINTENANCE_LOG_SERVICE_TYPE_ENUM);
        Map<String, I18nEnumAttribute> maintenanceLogServiceTypeEnumMap = maintenanceLogServiceTypeEnum.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));

        final List<List<String>> data = getMaintenanceExportHeader(req, companyId);
        getHeaderContent(data);
        // 增加数据
        list.forEach(e -> {
            getMaintenanceHead(req, e, deviceMap, categoryMap, productCacheMap, maintenanceLogServiceTypeEnumMap, data);
        });
        export(data, response, "MAINTENANCE");
    }

    private static void getHeaderContent(List<List<String>> data) {
        // 第三行，表头
        List<String> three = new ArrayList<>();
        List<I18nEnumAttribute> maintenanceTitle = I18nController.getEnum(UserContext.getLanguage(), StringConst.MAINTENANCE_TITLE);
        Map<String, I18nEnumAttribute> maintenanceTitleMap = maintenanceTitle.stream()
                .collect(Collectors.toMap(I18nEnumAttribute::getType, Function.identity(), (k1, k2) -> k2));
        three.add(maintenanceTitleMap.getOrDefault("name", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("sn", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault(CATEGORY, new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault(MODEL, new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("serviceType", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("maintenanceDate", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("runtime", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("laborTime", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("expenses", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("performedBy", new I18nEnumAttribute()).getDesc());
        three.add(maintenanceTitleMap.getOrDefault("contents", new I18nEnumAttribute()).getDesc());
        data.add(three);
    }

    @NotNull
    private List<List<String>> getMaintenanceExportHeader(ReportMaintenancePageDto req, Long companyId) {
        List<List<String>> data = new ArrayList<>();
        // 第一行，公司名称，导出时间
        List<String> one = new ArrayList<>();
        one.add(COMPANY_NAME);
        one.add(Optional.ofNullable(remoteCompanyService.getDetail(companyId)).orElse(new CompanyVo()).getCompanyName());
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        one.add(null);
        String zone= getStrTimeOfZone(System.currentTimeMillis(), req.getRegion());
        String s = (zone + " UTC") + (req.getZone() > 0 ? ("+" + req.getZone()) : req.getZone());
        one.add(s);
        data.add(one);
        // 第二行，选择时间跨度
        List<String> two = new ArrayList<>();
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add(null);
        two.add("Time range:");
        String range = "";
        if (req.getStart() != null) {
            range += getStrDateByRegionZone(req.getStart(), req.getRegion());
        }
        range += "~";
        if (req.getEnd() != null) {
            range += getStrDateByRegionZone(req.getEnd(), req.getRegion());;
        }
        two.add(range);
        data.add(two);
        return data;
    }

    private static void getMaintenanceHead(ReportMaintenancePageDto req, MaintenanceLog e, Map<String, CompanyDevice> deviceMap, Map<String, FleetCategoryListVo> categoryMap, Map<Long, ProductCache> productCacheMap, Map<String, I18nEnumAttribute> maintenanceLogServiceTypeEnumMap, List<List<String>> data) {
        List<String> d = new ArrayList<>();
        CompanyDevice device = deviceMap.getOrDefault(e.getDeviceId(), new CompanyDevice());
        d.add(device.getDeviceName());
        d.add(device.getDeviceSn());
        d.add(categoryMap.getOrDefault(device.getSecondCategoryCode(), new FleetCategoryListVo()).getCategoryName());
        d.add(productCacheMap.getOrDefault(device.getProductId(), new ProductCache()).getCommodityModel());
        d.add(maintenanceLogServiceTypeEnumMap.getOrDefault(e.getServiceType() + "", new I18nEnumAttribute()).getDesc());
        if (e.getServiceTime() != null) {
            d.add(getStrDateByRegionZone(e.getServiceTime(), req.getRegion()));
        } else {
            d.add("");
        }
        if (e.getRuntime() != null) {
            d.add(e.getRuntime() + HOURS);
        } else {
            d.add(null);
        }
        if (e.getLaborTime() != null) {
            d.add(e.getLaborTime() + HOURS);
        } else {
            d.add(null);
        }
        if (e.getExpenses() != null) {
            d.add(e.getExpenses().setScale(0, RoundingMode.HALF_UP).longValue() + "");
        } else {
            d.add(null);
        }
        d.add(e.getPerformedBy());
        d.add(e.getContents());
        data.add(d);
    }

    private void export(List<List<String>> data, HttpServletResponse response, String title) throws IOException {
        try {
            data.forEach(e -> e.replaceAll(CsvUtil::format));
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.toString());
            response.setCharacterEncoding(UTF_8);
            //进行下载
            String fileName = URLEncoder.encode(title + "-" + new SimpleDateFormat("yyyyMMdd").format(DateUtil.getDate(LocalDateTime.now())), UTF_8).replace("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".csv");
            // 加上UTF-8文件的标识字符
            response.getWriter().write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
            CsvWriter csvWriter = cn.hutool.core.text.csv.CsvUtil.getWriter(response.getWriter());
            csvWriter.write(data);
            csvWriter.close();
        }catch (IOException ioException){
            log.error(ioException.getMessage(), ioException);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding(UTF_8);
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding(UTF_8);
            response.getWriter().println(JSON.toJSONString(R.fail("下载文件失败")));
        }
    }

    /**
     * 获取当前时间戳的东八区日期
     * @param timestamp
     * @return
     */
    public static String getStrDateByRegionZone(Long timestamp, String region){
        if(Objects.isNull(timestamp) || timestamp==0L){
            return "";
        }
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.of("+8"));
        return localDateTime.format(getDatePatternByRegion(region));
    }

    public static String getStrDateOfZone(Long timestamp,String formatPattern){
        if(Objects.isNull(timestamp) || timestamp==0L){
            return "";
        }
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.of("+8"));
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(formatPattern);
        return localDateTime.format(dateTimeFormatter);
    }

    /**
     * 获取指定时区下的时间字符串
     * @param timestamp 时间戳
     * @return
     */
    public static String getStrTimeOfZone(Long timestamp,String region){
        if(Objects.isNull(timestamp) || timestamp==0L){
            return "";
        }
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.of("+8"));
        return localDateTime.format(getAmDatePatternByRegion(region));
    }

    /**
     * 获取时区对应的日期格式对象
     * @param region
     * @return
     */
    private DateTimeFormatter getTimePatternByRegion(String region){
        if(EU.equals(region)){
            return DateTimeFormatter.ofPattern(StringConst.TIME_EU_PATTERN);
        }
        return DateTimeFormatter.ofPattern(StringConst.TIME_NA_PATTERN);
    }

    /**
     * 获取时区对应的日期格式对象
     * @param region
     * @return
     */
    private static DateTimeFormatter getDatePatternByRegion(String region){
        if(EU.equals(region)){
            return DateTimeFormatter.ofPattern(StringConst.DATE_EU_PATTERN);
        }
        return DateTimeFormatter.ofPattern(StringConst.DATE_NA_PATTERN);
    }

    /**
     * 获取时区对应的日期格式对象
     * @param region
     * @return
     */
    private static DateTimeFormatter getAmDatePatternByRegion(String region){
        if(EU.equals(region)){
            return DateTimeFormatter.ofPattern(StringConst.TIME_WITH_AM_EU_PATTERN);
        }
        return DateTimeFormatter.ofPattern(StringConst.TIME_WITH_AM_NA_PATTERN,Locale.US);
    }
}
