package com.chervon.fleet.web.entity.po;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备日使用量使用次数统计表（设备级：不受绑定解绑影响）(t_data_daily_tool_usage)实体类
 *
 * <AUTHOR>
 * @since 2023-08-28 17:04:04
 * @description 由 Mybatisplus Code Generator 创建
 */
@Data
@NoArgsConstructor
public class DailyToolUsageCount implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 设备一级分类编码
     */
    private String categoryCode;
    /**
     * 日期：2023-07-12
     */
    private String date;
    /**
     * 当日总使用时长（单位：分钟 不受绑定解绑影响）
     */
    private Integer usageDuration;
    /**
     * 设备使用数量 *
     */
    private Integer usageCount;
}