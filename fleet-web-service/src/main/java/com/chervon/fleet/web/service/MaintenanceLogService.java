package com.chervon.fleet.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.web.entity.dto.MaintenanceLogDto;
import com.chervon.fleet.web.entity.dto.MaintenanceLogSearchDto;
import com.chervon.fleet.web.entity.po.MaintenanceLog;
import com.chervon.fleet.web.entity.vo.MaintenanceLogVo;
/**
 * 维保操作记录表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-15 19:15:52
 * @description 
 */
public interface MaintenanceLogService extends IService<MaintenanceLog> {
    /**
     * 设备维保日志-列表
     * @param req 查询条件
     * @return 列表数据
     */
    PageResult<MaintenanceLogVo> maintenanceLogPage(MaintenanceLogSearchDto req);

    /**
     * 设备维保日志-查询截止当前的运行时间-传入deviceId
     * @param deviceId 设备id
     * @return 运行时间
     */
    Integer maintenanceLogRuntime(String deviceId);

    /**
     * 设备维保日志-新增
     *
     * @param req 操作对象
     */
    void maintenanceLogAdd(MaintenanceLogDto req);

    /**
     * 设备维保日志-编辑
     *
     * @param req 操作对象
     */
    void maintenanceLogEdit(MaintenanceLogDto req);

    /**
     * 设备维保日志-删除-传入maintenanceLogId
     *
     * @param maintenanceLogId 维保日志id
     */
    void maintenanceLogDelete(Long maintenanceLogId);

    /**
     * 设备维保日志-详情-传入maintenanceLogId
     *
     * @param maintenanceLogId 维保日志id
     * @return 维保日志对象
     */
    MaintenanceLogVo maintenanceLogDetail(Long maintenanceLogId);
}
