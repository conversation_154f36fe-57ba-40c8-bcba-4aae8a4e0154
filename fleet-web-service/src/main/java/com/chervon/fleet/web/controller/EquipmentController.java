package com.chervon.fleet.web.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.fleet.web.annotation.WebApiHeaders;
import com.chervon.fleet.web.api.entity.dto.DeviceEditDto;
import com.chervon.fleet.web.api.entity.query.dashboard.ChargingSystemDetailDashboardQuery;
import com.chervon.fleet.web.entity.consts.StringConst;
import com.chervon.fleet.web.entity.dto.EquipmentSearchDto;
import com.chervon.fleet.web.entity.dto.StatisticsQueryDto;
import com.chervon.fleet.web.entity.vo.*;
import com.chervon.fleet.web.entity.vo.dashboard.ChargingSystemDetailDashboardVo;
import com.chervon.fleet.web.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Equipment设备列表和详情
 */
@Api(tags = "Equipment设备列表和详情")
@RestController
@Slf4j
@RequestMapping("equipment")
@AllArgsConstructor
public class EquipmentController {

    private final EquipmentService equipmentService;
    private final BiPowerHubChargingService biPowerHubChargingService;
    private final BiPowerHubDetailService biPowerHubDetailService;
    private final DailyBatteryUsageService dailyBatteryUsageService;
    private final TotalBatteryUsageService totalBatteryUsageService;
    private final DailyChargerUsageService dailyChargerUsageService;
    private final DailyToolUsageService dailyToolUsageService;
    private final TotalToolUsageService totalToolUsageService;

    /**
     * 根据登录的租户id获取filter数据
     * @param search
     * @return
     */
    @ApiOperation(value = "根据登录的租户id获取filter数据")
    @GetMapping(value = "/filterCondition")
    @WebApiHeaders
    public WebFilterConditionVo filterCondition(@RequestParam(value = "search", required = false) String search) {
        return equipmentService.filterCondition(search);
    }

    /**
     * 查询租户下的库存设备列表
     * @param req
     * @return
     */
    @ApiOperation("查询租户下的库存设备列表")
    @PostMapping("list")
    @WebApiHeaders
    public EquipmentSearchVo list(@RequestBody EquipmentSearchDto req) {
        return equipmentService.search(req);
    }

    /**
     * 根据设备id，查询设备详情
     * @param deviceId
     * @return
     */
    @ApiOperation("根据设备id，查询设备详情")
    @GetMapping("detail")
    @WebApiHeaders
    public EquipmentVo detail(@RequestParam("deviceId") String deviceId) {
        Assert.hasText(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        return equipmentService.detail(deviceId);
    }

    /**
     * 编辑设备名称
     * @param req
     */
    @ApiOperation("编辑设备名称")
    @PostMapping("editName")
    @WebApiHeaders
    public void editName(@RequestBody DeviceEditDto req) {
        equipmentService.editName(req);
    }

    /**
     * 删除设备
     * @param deviceId
     */
    @ApiOperation("删除设备")
    @GetMapping("delete")
    @WebApiHeaders
    public void delete(@RequestParam("deviceId") String deviceId) {
        equipmentService.delete(deviceId);
    }



    /**
     * 查询工具类使用时长和使用次数：从t_data_total_tool_usage表查询【总使用时长，总使用次数】
     */
    @ApiOperation("详情-statistics-工具类：USAGE STATISTICS【总使用时长和总使用次数】（从t_data_total_tool_usage表查询）")
    @GetMapping("detail/statistics/tool")
    @WebApiHeaders
    public EquipmentStatisticsToolVo detailStatisticsTool(@RequestParam("deviceId") String deviceId) {
        Assert.notNull(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        return totalToolUsageService.detailStatisticsTool(deviceId);
    }

    /**
     * 检测StatisticsQueryDto格式
     *
     * @param req dto
     */
    private void checkStatisticsQueryDto(@RequestBody StatisticsQueryDto req) {
        Assert.notNull(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        Assert.notNull(req.getStart(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.START_TIME);
        Assert.notNull(req.getEnd(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.END_TIME);
        if (!StringConst.YMD_REG.matcher(req.getStart()).matches()) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, req.getStart());
        }
        if (!StringConst.YMD_REG.matcher(req.getEnd()).matches()) {
            throw new ServiceException(ErrorCode.PARAMETER_FORMAT_ERROR, req.getEnd());
        }
    }

    /**
     * 查询工具类每日使用时长：从t_data_daily_tool_usage表查询
     * @param req
     * @return
     */
    @ApiOperation("详情-statistics-工具类-每日运行时间时长统计-Runtime&Frequency")
    @PostMapping("detail/statistics/tool/runtimeFrequency")
    @WebApiHeaders
    public List<EquipmentStatisticsToolRuntimeFrequencyVo> detailStatisticsToolRuntimeFrequency(@RequestBody StatisticsQueryDto req) {
        checkStatisticsQueryDto(req);
        return dailyToolUsageService.detailStatisticsToolRuntimeFrequency(req);
    }

    @ApiOperation("详情-statistics-工具类-Average Daily Usage")
    @PostMapping("detail/statistics/tool/averageDailyUsage")
    @WebApiHeaders
    public EquipmentStatisticsToolAverageDailyUsageVo detailStatisticsToolAverageDailyUsage(@RequestBody StatisticsQueryDto req) {
        checkStatisticsQueryDto(req);
        return dailyToolUsageService.detailStatisticsToolAverageDailyUsage(req);
    }

    @ApiOperation("详情-statistics-工具类-每日能量消耗-Energy Consumption")
    @PostMapping("detail/statistics/tool/energyConsumption")
    @WebApiHeaders
    public List<EquipmentStatisticsToolEnergyConsumptionVo> detailStatisticsToolEnergyConsumption(@RequestBody StatisticsQueryDto req) {
        checkStatisticsQueryDto(req);
        return dailyToolUsageService.detailStatisticsToolEnergyConsumption(req);
    }

    /**
     * 设备powerAvailability DC-DC设备特殊
     * @param deviceId
     * @return
     */
    @ApiOperation("充电器设备详情：powerAvailability")
    @GetMapping("detail/statistics/charger")
    @WebApiHeaders
    public EquipmentStatisticsChargerVo detailStatisticsCharger(@RequestParam("deviceId") String deviceId) {
        Assert.notNull(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        return biPowerHubChargingService.detailStatisticsCharger(deviceId);
    }

    @ApiOperation(value = "详情-statistics-充电器-详情:拓扑图")
    @PostMapping(value = "detail/statistics/charger/detail")
    @WebApiHeaders
    public R<List<ChargingSystemDetailDashboardVo>> chargingSystemDetail(@RequestBody ChargingSystemDetailDashboardQuery query) {
        Assert.notNull(query.getCompanyId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.COMPANY_ID);
        Assert.notNull(query.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        List<ChargingSystemDetailDashboardVo> result = biPowerHubDetailService.powerHubDetailRight(query);
        return R.ok(result);
    }

    @ApiOperation("详情-statistics-充电器-每日充电器使用统计-Usage Statistics")
    @PostMapping("detail/statistics/charger/usage")
    @WebApiHeaders
    public List<EquipmentStatisticsChargerUsageVo> detailStatisticsChargerUsage(@RequestBody StatisticsQueryDto req) {
        checkStatisticsQueryDto(req);
        return dailyChargerUsageService.detailStatisticsChargerUsage(req);
    }

    @ApiOperation("详情-statistics-电池")
    @GetMapping("detail/statistics/battery")
    @WebApiHeaders
    public EquipmentStatisticsBatteryVo detailStatisticsBattery(@RequestParam("deviceId") String deviceId) {
        Assert.notNull(deviceId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        return totalBatteryUsageService.detailStatisticsBattery(deviceId);
    }

    @ApiOperation("详情-statistics-电池-每日电池充放电量统计-Charging & Discharging Energy")
    @PostMapping("detail/statistics/battery/energy")
    @WebApiHeaders
    public List<EquipmentStatisticsBatteryEnergyVo> detailStatisticsBatteryEnergy(@RequestBody StatisticsQueryDto req) {
        checkStatisticsQueryDto(req);
        return dailyBatteryUsageService.detailStatisticsBatteryEnergy(req);
    }

    @ApiOperation("详情-statistics-电池-每日电池充放电时长统计-Charging & Discharging Time")
    @PostMapping("detail/statistics/battery/time")
    @WebApiHeaders
    public List<EquipmentStatisticsBatteryTimeVo> detailStatisticsBatteryTime(@RequestBody StatisticsQueryDto req) {
        checkStatisticsQueryDto(req);
        return dailyBatteryUsageService.detailStatisticsBatteryTime(req);
    }

}
