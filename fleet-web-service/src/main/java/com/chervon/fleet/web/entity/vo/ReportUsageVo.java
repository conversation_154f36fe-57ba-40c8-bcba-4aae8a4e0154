package com.chervon.fleet.web.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 报表使用量
 * <AUTHOR>
 * @date 2023/7/25 11:43
 */
@Data
@Accessors(chain = true)
public class ReportUsageVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备图片
     */
    @ApiModelProperty("设备图片")
    private String picture;

    @ApiModelProperty("设备名称")
    private String name;

    @ApiModelProperty("设备sn")
    private String sn;

    @ApiModelProperty("二级品类")
    private String category;

    @ApiModelProperty("二级品类名称")
    private String categoryName;

    @ApiModelProperty("商品型号")
    private String model;

    @ApiModelProperty("标签")
    private List<String> tags;

    @ApiModelProperty("Daily average usage time")
    private String dailyAverageUsageTime;

    @ApiModelProperty("Usage time-selected range")
    private String usageTimeSelectedRange;

    @ApiModelProperty("Total usage time")
    private String totalUsageTime;

    @ApiModelProperty("Data start time")
    private Long dataStartTime;

    @ApiModelProperty("Data end time")
    private Long dataEndTime;

}
