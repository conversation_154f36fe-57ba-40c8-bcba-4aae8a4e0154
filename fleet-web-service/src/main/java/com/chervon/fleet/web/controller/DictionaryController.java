package com.chervon.fleet.web.controller;

import com.chervon.fleet.web.service.DataDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Dictionary通用数据字典表相关接口
 *
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
@RestController
@RequestMapping("/dict")
@Api(tags = "通用配置")
public class DictionaryController {
    @Autowired
    private DataDictionaryService dataDictionaryService;

    @ApiOperation("根据dataName获取配置")
    @GetMapping("/get")
    public String getConfig(@RequestParam("key") String key) {
        return dataDictionaryService.getValueByKeyNoCache(key);
    }
}
