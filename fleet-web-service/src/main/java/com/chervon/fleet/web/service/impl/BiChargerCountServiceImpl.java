package com.chervon.fleet.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.web.api.entity.query.InventoryQuery;
import com.chervon.fleet.web.entity.po.BiChargerCount;
import com.chervon.fleet.web.entity.vo.dashboard.ChargerCountDashboardVo;
import com.chervon.fleet.web.mapper.BiChargerCountMapper;
import com.chervon.fleet.web.service.BiChargerCountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 充电器数量统计表（看板：total charge system饼状图）服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-27 13:58:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BiChargerCountServiceImpl extends ServiceImpl<BiChargerCountMapper, BiChargerCount>
    implements BiChargerCountService {

    @Override
    public List<BiChargerCount> listByCompanyId(Long companyId) {
        return this.list(new LambdaQueryWrapper<BiChargerCount>().eq(BiChargerCount::getCompanyId, companyId));
    }

    @Override
    public int countChargerByCompanyId(Long companyId) {
        final List<BiChargerCount> list = this.list(new LambdaQueryWrapper<BiChargerCount>()
                .eq(BiChargerCount::getCompanyId, companyId)
                .select(BiChargerCount::getChargerCount));
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.stream().mapToInt(BiChargerCount::getChargerCount).sum();
    }

    @Override
    public List<ChargerCountDashboardVo> chargerCount(Long companyId) {
        List<BiChargerCount> biChargerCountList = listByCompanyId(companyId);
        if (CollectionUtils.isEmpty(biChargerCountList)) {
            log.debug("租户({})看板充电器数量统计数据为空", companyId);
            return new ArrayList<>();
        }
        return ConvertUtil.convertList(biChargerCountList, ChargerCountDashboardVo.class);
    }
}