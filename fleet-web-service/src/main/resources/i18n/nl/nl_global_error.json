[{"code": "500", "reason": "Service interne uitzondering"}, {"code": "602", "reason": "Server time-out"}, {"code": "1401", "reason": "Gebruiker is niet a<PERSON>"}, {"code": "1403", "reason": "Toestemming geweigerd"}, {"code": "1405", "reason": "[{0}] <PERSON><PERSON><PERSON>"}, {"code": "1450", "reason": "Parameter [{0}] fout"}, {"code": "1451", "reason": "Parameter [{0}] niet verstrekt"}, {"code": "1452", "reason": "Parameter[{0}]Indelingsfout"}, {"code": "1464", "reason": "De lengte van parameter [{0}] is te lang"}, {"code": "1466", "reason": "Activiteiten worden op de achtergrond uitgevoerd, wacht even"}, {"code": "10001", "reason": "geen gegevens gevonden:[{0}]"}, {"code": "10002", "reason": "gegevensduplicatie:[{0}]"}, {"code": "10008", "reason": "database verrichtingsfout:[{0}]"}]