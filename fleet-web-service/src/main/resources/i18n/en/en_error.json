[{"code": "2003", "reason": "Duplicate request, please wait a moment"}, {"code": "1190052001", "reason": "Dealer is saved to collection"}, {"code": "1190052002", "reason": "Dealer not found"}, {"code": "1190052003", "reason": "Total number of saved dealer exceed limit"}, {"code": "1190012001", "reason": "The device is bound to another app"}, {"code": "1190012002", "reason": "The device is bound by another company"}, {"code": "1190012003", "reason": "The device has been deactivated"}, {"code": "1190012004", "reason": "Device data not found"}, {"code": "1190012005", "reason": "Device SN error"}, {"code": "1190012006", "reason": "Device data not found"}, {"code": "1190012009", "reason": "Duplicate equipment names are not allowed"}, {"code": "1190022001", "reason": "Gateway already exist. Cannot create new gateway"}, {"code": "1190022002", "reason": "Gateway name already exist"}, {"code": "1190022003", "reason": "Gateway cannot be found"}, {"code": "1190022004", "reason": "Gateway cannot be created"}, {"code": "1190022005", "reason": "Gateway cannot be created"}, {"code": "1190032001", "reason": "Name overlap with default group name"}, {"code": "1190032002", "reason": "Duplicate group names are not allowed"}, {"code": "1190032003", "reason": "The number of groups created exceeds limit"}, {"code": "1190032004", "reason": "Group does not exist"}, {"code": "1190032005", "reason": "Cannot modify default group"}, {"code": "1190032006", "reason": "Cannot delete default group"}, {"code": "1190032007", "reason": "Duplicate tag names are not allowed"}, {"code": "1190032008", "reason": "The number of tags created exceeds limit"}, {"code": "**********", "reason": "Tag does not exist"}, {"code": "**********", "reason": "Maintenance info not found"}, {"code": "**********", "reason": "Maintenance log not found"}, {"code": "**********", "reason": "Incorrect usage data"}, {"code": "**********", "reason": "Incorrect due date"}, {"code": "**********", "reason": "The user is already bound by another company"}, {"code": "**********", "reason": "The company has already registered EGO Fleet."}, {"code": "**********", "reason": "Account does not exist"}, {"code": "**********", "reason": "Account is activated"}, {"code": "**********", "reason": "This link is invalid. Please contact the inviter to resend the invitation."}, {"code": "**********", "reason": "Must enter password"}, {"code": "**********", "reason": "Account service not available"}, {"code": "**********", "reason": "The account does not match the password."}, {"code": "**********", "reason": "Email already registered. Please log in."}, {"code": "**********", "reason": "Role cannot be transferred"}, {"code": "**********", "reason": "Role cannot be transferred"}, {"code": "**********", "reason": "Fail to acquire user info"}, {"code": "**********", "reason": "Token error"}, {"code": "**********", "reason": "Token error"}, {"code": "**********", "reason": "Please follow the current password format"}, {"code": "**********", "reason": "Fail to acquire user access"}, {"code": "**********", "reason": "Wrong password format"}, {"code": "**********", "reason": "The account does not match the password."}, {"code": "**********", "reason": "Invitee error"}, {"code": "**********", "reason": "Admin account cannot be edited."}, {"code": "**********", "reason": "Invite failed. The user is already an employee of your company."}, {"code": "**********", "reason": "Invite failed. The user is already an employee of another company."}, {"code": "**********", "reason": "Frequent invitation request. Please check with invitee."}, {"code": "**********", "reason": "Fail to transfer admin role"}, {"code": "**********", "reason": "User not found"}, {"code": "**********", "reason": "Your account is logged into too many devices."}, {"code": "**********", "reason": "Invalid link.\nPlease resend your request"}, {"code": "**********", "reason": "Invalid link.\nPlease contact the inviter to resend the invitation"}, {"code": "**********", "reason": "Invalid link.\nPlease resend your request to reset the password"}, {"code": "**********", "reason": "Activation failed. You are already registered to another company."}, {"code": "**********", "reason": "Activation failed. You have already registered."}, {"code": "**********", "reason": "Mail server slow, please try again later."}, {"code": "**********", "reason": "An invitation was sent within 72 hours. Please wait for activation."}, {"code": "**********", "reason": "An invitation was sent. You can resend the invitation via \"invite Again\"."}]