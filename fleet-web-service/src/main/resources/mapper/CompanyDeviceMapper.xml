<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.fleet.web.mapper.CompanyDeviceMapper">
    <select id="selectMaxOrder" resultType="com.chervon.fleet.web.entity.po.CompanyDevice">
        select device_name
        from t_company_device
        where is_deleted = 0
          and company_id = #{companyId}
          and device_name REGEXP '^${cDeviceName}$|^${cDeviceName}\\([0-9]+\\)$'
    </select>

    <select id="selectListWithDeletedByCompanyId" resultType="com.chervon.fleet.web.entity.po.CompanyDevice">
        select *
        from t_company_device
        where company_id = #{companyId} order by create_time desc
    </select>

    <select id="selectDeletedList" resultType="com.chervon.fleet.web.entity.po.CompanyDevice">
        SELECT id,is_deleted,device_id,product_id,first_category_code,second_category_code,device_name,company_id,custom_type,binding_time,maintenance_status
        FROM t_company_device where is_deleted=1  and company_id = #{companyId}
        and id in (
            select  Max(id) from t_company_device where is_deleted=1 and company_id=#{companyId}
        <if test="tagDeviceIds != null and tagDeviceIds.size() > 0">
            and device_id in
            <foreach collection="tagDeviceIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="excludeDeviceIds != null and excludeDeviceIds.size() > 0">
            and device_id not in
            <foreach collection="excludeDeviceIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="secondCategoryCodes != null and secondCategoryCodes.size() > 0">
            and second_category_code in
            <foreach collection="secondCategoryCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY device_id
        )
    </select>

    <select id="getDeviceCount" resultType="java.lang.Integer">
        SELECT count(1) FROM t_company_device WHERE is_deleted=0 and company_id=#{companyId}
        <choose>
            <when test="firstCategoryCode != null and firstCategoryCode=='Tools' ">
                and (first_category_code != 'Charger' and first_category_code!= 'Battery')
            </when>
            <otherwise>
                and first_category_code = #{firstCategoryCode}
            </otherwise>
        </choose>
    </select>

    <select id="getCompanyIdList" resultType="java.lang.Long">
        SELECT distinct company_id FROM t_company_device WHERE is_deleted=0
    </select>
</mapper>
