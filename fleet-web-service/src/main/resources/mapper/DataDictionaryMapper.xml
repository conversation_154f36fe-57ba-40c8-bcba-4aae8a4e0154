<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.fleet.web.mapper.DataDictionaryMapper">
    <resultMap id="BaseResultMap" type="com.chervon.fleet.web.entity.po.DataDictionary">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="data_group" jdbcType="VARCHAR" property="dataGroup"/>
        <result column="group_desc" jdbcType="VARCHAR" property="groupDesc"/>
        <result column="data_name" jdbcType="VARCHAR" property="dataName"/>
        <result column="data_value" jdbcType="VARCHAR" property="dataValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="sort_id" jdbcType="INTEGER" property="sortId"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, data_group, group_desc,
    data_name, data_value, remark, sort_id, create_user, create_user_name, create_time, 
    update_user, update_user_name, update_time
  </sql>

</mapper>