<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.fleet.web.mapper.BiDeviceErrorListMapper">
    <select id="selectLatestErrorDevice"
            resultType="java.lang.String">
        select device_name as latestErrorDevice
        from t_bi_device_error_list
        where device_category in ("Charger", "Battery")
          and company_id = #{companyId}
        and device_id in
        <foreach collection="listDeviceId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by modify_time desc limit 1;
    </select>
    <select id="countErrorNumber" resultType="java.lang.Integer">
        select count(*)
        from t_bi_device_error_list
        where device_category in ("Charger", "Battery")
          and company_id = #{companyId} and device_id in
        <foreach collection="listDeviceId" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
