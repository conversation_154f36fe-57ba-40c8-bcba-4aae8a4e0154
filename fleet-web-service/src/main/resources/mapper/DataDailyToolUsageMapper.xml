<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.fleet.web.mapper.DailyToolUsageMapper">
    <sql id="tableName">
        t_data_daily_tool_usage
    </sql>

    <sql id="baseColumn">
        id,company_id,device_id,category_code,date,usage_duration,number_times,energy_consume,create_time,modify_time,status,span_usage_duration,span_number_times,span_energy_consume
    </sql>


    <select id="countDailyToolUsageByDate" resultType="com.chervon.fleet.web.entity.po.DailyToolUsageCount">
        select company_id,category_code,date,
            sum(usage_duration) as usage_duration,count(device_id) as usage_count
        from t_data_daily_tool_usage where company_id = #{companyId} and date=#{date} and status=0
        group by company_id,category_code,date
    </select>

</mapper>