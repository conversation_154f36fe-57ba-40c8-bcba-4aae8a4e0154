<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.fleet.web.mapper.DailyBatteryUsageMapper">
    <sql id="tableName">
        t_data_daily_battery_usage
    </sql>

    <sql id="baseColumn">
        id,device_id,category_code,second_category_code,company_id,date,charging_time,discharging_time,charging_energy,discharging_energy,daily_usage_time,daily_number_times,create_time,modify_time,status,
        span_charging_time,span_discharging_time,span_charging_energy,span_discharging_energy
    </sql>

    <select id="countDailyBatteryUsageByDate" resultType="com.chervon.fleet.web.entity.po.DailyBatteryUsageCount">
        select company_id,second_category_code,date,sum(daily_usage_time) as daily_usage_time,sum(daily_number_times) as daily_number_times,count(if(daily_usage_time>120, 1, null)) device_count
        from t_data_daily_battery_usage where date = #{date} and company_id = #{companyId} and status=0
        group by company_id,second_category_code,date
    </select>

</mapper>