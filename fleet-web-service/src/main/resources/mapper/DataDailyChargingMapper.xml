<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chervon.fleet.web.mapper.DailyChargerUsageMapper">
    <sql id="tableName">
        t_data_daily_charging
    </sql>

    <sql id="baseColumn">
        id,company_id,device_id,category_code,second_category_code,date,charging_energy,charging_time,create_time,modify_time,status,span_charging_energy,span_charging_time
    </sql>

    <select id="countDailyChargingByDate" resultType="com.chervon.fleet.web.entity.po.DailyChargingCount">
        select company_id,date,sum(charging_energy) as charging_energy
        from t_data_daily_charging where date=#{date} and company_id = #{companyId} and status=0 and second_category_code != 'PGXPowerBank'
        GROUP BY company_id,date
    </select>

</mapper>