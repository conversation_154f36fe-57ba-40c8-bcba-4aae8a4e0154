package com.chervon.fleet.web.api.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 自定义标签对象
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TagVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 分组id
     */
    private Long groupId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 标签id
     */
    private Long tagId;
    /**
     * 标签名称
     */
    private String tagName;

}
