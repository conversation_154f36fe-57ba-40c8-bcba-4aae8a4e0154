package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * fleet品类数据
 * <AUTHOR>
 * @date 2023/7/12 13:53
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "fleet品类数据")
public class FleetWebCategoryVo extends FleetCategoryVo implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1;
}
