package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * @Description:
 * 充电适配器和充电仓通用状态: 0保留  1 正常充电 2等待充电 3电池过温 4电池故障 5空闲 6充电完成  (-1 电池类型放电  -4全部设备故障)
 * <AUTHOR>
 * @Date: 2023/7/20 13:34
 */
public enum CompartmentStatusEnum {
    Unknown(0,"未知状态"),
    Charging(1, "正常充电(绿色)"),//NORMAL
    Standby(2, "等待充电(灰色)"),
    OverHot(3, "电池过温(橙色)"),
    Fault(4, "电池故障(红色)"),
    Empty(5,"空闲"),
    Complete(6,"充电完成"),//Fully Charged
    Discharging(-1,"正在放电"),
    ALL_Fault(-4,"全部故障")
    ;

    /**
     * 类型
     */
    private final int type;

    /**
     * 值
     */
    private final String value;

    /**
     * 构造函数
     * @param type 类型值
     * @param value 值
     */
    CompartmentStatusEnum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取类型
     */
    public int getType() {
        return type;
    }

    /**
     * 获取值
     */
    public String getValue() {
        return value;
    }

    /**
     * 获取枚举
     */
    public static CompartmentStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(Unknown);
    }
}
