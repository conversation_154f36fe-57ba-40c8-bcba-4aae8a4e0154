package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 固定网关坐标列表及区间距离配置
 *
 * <AUTHOR>
 * @date 2024/7/31 14:00
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "固定网关坐标列表及区间距离配置")
public class FixGatewayCoordinateVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("距离阈值")
    private BigDecimal distance;

    @ApiModelProperty("坐标列表")
    private List<GatewayCoordinateVo> list = new ArrayList<>();
}
