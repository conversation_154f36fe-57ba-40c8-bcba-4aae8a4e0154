package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.vo.DeviceErrorVo;

import java.util.List;
import java.util.Map;

/**
 * 远程设备服务接口
 */
public interface RemoteFleetAppService {

    /**
     * 根据租户id模糊查询设备id集合
     *
     * @param companyId 租户id片段
     * @return 设备id集合
     * @throws ServiceException 自定义内部服务异常
     */
    List<String> listDeviceIdLikeCompanyId(String companyId) throws ServiceException;

    /**
     * 根据设备id构建设备id和用户id的映射
     *
     * @param deviceIds 设备id集合
     * @return map
     * @throws ServiceException 自定义内部服务异常
     */
    Map<String, Long> mapDeviceIdCompanyId(List<String> deviceIds) throws ServiceException;

    /**
     * 给RN面板提供设备最后一条故障信息接口
     * @param deviceId 设备id
     * @param hourExpire 故障过期小时数
     * @return 设备最新一条故障
     */
    DeviceErrorVo getLastDeviceError(String deviceId,Integer hourExpire);
}
