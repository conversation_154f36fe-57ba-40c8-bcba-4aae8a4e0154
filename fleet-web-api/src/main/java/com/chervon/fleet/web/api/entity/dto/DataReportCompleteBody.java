package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备上线下线状态消息体负载实体
 * 给 android端订阅刷新设备在线离线状态 *
 * <AUTHOR> 2023/7/19
 */
@Accessors(chain = true)
@Data
public class DataReportCompleteBody implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("上报完成状态：1上报完成有云朵 0超期无云朵")
    private Integer status;

    @ApiModelProperty("网关id")
    private String gatewayId;
}
