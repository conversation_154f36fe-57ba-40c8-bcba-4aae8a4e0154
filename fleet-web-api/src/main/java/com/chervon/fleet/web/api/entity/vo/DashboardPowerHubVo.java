package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 仪表板电源集线器视图对象
 * <AUTHOR>
 * @date 2023/7/24 15:12
 */
@Data
@Accessors(chain = true)
public class DashboardPowerHubVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 标签列表
     */
    @ApiModelProperty("标签列表")
    private List<TagVo> tags;
    /**
     * 错误列表
     */
    @ApiModelProperty("错误列表")
    private List<Error> errors;
    /**
     * 充电进度
     */
    @ApiModelProperty("充电进度")
    private BigDecimal chargingRate;
    /**
     * 充电仓使用中数量
     */
    @ApiModelProperty("充电仓使用中数量")
    private Integer portUsedNumber;
    /**
     * 充电仓总数
     */
    @ApiModelProperty("充电仓总数")
    private Integer portTotalNumber;
    /**
     * Portable-充电完成数
     */
    @ApiModelProperty("Portable-充电完成数")
    private Integer portableReadyNumber;
    /**
     * Portable-充电中数
     */
    @ApiModelProperty("Portable-充电中数")
    private Integer portableChargingNumber;
    /**
     * Portable-等待中数
     */
    @ApiModelProperty("Portable-等待中数")
    private Integer portableWaitingNumber;
    /**
     * High Capacity-充电完成数
     */
    @ApiModelProperty("High Capacity-充电完成数")
    private Integer highReadyNumber;
    /**
     * High Capacity-充电中数
     */
    @ApiModelProperty("High Capacity-充电中数")
    private Integer highChargingNumber;
    /**
     * High Capacity-等待中数
     */
    @ApiModelProperty("High Capacity-等待中数")
    private Integer highWaitingNumber;
    /**
     * 充电器状态：
     * 0保留 -1放电  1 正常充电 2等待充电 3电池过温 4电池故障 5空闲 6充电完成  (-1 电池类型放电  -4全部设备故障)
     */
    @ApiModelProperty("充电状态：0保留 1 正常充电 2等待充电 3电池过温 4电池故障 5充满")
    private Integer chargerState;
    /**
     * 充电器的剩余充电时长预估：4:37
     */
    @ApiModelProperty("充电器的剩余充电时长预估：4:37")
    private String readyInTime;
    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 看板错误子类
     */
    @Data
    @ApiModel("看板错误子类")
    public static class Error implements Serializable {

        private static final long serialVersionUID = 1;
        /**
         * 故障码
         */
        @ApiModelProperty("故障码")
        private String errorCode;
        /**
         * 故障消息标题
         */
        @ApiModelProperty("故障消息标题")
        private String faultMessageTitle;

        /**
         * 处理建议内容
         */
        @ApiModelProperty("处理建议内容")
        private String suggestionContent;
    }
}
