package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 服务状态展示实体
 *
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(description = "分组对象")
public class WebFilterGroupVo extends GroupVo implements Serializable {

    private static final long serialVersionUID = 1;

}
