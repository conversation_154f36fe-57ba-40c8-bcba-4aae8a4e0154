package com.chervon.fleet.web.api.entity.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * (t_company_device)实体类
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-25 10:37:56
 */
@Data
@Accessors(chain = true)
public class CompanyDeviceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 设备sn
     */
    private String deviceSn;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 删除状态 0：未删除 1：已删除
     */
    private Integer isDeleted;
    /**
     * 设备绑定时间
     */
    private Long bindingTime;
    /**
     * 设备绑定用户id
     */
    private Long bindingUserId;
    /**
     * 设备库存状态：1-In Warehouse，2-Out for Work，3-Unknown location，4-Never Seen
     */
    private Integer warehouseStatus;
    /**
     * 网关id
     */
    private String gatewayId;

}