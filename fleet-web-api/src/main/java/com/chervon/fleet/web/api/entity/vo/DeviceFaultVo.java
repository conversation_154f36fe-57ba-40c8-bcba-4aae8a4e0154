package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备故障对象
 * <AUTHOR> 2023/7/12
 */
@Data
public class DeviceFaultVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 故障码
     */
    @ApiModelProperty(value = "故障码")
    private String faultCode;
    /**
     *  设备最后上报时间戳
     */
    @ApiModelProperty(value = "设备数据最后上报时间戳")
    private Long reportTime;



}
