package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: FleetCompanyDeviceVo
 * @Description:租户设备对象vo
 * <AUTHOR>
 * @date 2023/8/2 16:42
 */
@Data
@Accessors(chain = true)
public class FleetCompanyDeviceVo implements Serializable {

    private static final long serialVersionUID = 1;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private Long companyId;
    /**
     * 租户名称
     */
    @ApiModelProperty("租户名称")
    private String companyName;
    /**
     * 已绑定设备数量
     */
    @ApiModelProperty("已绑定设备数量")
    private Integer count;
    /**
     *  已绑定设备id
     */
    @ApiModelProperty("已绑定设备id")
    private List<String> deviceId = new ArrayList<>();
    /**
     * 已绑定设备sn
     */
    @ApiModelProperty("已绑定设备sn")
    private List<String> deviceSn = new ArrayList<>();
    /**
     * 已绑定设备fleet二级品类code
     */
    @ApiModelProperty("已绑定设备fleet二级品类code")
    private List<String> categoryCode = new ArrayList<>();
    /**
     * 已绑定设备品牌id
     */
    @ApiModelProperty("已绑定设备品牌id")
    private List<Long> brandId = new ArrayList<>();
    /**
     * 已绑定设备model#
     */
    @ApiModelProperty("已绑定设备model#")
    private List<String> model = new ArrayList<>();

    /**
     * 获取设备数量
     * @return Integer数量
     */
    public Integer getCount() {
        return deviceId.size();
    }

}
