package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.vo.DashboardErrorVo;
import com.chervon.fleet.web.api.entity.vo.DashboardInventoryVo;
import com.chervon.fleet.web.api.entity.vo.DashboardPowerVo;
import com.chervon.fleet.web.api.entity.vo.DashboardPowerAvailabilityVo;
import com.chervon.fleet.web.api.entity.vo.DashboardPowerHubVo;
import java.util.List;

/**
 * 远程fleet仪表板服务接口
 */
public interface RemoteFleetAppDashboardService {

    /**
     * 库存看板
     *
     * @return 看板数据
     * @throws ServiceException 自定义内部服务异常
     */
    DashboardInventoryVo inventory() throws ServiceException;

    /**
     * Inventory-故障详情列表
     *
     * @return 故障消息列表
     * @throws ServiceException 自定义内部服务异常
     */
    List<DashboardErrorVo> inventoryError() throws ServiceException;

    /**
     * Power
     *
     * @return Power数据
     * @throws ServiceException 自定义内部服务异常
     */
    DashboardPowerVo power() throws ServiceException;

    /**
     * Power-Power Availability
     *
     * @return Power Availability数据
     * @throws ServiceException 自定义内部服务异常
     */
    DashboardPowerAvailabilityVo powerAvailability() throws ServiceException;

    /**
     * Power-Power Overview
     *
     * @return Power Overview 数据
     * @throws ServiceException 自定义内部服务异常
     */
    List<DashboardPowerHubVo> powerOverview() throws ServiceException;
}
