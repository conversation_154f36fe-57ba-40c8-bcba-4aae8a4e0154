package com.chervon.fleet.web.api.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 筛选列表条件对象
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FilterConditionVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 库存状态选项
     */
    @ApiModelProperty(value = "库存状态选项")
    private List<Integer> warehouseStatus;
    /**
     * 设备分类列表(包含已删除设备分类)
     */
    @ApiModelProperty(value = "设备分类列表")
    private List<FleetCategoryVo> categories;
    /**
     * 自定义标签列表
     */
    @ApiModelProperty(value = "自定义标签列表")
    private List<GroupVo> groups;
    /**
     * 在线状态
     */
    @ApiModelProperty("在线状态")
    private List<Integer> onlineStatus;
    /**
     * 维保状态
     */
    @ApiModelProperty("维保状态")
    private List<Integer> maintenanceStatus;
}
