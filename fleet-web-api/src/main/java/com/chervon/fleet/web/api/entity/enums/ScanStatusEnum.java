package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 网关扫描到设备枚举：0-未扫描到,1-扫描到
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ScanStatusEnum implements TypeEnum {
    /**
     * 未扫描到
     */
    NOT_FOUND(0, "未扫描到"),
    /**
     * 已扫描到
     */
    FOUND(1, "已扫描到");


    int type;
    String desc;

}
