package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * DashboardBatteryVo 电池包看板返回对象vo
 * <AUTHOR>
 * @date 2023/7/24 15:06
 */
@Data
@Accessors(chain = true)
public class DashboardBatteryVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * batteryTypename
     */
    @ApiModelProperty("电池包类型名称")
    private String name;
    /**
     * battery count
     */
    @ApiModelProperty("电池包类型数量")
    private Integer count;
    /**
     * 当前类型电池包总安时数（非iot协议电池包显示总安时）
     */
    @ApiModelProperty("该电池包类型总安时数")
    private BigDecimal totalAh;
}
