package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: DeviceErrorVo
 * @Description:设备故障信息vo
 * <AUTHOR>
 * @date 2024/8/6 10:13
 */
@Data
@Accessors(chain = true)
public class DeviceErrorVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;
    /**
     * 故障码
     */
    @ApiModelProperty("故障码")
    private String errorCode;
    /**
     * 故障消息标题
     */
    @ApiModelProperty("故障消息标题")
    private String faultMessageTitle;
    /**
     * 处理建议内容
     */
    @ApiModelProperty("处理建议内容")
    private String suggestionContent;
}
