package com.chervon.fleet.web.api.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 产品查询对象
 * <AUTHOR>
 * 
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "产品查询对象")
public class ProductQuery extends PageQuery {
    private static final long serialVersionUID = 1L;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 分类Id
     */
    @ApiModelProperty(value = "分类Id")
    private Long categoryId;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

}
