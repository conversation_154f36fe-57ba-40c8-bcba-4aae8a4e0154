package com.chervon.fleet.web.api.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 库存状态查询对象
 * <AUTHOR>
 * 
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "库存状态查询对象")
public class InventoryStatusQuery extends PageQuery {
    private static final long serialVersionUID = 1L;
    /**
     * 租户id，必填
     */
    @ApiModelProperty(value = "租户id，必填")
    private Long companyId;
    /**
     * 网关id
     */
    @ApiModelProperty(value = "网关id")
    private String gatewayId;
    /**
     * 设备id集合
     */
    @ApiModelProperty(value = "设备id集合")
    private List<String> listDeviceId;
    /**
     * 库存状态
     */
    @ApiModelProperty(value = "库存状态")
    private List<Integer> warehouseStatus;
    /**
     * 开始时间
     */
    private Long startTime;


}
