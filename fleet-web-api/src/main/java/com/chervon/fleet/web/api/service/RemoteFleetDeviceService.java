package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.dto.*;
import com.chervon.fleet.web.api.entity.vo.*;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * 设备服务
 * <AUTHOR>
 * @date 2023/7/3 14:11
 */
public interface RemoteFleetDeviceService {

    /**
     * 绑定设备
     * @param req 绑定设备请求参数
     * @return 设备详情
     * @throws ServiceException 自定义内部服务异常
     */
    void bind(BaseDeviceDto req) throws ServiceException;

    /**
     * 获取设备详情
     *
     * @param req 设备详情请求参数
     * @return 设备详情
     * @throws ServiceException 自定义内部服务异常
     */
    DeviceInfoVo detail(BaseDeviceDto req) throws ServiceException;
    /**
     * 获取RN面板设备详情
     *
     * @param req 设备详情请求参数
     * @return 设备详情
     * @throws ServiceException 自定义内部服务异常
     */
    RnDetailVo getRnDetail(RnDetailRequest req) throws ServiceException;
    /**
     * 获取设备基础状态：在线离线状态
     * @param req 请求对象
     * @return
     * @throws ServiceException
     */
    BasicStatusVo getDeviceStatus(BaseDeviceDto req) throws ServiceException;

    /**
     * 编辑设备名称
     * @param req 操作对象
     */
    void editName(DeviceEditDto req)  throws ServiceException;
    /**
     * 获取设备详情
     * @param deviceId 设备ID
     * @return 设备信息
     * @throws ServiceException 自定义内部服务异常
     */
    default DeviceInfoVo detailById(String deviceId) throws ServiceException {
        BaseDeviceDto req = new BaseDeviceDto();
        req.setDeviceId(deviceId);
        return detail(req);
    }

    /**
     * 获取设备详情
     * @param deviceSn 设备SN
     * @return 设备信息
     * @throws ServiceException 自定义内部服务异常
     */
    default DeviceInfoVo detailBySn(String deviceSn) throws ServiceException {
        BaseDeviceDto req = new BaseDeviceDto();
        req.setDeviceSn(deviceSn);
        return detail(req);
    }

    /**
     * 校验SN的合法性之后获取产品ID
     *
     * @param deviceSn 设备sn
     * @return 产品id
     * @throws ServiceException 自定义内部服务异常
     */
    Long getProductIdBySn(String deviceSn) throws ServiceException;

    /**
     * 设备解绑
     *
     * @param deviceId 设备id
     * @throws ServiceException 自定义内部服务异常
     */
    void unbind(String deviceId) throws ServiceException;

    /**
     * 解绑租户下的所有设备
     *
     * @param companyId 租户id
     * @throws ServiceException 自定义内部服务异常
     */
    @Async
    void unbindByCompanyId(Long companyId) throws ServiceException;

    /**
     * 根据租户id集合批量获取租户设备信息
     *
     * @param companyIds 租户id集合
     * @return 列表数据
     * @throws ServiceException 自定义内部服务异常
     */
    List<CompanyDeviceVo> batchCompanyDeviceByCompanyId(List<Long> companyIds) throws ServiceException;

    /**
     * 由iot_platform MQTTConnectController.receiveOnlineStatus更新设备在线状态
     *
     * @param deviceId     设备id
     * @param onlineStatus 设备在线状态 1 在线 0 离线
     * @throws ServiceException 自定义内部服务异常
     */
    void updateDeviceOnlineStatus(String deviceId, Integer onlineStatus) throws ServiceException;

    /**
     * 管理平台同步更新fleet端设备信息
     * @param listEditDto 编辑信息列表
     * @return 更新条数
     * @throws ServiceException 自定义内部服务异常
     */
    int batchUpdateCompanyDeviceInfo(List<CompanyDeviceEditDto> listEditDto) throws ServiceException;
    /**
     * 管理平台同步更新fleet端设备信息
     * @param companyDeviceEditDto 编辑信息
     * @return 更新结果
     * @throws ServiceException 自定义内部服务异常
     */
    boolean updateCompanyDeviceInfo(CompanyDeviceEditDto companyDeviceEditDto) throws ServiceException;

    /**
     * 更新设备的数据上报时间
     * @param req 请求参数
     * @throws ServiceException 自定义内部服务异常
     */
    void updateDeviceReportPowerAndTimestamp(DeviceShadowDataDto req) throws ServiceException;

    /**
     * fleet租户设备关系列表查询
     *
     * @param req 查询条件
     * @return 列表数据
     * @throws ServiceException 自定义内部服务异常
     */
    List<FleetCompanyDeviceVo> companyDeviceList(FleetCompanyDevicePageDto req) throws ServiceException;
}
