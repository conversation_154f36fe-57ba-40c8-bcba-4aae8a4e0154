package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 网关扫描设备状态（扫描到未扫描到状态）上报对象
 * 硬件网关和软件网关
 */
@Data
@Accessors(chain = true)
public class GatewayScanReportingDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 网关上报的位置经纬度信息和时间戳
     */
    @ApiModelProperty("网关上报的位置经纬度信息和时间戳")
    private ReportGatewayDto gateway;
    /**
     * 网关上报的设备列表
     */
    @ApiModelProperty("网关上报的设备列表")
    private List<ReportDeviceDto> devices;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GatewayScanReportingDto that = (GatewayScanReportingDto) o;
        return gateway.equals(that.gateway) && devices.equals(that.devices);
    }

    @Override
    public int hashCode() {
        return Objects.hash(gateway, devices);
    }
}
