package com.chervon.fleet.web.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 网关相关错误信息提示编号
 * 错误码定义：错误码：3位应用名+3位模块名+2+3位流水号
 *  * fleet web应用名：119+001+2+三位流水号
 * <AUTHOR>
 * @Description: This is description
 * @Date: 2023/7/27 11:50
 */
public enum DeviceErrorCodeEnum implements IError {
    /**
     * The device already bound other app
     */
    DEVICE_BOUND_OTHER_APP("1190012001", "The device already bound other app."),
    /**
     * The device is already bound to another tenant
     */
    DEVICE_BOUND_OTHER_COMPANY("1190012002", "The device is already bound to another tenant"),
    DEVICE_NOT_ENABLED("1190012003", "The device has been deactivated"),
    DEVICE_BOUND_NOT_FOUND("1190012004", "No bound device data found"),
    DEVICE_SN_CODE_ERROR("1190012005", "Device sn code error."),
    DEVICE_SN_CANNOT_FIND_PRODUCT("1190012006", "No product data found that matches the device's SN code"),
    DEVICE_NAME_EXISTED("1190012009", "Duplicate equipment names are not allowed"),
    DEVICE_NOT_FOUND_CATEGORY("1190012010", "The device did not match the supported product category and cannot be bound!"),
    ;

    DeviceErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private final String errorCode;
    /**
     * 描述
     */
    private final String errorReason;

    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
