package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.dto.ProductFaultDto;

import java.util.List;

/**
 * @Description: 远程产品故障服务
 * <AUTHOR>
 * @since 2023-09-18 18:07
 **/
public interface RemoteProductFaultService {
    /**
     * 列表保存
     *
     * @param productFaultDtoList Dto列表
     * @throws ServiceException 自定义内部服务异常
     */
    void save(List<ProductFaultDto> productFaultDtoList);
}
