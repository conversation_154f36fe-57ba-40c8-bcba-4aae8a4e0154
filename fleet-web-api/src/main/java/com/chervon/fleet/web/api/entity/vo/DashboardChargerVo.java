package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 充电器看板返回对象
 * <AUTHOR>
 * @date 2023/7/24 15:06
 */
@Data
@Accessors(chain = true)
public class DashboardChargerVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * battery count
     */
    @ApiModelProperty("充电器分类数量")
    private Integer count;
    /**
     * chargerName
     */
    @ApiModelProperty("充电器分类名称")
    private String name;
}
