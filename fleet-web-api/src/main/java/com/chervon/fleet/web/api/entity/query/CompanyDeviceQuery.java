package com.chervon.fleet.web.api.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 租户设备查询条件
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "租户设备查询条件", description = "租户设备查询条件")
public class CompanyDeviceQuery extends PageQuery<CompanyDeviceQuery> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 设备SN
     */
    private String deviceSn;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 设备库存状态：1-In Warehouse，2-Out for Work，3-Unknown location，4-Never Seen
     */
    private Integer warehouseStatus;
    /**
     * 设备id列表
     */
    private List<String> deviceIds;
    /**
     * 一级设备分类编号
     */
    private List<String> firstCategoryCodes;
    /**
     * 是否删除
     */
    private Integer isDelete;

}
