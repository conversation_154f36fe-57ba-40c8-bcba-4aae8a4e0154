package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/12 15:29
 */
@Data
@ApiModel(description = "设备编辑名称对象")
public class RnDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("宿主app类型 android  ios")
    private String appType;

    @ApiModelProperty("宿主包（android、ios 应用的版本）")
    private String appVersion;

    @ApiModelProperty("业务类型：1 ego 2 fleet")
    private Integer businessType;
}
