package com.chervon.fleet.web.api.entity.vo;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备分类及设备数量统计返回对象
 * <AUTHOR>
 * @since 2023-08-17 14:18
 **/
@Data
@ApiModel("设备标签计数Vo,一般使用List包裹,用于APP库存看板")
public class DashboardCategoryCountVo implements Serializable {

    private static final long serialVersionUID = 1;
    /**
     * 分类编号
     */
    @ApiModelProperty("分类编号")
    @Translate(adapter = "fleetCategory", targetField = {"categoryName"})
    private String categoryCode;
    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String categoryName;
    /**
     * 分类数量
     */
    @ApiModelProperty("分类数量")
    private Integer categoryCount;
    /**
     * 分类比例
     */
    @ApiModelProperty("分类比例")
    private BigDecimal categoryRate;
}
