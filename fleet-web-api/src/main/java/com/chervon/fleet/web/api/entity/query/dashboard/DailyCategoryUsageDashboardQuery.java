package com.chervon.fleet.web.api.entity.query.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备分类日使用量看板查询类
 * <AUTHOR>
 * @since 2023-07-28 11:02
 **/
@Data
@ApiModel("设备分类日使用量看板查询类")
public class DailyCategoryUsageDashboardQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备分类ID", required = true)
    private List<String> categoryCodeList;
    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long companyId;
    @ApiModelProperty(value = "起始时间,yyyy-MM-dd字符串", required = true)
    private String startTime;
    @ApiModelProperty(value = "结束时间,yyyy-MM-dd字符串", required = true)
    private String endTime;
}
