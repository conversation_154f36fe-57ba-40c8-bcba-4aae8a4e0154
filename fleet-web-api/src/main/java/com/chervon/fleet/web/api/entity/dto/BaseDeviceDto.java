package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备基本入参对象
 * <AUTHOR>
 * @date 2023/7/3 14:09
 */
@Data
@ApiModel(description = "设备基本入参对象")
public class BaseDeviceDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备id，两个条件任选一个")
    private String deviceId;

    @ApiModelProperty("设备sn，两个条件任选一个")
    private String deviceSn;

    @ApiModelProperty("mac地址")
    private String mac;
}
