package com.chervon.fleet.web.api.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 分类筛选列表条件对象
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CategoryVo implements Serializable  {
    private static final long serialVersionUID = 1L;
    /**
     * 分类编号
     */
   private String categoryCode;
    /**
     * 分类名称
     */
   private String categoryName;
    /**
     * * 子分类列表
     */
   private List<CategoryVo> subCategoryList;
}
