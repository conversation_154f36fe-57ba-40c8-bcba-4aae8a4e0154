package com.chervon.fleet.web.api.entity.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className IotUpdateOnlineStatusDto
 * @description 在线状态更新对象
 * @date 2022/3/23 16:16
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IotUpdateOnlineStatusDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    private String clientId;
    /**
     * 设备上报的状态信息:online/offline
     */
    private String status;
    /**
     * 软网关上报的状态信息（同status）:
     */
    private String eventType;
    /**
     * 时间戳
     */
    private String timestamp;
}
