package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName:DashboardInventoryVo
 * @Description:库存看板vo
 * <AUTHOR>
 * @date 2023/7/24 13:57
 */
@Data
@Accessors(chain = true)
public class DashboardInventoryVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备数量
     */
    @ApiModelProperty("当前公司下设备数量")
    private Integer deviceCount;
    /**
     * 故障数
     */
    @ApiModelProperty("故障数")
    private Integer errorNumber;
    /**
     * 设备总数
     */
    @ApiModelProperty("设备总数")
    private Integer totalNumber;
    /**
     * 在库数
     */
    @ApiModelProperty("在库数")
    private Integer inWarehouseNumber;
    /**
     * 外出工作数
     */
    @ApiModelProperty("外出工作数")
    private Integer outForWorkNumber;
    /**
     * 位置未知数
     */
    @ApiModelProperty("位置未知数")
    private Integer unknownLocationNumber;
    /**
     *  未上报数
     */
    @ApiModelProperty("未上报数")
    private Integer neverSeenNumber;
    /**
     * 设备分类数量统计列表
     */
    @ApiModelProperty("设备分类数量统计列表")
    private List<DashboardCategoryCountVo> categoryCountList;
}
