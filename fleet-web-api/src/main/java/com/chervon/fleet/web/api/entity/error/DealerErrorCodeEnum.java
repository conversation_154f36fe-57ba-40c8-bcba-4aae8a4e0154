package com.chervon.fleet.web.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 网关相关错误信息提示编号
 * 错误码定义：错误码：3位应用名+3位模块名+2+3位流水号
 * *  * fleet web应用名：119+001+2+三位流水号
 *
 * <AUTHOR> 119003200
 * @Description: This is description
 * @Date: 2020/8/13 11:50
 */
public enum DealerErrorCodeEnum implements IError {
    /**
     * 经销商已被收藏
     */
    DEALER_ALREADY_COLLECTED("1190052001", "Dealer is saved to collection"),
    /**
     *经销商未找到
     */
    DEALER_NOT_FOUND("1190052002", "Dealer not found"),
    /**
     *经销商收藏数超过100
     */
    DEALER_FAVORITE_MORE_THAN_100("1190052003", "Total number of saved dealer exceed limit"),
    ;

    DealerErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private final String errorCode;
    /**
     * 描述
     */
    private final String errorReason;

    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
