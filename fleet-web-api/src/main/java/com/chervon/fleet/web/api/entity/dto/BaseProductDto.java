package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 产品基本入参对象
 * <AUTHOR>
 * @date 2023/7/3 16:22
 */
@Data
@ApiModel(description = "产品基本入参对象")
public class BaseProductDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品id，两个条件任选一个")
    private Long productId;

    @ApiModelProperty("产品sn code，两个条件任选一个")
    private String productSnCode;
}
