package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备信息视图对象
 * <AUTHOR>
 * @date 2023/7/10 10:25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayDeviceInfoVo extends DeviceInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *  设备数据上报完成状态：1 上报完成云朵标记  0 超过1小时消失
     */
    @ApiModelProperty(value = "设备数据上报完成状态：1 上报完成云朵标记  0 超过1小时消失")
    private Integer reportCompleteStatus;
    /**
     * 信号强度
     */
    @ApiModelProperty(value = "信号强度")
    private Integer rssi;
}
