package com.chervon.fleet.web.api.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 固定网关坐标信息对象
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GatewayCoordinateVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 坐标
     */
    private String coordinate;

}
