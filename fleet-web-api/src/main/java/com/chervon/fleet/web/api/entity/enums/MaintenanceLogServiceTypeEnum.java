package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 软网关类型
 */
public enum MaintenanceLogServiceTypeEnum implements TypeEnum {
    /**
     * 维护
     */
    MAINTENANCE(1, "Maintenance"),
    /**
     * 检查
     */
    INSPECTION(2, "Inspection"),
    /**
     * 修复
     */
    REPAIR(3, "Repair"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    MaintenanceLogServiceTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(MaintenanceLogServiceTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static MaintenanceLogServiceTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
