package com.chervon.fleet.web.api.entity.query.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 电池使用情况看板查询类
 * <AUTHOR>
 * @since 2023-07-31 16:18
 **/
@Data
@ApiModel("电池使用情况看板查询类")
public class BatteryUsageDashboardQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long companyId;
    @ApiModelProperty(value = "电池分类", required = true)
    private List<String> batteryTypeList;
    @ApiModelProperty(value = "起始时间,yyyy-MM-dd字符串", required = true)
    private String startTime;
    @ApiModelProperty(value = "结束时间,yyyy-MM-dd字符串", required = true)
    private String endTime;
}
