package com.chervon.fleet.web.api.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 语言信息列表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LanguageVo implements Serializable {

    private static final long serialVersionUID = 8382078016804452696L;


    @ApiModelProperty("语言")
    private String language;

    @ApiModelProperty("语言缩写")
    private String langCode;

}
