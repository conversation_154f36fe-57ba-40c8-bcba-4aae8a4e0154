package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 网关设备列表查询入参
 * <AUTHOR>
 * @date 2023/7/13 17:54
 */
@Data
@ApiModel(description = "网关设备列表查询入参")
public class GatewayDeviceListDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id集合
     */
    @ApiModelProperty("设备id集合")
    private List<String> deviceIds;
    /**
     * 网关id
     */
    @ApiModelProperty("网关id")
    private String gatewayId;
}
