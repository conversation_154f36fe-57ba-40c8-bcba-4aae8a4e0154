package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 网关添加移除请求处理对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GatewayChangeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "网关id")
    private String gatewayId;

    @ApiModelProperty("租户id")
    private Long companyId;

    @ApiModelProperty("增减状态：1增加操作  2移除操作")
    private Integer status;
}
