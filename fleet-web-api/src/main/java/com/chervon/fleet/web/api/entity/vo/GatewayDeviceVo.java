package com.chervon.fleet.web.api.entity.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 网关设备对象信息
 */
@Data
@Accessors(chain = true)
public class GatewayDeviceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 网关名称
     */
    private String name;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 网关类型：1固定网关   2移动网关
     */
    private Integer gatewayType;
    /**
     * 网关模式：0 正常   1省电
     */
    private Integer gatewayMode;
    /**
     * 固定网关位置：经度
     */
    private BigDecimal longitude;
    /**
     * 固定网关位置：纬度
     */
    private BigDecimal latitude;
    /**
     * 网关地址信息
     */
    private String location;
    /**
     * mac地址
     */
    private String mac;
    /**
     * 网关在线状态: 1-online   2-offline
     */
    private Integer onlineStatus;
    /**
     * 是否删除：0 正常  1已删除
     */
    private Integer isDeleted;
    /**
     * 设备品牌：samsung
     */
    private String deviceBrand;
    /**
     * 设备型号
     */
    private String deviceModel;
    /**
     * 系统版本
     */
    private String deviceSysVersion;

}
