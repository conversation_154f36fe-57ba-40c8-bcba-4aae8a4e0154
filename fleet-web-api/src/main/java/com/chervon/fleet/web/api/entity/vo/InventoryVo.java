package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备对象
 * <AUTHOR>
 * @date 2023/7/17 11:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(description = "设备对象")
public class InventoryVo extends DeviceInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备数据上报完成状态：1 上报完成云朵标记  0 超过1小时消失
     */
    @ApiModelProperty("设备数据上报完成状态：1 上报完成云朵标记  0 超过1小时消失")
    private Integer reportCompleteStatus;
    /**
     * 库存状态 0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location
     */
    @ApiModelProperty("库存状态 0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location")
    private Integer warehouseStatus;
    /**
     * 在线状态 0 离线 1 在线
     */
    @ApiModelProperty("在线状态 0 离线 1 在线")
    private Integer onlineStatus;
    /**
     * 故障状态 0 无故障 1 有故障
     */
    @ApiModelProperty("故障状态 0 无故障 1 有故障")
    private Integer errorStatus;

}
