package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 设备租户绑定解绑变更请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DeviceBindChangeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty("租户id")
    private Long companyId;

    @ApiModelProperty("网关id")
    private String gatewayId;

    @ApiModelProperty("绑定解绑状态：1绑定操作  2解绑操作")
    private Integer status;
}
