package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 网关请求对象
 * <AUTHOR>
 * @date 2023/6/30 16:23
 */
@Data
@ApiModel(description = "网关请求对象")
public class GatewayDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 网关id，新增不要，编辑要
     */
    @ApiModelProperty("网关id，新增不要，编辑要")
    private String gatewayId;
    /**
     * 网关名称
     */
    @ApiModelProperty("网关名称")
    private String gatewayName;
    /**
     *  软件网关类型：1固定网关 2移动网关
     */
    @ApiModelProperty("软件网关类型：1固定网关 2移动网关")
    private Integer gatewayType;
    /**
     * 网关类型：1 硬件网关 2 软件网关
     */
    @ApiModelProperty("网关类型：1 硬件网关 2 软件网关")
    private Integer type;
    /**
     * 硬件网关对应的设备id
     */
    @ApiModelProperty("硬件网关对应的设备id")
    private String deviceId;
    /**
     * 固定网关坐标：纬度，经度逗号分隔
     */
    @ApiModelProperty("固定网关坐标：纬度，经度逗号分隔")
    private String coordinate;
    /**
     * 网关地址信息
     */
    @ApiModelProperty("网关地址信息")
    private String location;
    /**
     * 手机：唯一地址
     */
    @ApiModelProperty("手机：唯一地址")
    private String uniqueId;
    /**
     * 手机：品牌
     */
    @ApiModelProperty("手机：品牌")
    private String deviceBrand;
    /**
     * 手机：型号
     */
    @ApiModelProperty("手机：型号")
    private String deviceModel;
    /**
     * 手机：系统版本
     */
    @ApiModelProperty("手机：系统版本")
    private String deviceSysVersion;
    /**
     * fleet app版本
     */
    @ApiModelProperty("fleet app版本")
    private String appVersion;

}
