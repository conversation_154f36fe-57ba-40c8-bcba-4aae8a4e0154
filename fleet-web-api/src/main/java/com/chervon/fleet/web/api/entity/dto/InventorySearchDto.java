package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 库存搜索对象
 * <AUTHOR>
 * @date 2023/7/17 10:54
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "库存搜索对象")
public class InventorySearchDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 搜索类型，1 库存 2 盘点
     */
    @ApiModelProperty("搜索类型，1 库存 2 盘点")
    private Integer searchType;
    /**
     * 盘点时必传网关id
     */
    @ApiModelProperty("盘点时必传网关id")
    private String gatewayId;
    /**
     * 设备名称，支持模糊搜索
     */
    @ApiModelProperty("设备名称，支持模糊搜索")
    private String deviceName;
    /**
     *  选中的二级品类编码集合，如果所属一级品类编码被选中，则传该下的所有二级品类
     */
    @ApiModelProperty("选中的二级品类编码集合，如果所属一级品类编码被选中，则传该下的所有二级品类")
    private List<String> categoryCodes;
    /**
     * 选中的库存状态集合
     */
    @ApiModelProperty("选中的库存状态集合")
    private List<Integer> warehouseStatus;
    /**
     * 选中的标签id集合
     */
    @ApiModelProperty("选中的标签id集合")
    private List<Long> tagIds;
    /**
     * 如果网关没传，则用这个为数据基数，如果传了，则此熟悉无效
     */
    @ApiModelProperty("如果网关没传，则用这个为数据基数，如果传了，则此熟悉无效")
    private List<String> deviceIds;

}
