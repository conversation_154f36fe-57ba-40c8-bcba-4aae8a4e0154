package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-07-13 17:12
 **/
@Data
public class RnDetailVo implements Serializable {
    /**
     * 设备所属产品ID
     */
    @ApiModelProperty("设备所属产品ID")
    private Long productId;
    /**
     * 设备出厂时烧录的ID，唯一标识：设备类型+mes码
     */
    @ApiModelProperty("设备出厂时烧录的ID，唯一标识：设备类型+mes码")
    private String deviceId;
    /**
     * 设备SN
     */
    @ApiModelProperty("设备SN")
    private String sn;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 设备版本号
     */
    @ApiModelProperty("设备版本号")
    private String version;
    /**
     * 是否在线 1：在线 0：离线
     */
    @ApiModelProperty("是否在线 1：在线 0：离线")
    private Integer isOnline;
    /**
     * 设备图片地址在S3中存储Key
     */
    @ApiModelProperty("设备图片地址在S3中存储Key")
    private String deviceIcon;
    /**
     * 设备类型，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网
     */
    @ApiModelProperty("通讯方式:wifi > 4G > BLE > DT> LAN >noNetworked")
    private String communicateMode;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;
    /**
     * 设备MAC地址
     */
    @ApiModelProperty("设备MAC地址")
    private String mac;
    /**
     * 绑定时间
     */
    @ApiModelProperty("绑定时间")
    private Long bindTime;

    @ApiModelProperty("rnBundleName")
    private String rnBundleName;
    /**
     * 总成零件序列号列表
     */
    @ApiModelProperty("总成零件序列号列表")
    private List<String> assemblySnList;

    @ApiModelProperty(value = "商品型号")
    private String commodityModel;

    @ApiModelProperty(value = "设备信息有效时长，单位秒")
    private Long period;

    @ApiModelProperty(value = "离线天数")
    private Long offlineDays;

    @ApiModelProperty(value = "设备状态 0：禁用 1：启用")
    private Integer enableStatus;
}
