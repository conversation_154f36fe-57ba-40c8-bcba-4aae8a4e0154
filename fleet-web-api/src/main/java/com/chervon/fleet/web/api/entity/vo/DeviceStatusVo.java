package com.chervon.fleet.web.api.entity.vo;

import com.chervon.fleet.web.api.entity.enums.ReportCompleteStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备状态响应对象
 *
 * <AUTHOR> 2023/7/12
 */
@Data
public class DeviceStatusVo implements Serializable {
    private static final long serialVersionUID = 1L;
    //毫秒转小时
    private static final long MS_TO_HOUR = 3600*1000L;
    @ApiModelProperty(value = "网关id")
    private String gatewayId;

    @ApiModelProperty(value = "租户id")
    private Long companyId;

    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 库存状态
     *
     * @see com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum
     */
    @ApiModelProperty(value = "库存状态")
    private Integer warehouseStatus;
    /**
     * 网关坐标
     */
    @ApiModelProperty("网关的坐标，逗号分隔：51.175330,-4.044769")
    private String coordinate;
    /**
     * 地址信息
     */
    private String location;
    /**
     * 设备连接信号强度
     */
    @ApiModelProperty(value = "信号强度")
    private Integer rssi;
    /**
     * 设备上报数据完成时间戳
     */
    @ApiModelProperty(value = "设备上报数据完成时间戳")
    private long dataReportCompleteTime;

    @ApiModelProperty(value = "设备数据上报完成状态：1上报完成云朵标记  0超过1小时消失")
    private Integer reportCompleteStatus;

    /**
     * * 上报完成
     *
     * @return
     */
    public Integer calcCompleteStatus() {
        long hour = (System.currentTimeMillis() - dataReportCompleteTime) / MS_TO_HOUR;
        if (hour > 1) {
            return ReportCompleteStatusEnum.REPORT_TIMED_OUT.getType();
        }
        return ReportCompleteStatusEnum.REPORT_COMPLETE.getType();
    }
}
