package com.chervon.fleet.web.api.entity.query.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @document 设备故障列表看板查询类
 * <AUTHOR>
 * @since 2023-07-27 17:36
 **/
@Data
@ApiModel("设备故障列表看板查询类")
public class DeviceErrorListDashboardQuery implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID,非必填")
    private String deviceId;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long companyId;
    /**
     * 查询条件：排除充电器和电池包的品类故障列表
     */
    @ApiModelProperty(value = "查询条件：排除充电器和电池包的品类故障列表")
    private boolean queryToolCategory = true;
    /**
     * BATTERY("Battery"),
     * CHARGER("Charger"),
     * POWER_UNIT("PowerUnit"),
     * RIDE_ON("RideOn"),
     * WALK_BEHIND("WalkBehind"),
     * HANDHELD("Handheld");
     */
    @ApiModelProperty(value = "按指定一级分类编码查询故障列表: 充电器编码：Charger 电池包编码：Battery")
    private String firstCategoryCode;
}