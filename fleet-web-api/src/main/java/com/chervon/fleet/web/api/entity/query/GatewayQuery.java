package com.chervon.fleet.web.api.entity.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 网关查询对象
 * @ClassName : GatewayQuery
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GatewayQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 批量查询网关id
     */
    private List<String> listGatewayId;

    /**
     * 硬件网关对应的设备id
     */
    private String deviceId;
    /**
     * 网关名称
     */
    private String name;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 网关类型：1固定网关   2移动网关
     */
    private Integer gatewayType;
    /**
     * 网关设备唯一码
     */
    private String uniqueId;
    /**
     * 网关在线状态: 1-online   2-offline
     */
    private Integer onlineStatus;
    /**
     * 是否删除：0 正常  1已删除
     */
    private Integer isDeleted;
}
