package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 库存搜索输出对象
 * <AUTHOR>
 * @date 2023/7/17 13:40
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "库存搜索输出对象")
public class InventorySearchVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备列表数据
     */
    @ApiModelProperty("设备列表数据")
    private List<InventoryVo> inventories;
    /**
     * 库存：租户下总设备数量，盘点：设备盘点总数
     */
    @ApiModelProperty("库存：租户下总设备数量，盘点：设备盘点总数")
    private Long totalCount;
    /**
     *  符合筛选条件的设备数量，如果没有筛选，则为空
     */
    @ApiModelProperty("符合筛选条件的设备数量，如果没有筛选，则为空")
    private Long searchCount;
    /**
     * 是否筛选过，0 没有筛选 1 筛选过
     */
    @ApiModelProperty("是否筛选过，0 没有筛选 1 筛选过")
    private Integer isFilter;
}
