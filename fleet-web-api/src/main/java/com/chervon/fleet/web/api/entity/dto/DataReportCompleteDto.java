package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备数据上报完成状态请求对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DataReportCompleteDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据完成上报的时间")
    private Long reportCompleteTime;

    @ApiModelProperty("topic上报时间戳")
    private Long timestamp;
}
