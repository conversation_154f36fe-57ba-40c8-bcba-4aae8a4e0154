package com.chervon.fleet.web.api.entity.vo;

import com.chervon.common.web.annotation.Translate;
import com.chervon.common.web.entity.ConvertType;
import com.chervon.fleet.web.api.entity.enums.MaintenanceStatusEnum;
import com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 库存状态看板Vo
 * <AUTHOR>
 * @since 2023-07-27 15:24
 **/
@Data
@Accessors(chain = true)
@ApiModel("库存状态看板Vo")
public class InventoryStatusDashboardVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 库存状态枚举key
     *
     * @see com.chervon.fleet.web.api.entity.enums.WarehouseStatusEnum
     */
    @ApiModelProperty("库存状态枚举key")
    @Translate(type = ConvertType.ENUM, enumName = "WarehouseStatusEnum", adapter = "I18NResource", targetField = {"inventoryType"})
    private Integer key;
    /**
     * 库存状态名称：out for work、in warehouse、Unknown Current Location、never seen
     */
    @ApiModelProperty("库存状态名称：out for work、in warehouse、Unknown Current Location、never seen")
    private String inventoryType;
    /**
     * 库存状态数量
     */
    @ApiModelProperty("库存状态数量")
    private Integer inventoryCount;

    /**
     * 设置库存状态名称
     * @param inventoryType 库存状态名称
     */
    public void setInventoryType(String inventoryType) {
        this.inventoryType=inventoryType;
        final Integer typeByDesc = WarehouseStatusEnum.getTypeByDesc(inventoryType);
        this.key = typeByDesc;
    }
}
