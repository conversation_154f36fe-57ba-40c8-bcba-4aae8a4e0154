package com.chervon.fleet.web.api.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 库存状态查询对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "库存状态查询对象")
public class DeviceShadowQuery extends PageQuery {
    private static final long serialVersionUID = 1L;
    /**
     * 租户id，必填
     */
    @ApiModelProperty(value = "租户id，必填")
    private Long companyId;
    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;
}
