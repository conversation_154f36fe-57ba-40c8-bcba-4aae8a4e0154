package com.chervon.fleet.web.api.entity.vo;

import com.chervon.common.core.annotation.Sensitive;
import com.chervon.common.core.enums.SensitiveStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 网关对象
 * <AUTHOR>
 * @date 2023/7/14 11:10
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "网关对象")
public class GatewayVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 网关id
     */
    @ApiModelProperty("网关id")
    private String gatewayId;
    /**
     * 网关名称
     */
    @ApiModelProperty("网关名称")
    private String gatewayName;
    /**
     * 软网关类型：1固定网关 2移动网关
     */
    @ApiModelProperty("软网关类型：1固定网关 2移动网关")
    private Integer gatewayType;
    /**
     * 网关类型：1硬件网关 2软件网关
     */
    @ApiModelProperty("网关类型：1 硬件网关 2 软件网关")
    private Integer type;
    /**
     * 固定网关坐标：纬度，经度逗号分隔
     */
    @ApiModelProperty("固定网关坐标：纬度，经度逗号分隔")
    private String coordinate;
    /**
     * 网关地址信息
     */
    @ApiModelProperty("网关地址信息")
    private String location;
    /**
     * 硬件网关对于的设备id
     */
    @ApiModelProperty("硬件网关对于的设备id")
    private String deviceId;
    /**
     * 网关在线状态: 1-online   0-offline
     */
    @ApiModelProperty("网关在线状态: 1-online   0-offline")
    private Integer onlineStatus;
    /**
     * 手机唯一码
     */
    @ApiModelProperty("手机唯一码")
    private String uniqueId;
    /**
     * 最后一次连接时间
     */
    @ApiModelProperty("最后一次连接时间")
    private Long lastConnectedTime;
    /**
     * 设备品牌
     */
    @ApiModelProperty("设备品牌")
    private String deviceBrand;
    /**
     *  设备型号
     */
    @ApiModelProperty("设备型号")
    private String deviceModel;
    /**
     * 系统版本
     */
    @ApiModelProperty("系统版本")
    private String deviceSysVersion;
    /**
     * app应用版本
     */
    @ApiModelProperty("app应用版本")
    private String appVersion;
    /**
     * 邮箱（脱敏）
     */
    @ApiModelProperty("邮箱")
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String email;
    /**
     * 连接设备数量
     */
    @ApiModelProperty("连接设备数量")
    private Integer connectedDeviceNumber;
    /**
     * 设备SN
     */
    @ApiModelProperty("设备SN")
    private String sn;
    /**
     * 设备固件技术版本
     */
    @ApiModelProperty("设备固件技术版本")
    private String technologyVersion;
    /**
     * 子设备数量
     */
    @ApiModelProperty("子设备数量")
    private Integer subDeviceNumber;
    /**
     * 产品商品型号
     */
    @ApiModelProperty("产品商品型号")
    private String commodityModel;

}
