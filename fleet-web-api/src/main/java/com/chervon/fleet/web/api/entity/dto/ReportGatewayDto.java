package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 网关扫描设备状态（扫描到未扫描到状态）上报子对象
 * <AUTHOR>
 * @date 2023/6/30 16:23
 */
@Data
@ApiModel(description = "移动网关上报位置请求对象")
public class ReportGatewayDto implements Serializable {
    /**
     * 网关类型
     */
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "网关类型：1固定网关，2移动网关")
    private Integer gatewayType;
    /**
     * "location": "31.927370302614396,118.80786875268647",
     */
    @ApiModelProperty("固定网关坐标：纬度，经度逗号分隔")
    private String location;
    /**
     * 网关地址信息：China, <PERSON>, <PERSON>, <PERSON>, 天元西路99号
     */
    @ApiModelProperty("网关地址信息")
    private String address;
    /**
     * 网关上报时间戳
     */
    @ApiModelProperty("网关上报时间戳")
    private Long reportTime;
}
