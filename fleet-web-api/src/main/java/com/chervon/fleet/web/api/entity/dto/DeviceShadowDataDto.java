package com.chervon.fleet.web.api.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 租户设备更新请求对象
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class DeviceShadowDataDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 设备电量 1011
     */
    private Integer power;
    /**
     * 充电器状态 1011
     */
    private Integer chargerState;
    /**
     * 电池状态 2028
     */
    private Integer batteryState;
    /**
     * 上报时间
     */
    private Long timestamp;

}
