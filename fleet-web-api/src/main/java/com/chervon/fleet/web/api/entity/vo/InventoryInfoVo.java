package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 设备对象
 * <AUTHOR>
 * @date 2023/7/17 11:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(description = "设备对象")
public class InventoryInfoVo extends InventoryVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备位置
     */
    @ApiModelProperty("设备位置")
    private String location;
    /**
     * 标签集合
     */
    @ApiModelProperty("标签集合")
    private List<TagVo> tags;
    /**
     *  故障代码
     */
    @ApiModelProperty("故障代码")
    private String errorCode;
    /**
     * 故障提示
     */
    @ApiModelProperty("故障提示")
    private String errorMessage;
    /**
     * 处理建议内容
     */
    @ApiModelProperty("处理建议内容")
    private String suggestionContent;
    /**
     * 网关的坐标
     */
    @ApiModelProperty("网关的坐标，逗号分隔：51.175330,-4.044769")
    private String coordinate;
    /**
     * 硬件网关设备能否编辑网关信息 0 不能 1 能
     */
    @ApiModelProperty("硬件网关设备能否编辑网关信息 0 不能 1 能")
    private Integer canEdit;

}
