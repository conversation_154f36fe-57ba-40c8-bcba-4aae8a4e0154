package com.chervon.fleet.web.api.entity.consts;

import java.util.regex.Pattern;

/**
 * @Description 常量类
 * <AUTHOR>
 * @date 2023/7/5 14:15
 */
public class IotConstant {

    /**
     * 15位多码正则校验
     */
    public static final String DEVICE_CODE_CHECK = "^[N,A,E,Z][A-Z,0-9]{4}[0-9]{9}[A-Z,0-9]";
    /**
     * 设备多码正则校验Pattern对象
     */
    public static final Pattern DEVICE_CODE_REG = Pattern.compile(DEVICE_CODE_CHECK);
    /**
     * 16位非IOT设备多码校验
     */
    public static final String NO_IOT_DEVICE_CODE_CHECK = "^[R][N,A,E,Z][A-Z,0-9]{4}[0-9]{9}[A-Z,0-9]";
    /**
     * 非IOT设备多码正则校验Pattern对象
     */
    public static final Pattern NO_IOT_CODE_REG = Pattern.compile(NO_IOT_DEVICE_CODE_CHECK);
}
