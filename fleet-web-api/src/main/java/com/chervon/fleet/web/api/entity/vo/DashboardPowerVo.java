package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * DashboardPowerVo
 * 充电看板vo
 * <AUTHOR>
 * @date 2023/7/24 14:20
 */
@Data
@Accessors(chain = true)
public class DashboardPowerVo implements Serializable {
    /**
     * Constructor
     */
    public DashboardPowerVo() {
        this.haveData = true;
    }

    private static final long serialVersionUID = 1L;
    /**
     * 当前公司下设备数量
     */
    @ApiModelProperty("当前公司下设备数量")
    private Integer deviceCount;
    /**
    * 租户下充电器汇总状态：
     * 全部故障：-4
     * 全部都充满：6
     * 存在故障就是故障：4
     * 没故障存在过温就是过温：3
     * 剩下所有情况都是：1 Normal充电中
     */
    @ApiModelProperty("充电状态: 1 Normal, 3 Overheat, 4 x Errors, 6 Fully Charged")
    private Integer chargeStatus;

    //故障统计：t_bi_power_hub_charging中2016设备影子：chargerState
    /**
     * 故障数
     */
    @ApiModelProperty("故障数")
    private Integer errorNumber;
    /**
     * 剩余充电时长--分钟
     */
    @ApiModelProperty("剩余充电时长--分钟")
    private String remainingChargingTime;
    /**
     * 充电完成时间--时间戳
     */
    @ApiModelProperty("充电完成时间--时间戳")
    private Long completionTime;
    /**
     * 是否展示Overview详情模块
     */
    @ApiModelProperty("是否展示Overview详情模块,如果租户下都是CH7000,百分比有效，就不展示下面的详情（百分比=0，返回true）")
    private Boolean isShowOverview;
    /**
     * 是否有数据上报
     */
    @ApiModelProperty("是否有数据上报")
    private Boolean haveData;
}
