package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 产品故障信息Dto
 * <AUTHOR>
 * @since 2023-08-23 18:58
 **/
@Data
public class ProductFaultDto implements Serializable {

    private static final long serialVersionUID = 1;
    /**
     * 告警消息id
     */
    @ApiModelProperty("告警消息id")
    private Long faultMessageId;
    /**
     * 产品id,仅Dto
     */
    @ApiModelProperty("产品id,仅Dto")
    private Long productId;
    /**
     * 产品SN编码
     */
    private String productSnCode;
    /**
     * 消息模板ID,仅Dto
     */
    @ApiModelProperty("消息模板ID,仅Dto")
    private Long messageTemplateId;
    /**
     * 故障编号
     */
    @ApiModelProperty("故障编号")
    private String faultCode;
    /**
     * 故障标题
     */
    @ApiModelProperty("故障标题")
    private String faultTitle;
    /**
     * 语言编码
     */
    @ApiModelProperty("语言编码")
    private String language;
    /**
     * 处理建议id
     */
    @ApiModelProperty("处理建议id")
    private Long suggestionId;
    /**
     * 处理建议内容
     */
    @ApiModelProperty("处理建议内容")
    private String suggestionContent;
}
