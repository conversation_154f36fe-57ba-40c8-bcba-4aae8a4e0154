package com.chervon.fleet.web.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 网关相关错误信息提示编号
 * fleet web应用名：119+002+2+三位流水号
 * <AUTHOR>
 * @Description: This is description
 * @Date: 2023/7/13 11:50
 */
public enum GatewayErrorCodeEnum implements IError {
    /**
     * 1190022001 用户下已存在软件网关，不能创建新的软件网关
     */
    SOFT_GATEWAY_ALREADY_EXIST("1190022001", "A software gateway already exists under the user, and a new software gateway cannot be created"),
    /**
     * 1190022002 网关名称已存在
     */
    GATEWAY_NAME_EXIST("1190022002", "Gateway name already exists"),
    /**
     * 1190022003 根据gatewayId，未查询到网关
     */
    GATEWAY_NOT_EXIST("1190022003", "According to gatewayId, no gateway information was found"),
    /**
     * 1190022004 设备未绑定到租户下，不能创建硬件网关
     */
    DEVICE_NOT_BOUND_CANNOT_CREATE_HARD_GATEWAY("1190022004", "The device is not bound to the tenant and cannot create a hardware gateway"),
    /**
     * 1190022005 不是网关设备，不能创建硬件网关
     */
    NOT_GATEWAY_DEVICE_CANNOT_CREATE_HARD_GATEWAY("1190022005", "Not a gateway device, unable to create hardware gateway"),
    ;

    GatewayErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private final String errorCode;
    /**
     * 描述
     */
    private final String errorReason;

    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
