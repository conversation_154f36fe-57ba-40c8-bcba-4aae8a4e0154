package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * PowerBankAvailabilityVo PowerBank设备详情Power Availability各模式下参数
 * <AUTHOR>
 * @date 2024/6/12 13:56
 */
@Data
@Accessors(chain = true)
public class PowerBankAvailabilityVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 电池包索引类型：0：大包  1：小包1  2：小包2
     */
    private String index;
    /**
     * 标称安时数
     */
    @ApiModelProperty("电池包安时类型")
    private String batteryType;
    /**
     * 充电状态
     */
    @ApiModelProperty("充电状态")
    private Integer chargingStatus;
    /**
     * 充电百分比
     */
    @ApiModelProperty("充电百分比")
    private BigDecimal chargingPercentage;;
}
