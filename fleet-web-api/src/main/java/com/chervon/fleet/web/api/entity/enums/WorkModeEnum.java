package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * powerBank充电器工作模式： 1 AC 2 DC 3待机  4故障
 * <AUTHOR> 2023/5/25
 */
public enum WorkModeEnum {
    /**
     * AC:交流充电  DC:直流充电  Idle:待机  Fault:故障
     */
    AC(1,"AC"),
    /**
     * AC:交流充电  DC:直流充电  Idle:待机  Fault:故障
     */
    DC(2,"DC"),
    /**
     * AC:交流充电  DC:直流充电  Idle:待机  Fault:故障
     */
    IDLE(3,"Idle"),
    /**
     * AC:交流充电  DC:直流充电  Idle:待机  Fault:故障
     */

    FAULT(4,"Fault"),
    ;
    /**
     * type值
     */
    private final int type;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    WorkModeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取type值
     * @return
     */
    public int getType() {
        return this.type;
    }

    /**
     * 获取描述
     * @return
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 获取枚举
     */
    public static WorkModeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}