package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * 电池状态2028
 * 0：保留
 * 1：充电中
 * 2：放电中
 * 3：充电满
 * 4：充电失败
 * 5：等待中（空闲）
 * 6：过温等待
 * 7：故障
 * <AUTHOR> 2023/9/5
 */
public enum BatteryState2028Enum {
    /**
     * 未知状态
     */
    Unknown(0,"未知状态"),
    /**
     * 充电中
     */
    Charging(1, "充电中"),
    /**
     * 放电中
     */
    Discharging(2,"放电中"),
    /**
     * 充电完成
     */
    Ready(3, "充电满"),
    /**
     * 充电失败
     */
    Fail(4, "充电失败"),
    /**
     * 等待中（空闲）
     */
    StandBy(5,"等待中（空闲）"),
    /**
     * 过温等待
     */
    OverHot(6, "过温等待"),
    /**
     * 故障
     */
    Fault(7, "故障")
            ;

    /**
     * 类型
     */
    private final int type;

    /**
     * 值
     */
    private final String value;


    /**
     * 构造函数
     * @param type 类型值
     * @param value 值描述
     */
    BatteryState2028Enum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取类型
     */
    public int getType() {
        return type;
    }

    /**
     * 获取值
     */
    public String getValue() {
        return value;
    }


    /**
     * 获取枚举
     */
    public static BatteryState2028Enum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(Unknown);
    }
}
