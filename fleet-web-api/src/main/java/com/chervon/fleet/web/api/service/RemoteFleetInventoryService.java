package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.dto.InventorySearchDto;
import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;
import com.chervon.fleet.web.api.entity.vo.InventoryInfoVo;
import com.chervon.fleet.web.api.entity.vo.InventorySearchVo;

/**
 * 远程库存服务
 * <AUTHOR>
 * @date 2023/7/17 10:35
 */
public interface RemoteFleetInventoryService {

    /**
     * 根据租户id查询过滤条件
     *
     * @param search 搜索设备名称
     * @return 过滤条件
     * @throws ServiceException 自定义内部服务异常
     */
    FilterConditionVo filterCondition(String search) throws ServiceException;

    /**
     * 根据filter条件和输入的设备名称筛选出的数据数量，区分库存和盘点
     *
     * @param req 查询条件
     * @return 符合条件的个数
     * @throws ServiceException 自定义内部服务异常
     */
    Long countFilterAndSearch(InventorySearchDto req) throws ServiceException;

    /**
     * 根据filter条件和输入的设备名称筛选出的数据列表，区分库存和盘点
     *
     * @param req 搜索条件
     * @return 结果列表
     * @throws ServiceException 自定义内部服务异常
     */
    InventorySearchVo listFilterAndSearch(InventorySearchDto req) throws ServiceException;

    /**
     * 设备详情
     *
     * @param deviceId 设备id
     * @return 设备详情
     * @throws ServiceException 自定义内部服务异常
     */
    InventoryInfoVo detail(String deviceId) throws ServiceException;
}
