package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * 维保状态类型
 */
public enum MaintenanceStatusEnum {
    /**
     * 维保状态类型:
     * 1: 到期
     * 2: 正常
     * 3: 关闭
     */
    DUE(1, "Service Due"),
    /**
     * 2: 正常
     */
    ON(2, "Normal"),
    /**
     * 3: 关闭
     */
    OFF(3, "OFF"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述,fleet_platform.t_bi_maintenance表中对应字段值
     */
    private String desc;

    MaintenanceStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
            .filter(x -> x.getType() == type)
            .map(MaintenanceStatusEnum::getDesc)
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static MaintenanceStatusEnum getEnum(int type) {
        return Arrays.stream(values())
            .filter(x -> x.getType() == type)
            .findFirst()
            .orElse(null);
    }

    /**
     * 根据数据库值获取对应key
     *
     * @param desc 数据库值
     * @return key
     */
    public static Integer getTypeByDesc(String desc) {
        return Arrays.stream(values())
            .filter(x -> Objects.equals(x.getDesc(), desc))
            .map(MaintenanceStatusEnum::getType)
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }
}
