package com.chervon.fleet.web.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 网关相关错误信息提示编号
 * 错误码定义：错误码：3位应用名+3位模块名+2+3位流水号
 *  *  * fleet web应用名：119+001+2+三位流水号
 * <AUTHOR> 119003200
 * @Description: This is description
 * @Date: 2020/8/13 11:50
 */
public enum MaintenanceErrorCodeEnum implements IError {
    /**
     * 未找到维保信息
     */
    MAINTENANCE_NOT_FOUND("1190042001", "No maintenance information found"),
    /**
     * 未找到维保日志信息
     */
    MAINTENANCE_LOG_NOT_FOUND("1190042002", "No maintenance log information found"),
    /**
     * 维保日志，使用时长不正确
     */
    MAINTENANCE_LOG_RUNTIME_ERROR("1190042003", "Maintenance log, incorrect usage duration"),
    /**
     * 维保截止时间格式不正确
     */
    MAINTENANCE_DEADLINE_TIME_ERROR("1190042004", "The maintenance deadline format is incorrect"),

    ;

    MaintenanceErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private final String errorCode;
    /**
     * 描述
     */
    private final String errorReason;

    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
