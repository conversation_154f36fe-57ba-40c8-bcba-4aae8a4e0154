package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 网关扫描到设备在线状态后发布mqtt消息体负载实体
 * 给 android端订阅刷新设备库存状态 *
 * <AUTHOR> 2023/7/19
 */
@Accessors(chain = true)
@Data
public class MqttMessageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消息唯一id")
    private String messageId;
    /**
     * topic消息业务类型：1设备库存状态变更  2设备在线离线状态变更  3设备数据上报完成云朵状态通知 4设备绑定解绑变更*
     * @see //com.chervon.fleet.web.entity.enums.MqttMessageTypeEnum
     */
    @ApiModelProperty("topic消息业务类型：1设备库存状态变更  2设备在线离线状态变更  3设备数据上报完成云朵状态通知 4设备绑定解绑变更")
    private Integer messageType;

    @ApiModelProperty("消息体")
    private Object messageBody;
}
