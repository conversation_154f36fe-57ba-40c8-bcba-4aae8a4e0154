package com.chervon.fleet.web.api.entity.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet网关上报扫描到的设备信息
 * <AUTHOR>
 * @date 2023/7/12
 */
@Data
@Accessors(chain = true)
@ApiModel("fleet网关上报扫描到的设备信息")
public class ReportDeviceDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "1扫描到，0未扫描到")
    private Integer status;

    @ApiModelProperty(value = "蓝牙信号强度:大约0~-96dBm")
    private Integer rssi;

    @ApiModelProperty("网关上报时间戳")
    private Long reportTime;

}
