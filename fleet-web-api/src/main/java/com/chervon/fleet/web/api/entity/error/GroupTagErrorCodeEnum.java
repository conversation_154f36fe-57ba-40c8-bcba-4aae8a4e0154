package com.chervon.fleet.web.api.entity.error;

import com.chervon.common.core.error.IError;

/**
 * 网关相关错误信息提示编号
 * *********
 * <AUTHOR>
 * @Description: This is description
 * @Date: 2020/8/13 11:50
 */
public enum GroupTagErrorCodeEnum implements IError {
    /**
     * company had a default tag group called 'My Tags
     */
    COMPANY_HAD_DEFAULT_TAG_GROUP("1190032001", "company had a default tag group called 'My Tags'"),
    /**
     * group name is existed
     */
    GROUP_NAME_EXISTED("1190032002", "Duplicate group names are not allowed"),
    /**
     * the number of groups exceeds 100
     */

    GROUP_EXCEEDS_100("1190032003", "the number of groups exceeds 100"),
    /**
     * group does not exist
     */
    GROUP_NOT_EXIST("1190032004", "group does not exist"),
    /**
     *  default group cannot be modified
     */
    GROUP_DEFAULT_CANNOT_EDITED("1190032005", "default group cannot be modified"),
    /**
     * default group cannot be deleted
     */
    GROUP_DEFAULT_CANNOT_DELETED("1190032006", "default group cannot be deleted"),
    /**
     * tag name is existed
     */
    TAG_NAME_EXISTED("1190032007", "Duplicate tag names are not allowed"),
    /**
     * the number of tags exceeds 100
     */
    TAG_EXCEEDS_100("1190032008", "the number of tags exceeds 100"),
    /**
     * tag does not exist
     */
    TAG_NOT_EXIST("1190032009", "tag does not exist"),


    ;

    GroupTagErrorCodeEnum(String errorCode, String errorReason) {
        this.errorCode = errorCode;
        this.errorReason = errorReason;
    }

    /**
     * 编号
     */
    private final String errorCode;
    /**
     * 描述
     */
    private final String errorReason;

    public String getCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return errorReason;
    }


}
