package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 在线状态使用类型 1 网关
 * @Date: 2023/7/20 13:34
 */
public enum OnlineStatusTypeEnum {

    /**
     * 网关
     */
    GATEWAY(1, "gateway"),
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type 类型值
     * @param value 值
     */
    OnlineStatusTypeEnum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }


    /**
     * 获取枚举类型值
     * @param desc 描述
     * @return 返回值
     */
    public static Integer getTypeByDesc(String desc) {
        return Arrays.stream(values())
                .filter(x -> x.getValue().equals(desc))
                .map(OnlineStatusTypeEnum::getType)
                .findFirst()
                .orElse(null);
    }


    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static OnlineStatusTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
