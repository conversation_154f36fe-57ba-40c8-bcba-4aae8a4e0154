package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 设备在线离线状态枚举OnlineStatus:1 在线 0离线
 * @Date: 2023/7/20 13:34
 */
public enum FleetDeviceOnlineStatusEnum {
    /**
     * 在线
     */
    ONLINE(1, "Online"),
    /**
     * 离线
     */
    OFFLINE(0, "Offline");

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type 数字值
     * @param value 描述
     */
    FleetDeviceOnlineStatusEnum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取枚举类型值
     * @return type 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }


    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(FleetDeviceOnlineStatusEnum::getValue)
                .findFirst()
                .orElse(null);
    }


    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static FleetDeviceOnlineStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
