package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品信息对象
 * <AUTHOR>
 * @date 2023/7/3 16:18
 */
@Data
@ApiModel(description = "产品信息对象")
public class ProductInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Long productId;
    /**
     * 产品图片
     */
    @ApiModelProperty("产品图片")
    private String productIconUrl;
    /**
     * 产品名称
     */
    @ApiModelProperty("产品商品型号")
    private String commodityModel;
    /**
     * 产品名称
     */
    @ApiModelProperty("二级品类编码")
    private String categoryCode;
    /**
     * 产品名称
     */
    @ApiModelProperty("二级品类名称")
    private String categoryName;

    /**
     * 设备类型，wifiAndBle：Wifi+BLE，4GAndBle：4G+BLE，ble：BLE，DOrT：D/T，wifi：WIFI，4G：4G，lan：LAN，notNetworked：不联网
     */
    @ApiModelProperty("设备类型:wifi > 4G > BLE > DT> LAN >noNetworked")
    private String networkModes;
    /**
     * 设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备
     */
    @ApiModelProperty("设备类型，directConnectedDevice：直连设备，gatewayDevice：网关设备，gatewaySubDevice：网关子设备，notIotDevice：非Iot设备，oldIotDevice：老iot设备")
    private String productType;
    /**
     * 产品snCode
     */
    @ApiModelProperty("产品snCode")
    private String productSnCode;
    /**
     * 产品适配的app类型 1 ego 2 fleet
     */
    @ApiModelProperty("产品适配的app类型 1 ego 2 fleet")
    private List<Integer> businessType;

    //************************以下RN面板设备详情新增**********************
    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String model;
    @ApiModelProperty("品牌Id")
    private Long brandId;

    @ApiModelProperty("品牌名称")
    private String brandName;
    /**
     * 产品描述
     */
    @ApiModelProperty("产品描述")
    private String description;

    @ApiModelProperty("问卷模板：commonTemplate， extendedWarrantyTemplate，用逗号间隔")
    private String questionTemplate;

    @ApiModelProperty("图片类型：0图片上传，1图片链接")
    private Integer iconType;
}
