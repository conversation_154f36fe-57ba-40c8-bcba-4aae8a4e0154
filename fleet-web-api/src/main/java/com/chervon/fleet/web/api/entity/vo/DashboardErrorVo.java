package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName: DashboardErrorVo
 * @Description:故障信息vo
 * <AUTHOR>
 * @date 2023/7/24 14:13
 */
@Data
@Accessors(chain = true)
public class DashboardErrorVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;
    /**
     * 设备sn
     */
    @ApiModelProperty("设备sn")
    private String deviceSn;
    /**
     *  设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Long productId;
    /**
     * 产品图片
     */
    @ApiModelProperty("产品图片")
    private String productIconUrl;
    /**
     * 产品商品型号
     */
    @ApiModelProperty("产品商品型号")
    private String commodityModel;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    private String productModel;
    /**
     * 故障码
     */
    @ApiModelProperty("故障码")
    private String errorCode;
    /**
     * 故障消息标题
     */
    @ApiModelProperty("故障消息标题")
    private String faultMessageTitle;
    /**
     * 处理建议内容
     */
    @ApiModelProperty("处理建议内容")
    private String suggestionContent;
}
