package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 网关索引视图对象
 * <AUTHOR>
 * @date 2023/7/10 10:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GatewayIndexVo extends GatewayVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 是否存在网关
     */
    @ApiModelProperty("是否存在网关")
    private boolean existed;
}
