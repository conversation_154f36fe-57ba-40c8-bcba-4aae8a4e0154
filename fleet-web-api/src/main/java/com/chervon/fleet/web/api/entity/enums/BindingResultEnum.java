package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 设备绑定结果枚举：
 * 1：设备已被其他应用绑定 2：设备已被其他公司绑定
 */
public enum BindingResultEnum implements TypeEnum {
    /**
     * 1：device_bound_other_app
     */
    DEVICE_BOUND_OTHER_APP(1, "device_bound_other_app"),
    /**
     * 2 device_bound_other_company
     */
    DEVICE_BOUND_OTHER_COMPANY(2, "device_bound_other_company"),
    /**
     * 3 设备已停用，不能绑定
     */
    DEVICE_NOT_ENABLED(3,"device_not_enabled")
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务类型构造函数
     * @param type 类型id
     * @param desc 描述
     */
    BindingResultEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(BindingResultEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static BindingResultEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
