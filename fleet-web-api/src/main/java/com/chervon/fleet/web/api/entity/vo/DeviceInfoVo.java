package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备相关信息
 * <AUTHOR>
 * @date 2023/6/30 17:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "设备相关信息")
public class DeviceInfoVo extends ProductInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String deviceId;
    /**
     * 设备sn
     */
    @ApiModelProperty("设备sn")
    private String deviceSn;
    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;
    /**
     * mac地址
     */
    @ApiModelProperty("mac地址")
    private String mac;

}
