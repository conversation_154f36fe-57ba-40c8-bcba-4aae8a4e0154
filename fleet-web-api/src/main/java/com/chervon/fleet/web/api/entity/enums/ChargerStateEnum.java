package com.chervon.fleet.web.api.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 充电器状态枚举
 **/
@AllArgsConstructor
@Getter
public enum ChargerStateEnum {
    /**
     * 充电器状态枚举
     */
    CHARGING(1, "充电中"),
    /**
     * 等待充电
     */
    STANDBY(2, "等待充电"),
    OVERHEAT(3, "过温"),
    ERROR(4, "故障"),
    EMPTY(5, "空闲"),
    READY(6, "充电完成"),
    /**
     * 当前租户ID下所有设备全部故障
     * 仅t_bi_charging_state.charger_state字段会出现这种情况
     */
    ALL_ERROR(-4, "全部故障"),
    NO_DATA(-1,"代表实时数据过期")
    ;

    /**
     * 数据库值
     */
    private Integer value;
    /**
     * 描述
     */
    private String desc;
}
