package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务状态展示实体
 *
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "分组对象")
public class GroupVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分组id")
    private Long groupId;

    @ApiModelProperty("是否是默认的 0 不是 1 是")
    private Integer isDefault;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("分组名称")
    private List<TagVo> tags = new ArrayList<>();

}
