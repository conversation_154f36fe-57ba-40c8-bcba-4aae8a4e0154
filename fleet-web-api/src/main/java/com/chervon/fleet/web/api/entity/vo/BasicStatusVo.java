package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备状态响应对象
 *
 * <AUTHOR> 2024/7/30
 */
@Data
public class BasicStatusVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 设备在线状态： 0 离线 1 在线
     */
    @ApiModelProperty("设备在线状态： 0 离线 1 在线")
    private Integer isOnline;
}
