package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * fleet品类数据
 * <AUTHOR>
 * @date 2023/7/12 13:53
 */
@Data
@ApiModel(description = "fleet品类数据")
public class FleetCategoryVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 品类code
     */
    @ApiModelProperty("品类code")
    private String categoryCode;
    /**
     * 品类层级 1 一级 2 二级（平台维护的品类）
     */
    @ApiModelProperty("品类层级 1 一级 2 二级（平台维护的品类）")
    private Integer level;
    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String categoryName;
    /**
     * 二级品类列表
     */
    @ApiModelProperty("二级品类列表")
    private List<FleetCategoryVo> children;
}
