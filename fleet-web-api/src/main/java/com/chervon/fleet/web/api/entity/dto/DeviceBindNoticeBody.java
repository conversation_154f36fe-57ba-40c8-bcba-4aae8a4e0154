package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 租户设备绑定状态消息体负载实体
 * 给 android端订阅刷新设备列表重新加载 *
 * <AUTHOR> 2023/7/19
 */
@Accessors(chain = true)
@Data
public class DeviceBindNoticeBody implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("租户id")
    private Long companyId;

    @ApiModelProperty("网关id")
    private String gatewayId;

    @ApiModelProperty("租户设备绑定状态：1添加绑定 2解除绑定")
    private Integer status;
}
