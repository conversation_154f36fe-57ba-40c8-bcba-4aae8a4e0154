package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.dto.BaseProductDto;
import com.chervon.fleet.web.api.entity.vo.ProductInfoVo;

import java.util.List;

/**
 * fleet产品服务
 * <AUTHOR>
 * @date 2023/7/3 16:17
 */
public interface RemoteFleetProductService {

    /**
     * 获取fleet的产品列表
     *
     * @return fleet产品列表
     * @throws ServiceException 自定义内部服务异常
     */
    List<ProductInfoVo> listProduct() throws ServiceException;

    /**
     * 获取产品信息
     *
     * @param req 产品信息查询条件
     * @return 产品信息
     * @throws ServiceException 自定义内部服务异常
     */
    ProductInfoVo detailProduct(BaseProductDto req) throws ServiceException;

    /**
     * 获取产品信息
     * @param productId 产品id
     * @return 产品信息
     * @throws ServiceException 自定义内部服务异常
     */
    default ProductInfoVo detailByProductId(Long productId) throws ServiceException {
        BaseProductDto req = new BaseProductDto();
        req.setProductId(productId);
        return detailProduct(req);
    }

    /**
     * 获取产品信息
     * @param productSnCode sn码
     * @return 产品信息
     * @throws ServiceException 自定义内部服务异常
     */
    default ProductInfoVo detailByProductSnCode(String productSnCode) throws ServiceException {
        BaseProductDto req = new BaseProductDto();
        req.setProductSnCode(productSnCode);
        return detailProduct(req);
    }
}
