package com.chervon.fleet.web.api.entity.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 租户设备更新请求对象
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class CompanyDeviceEditDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 设备sn
     */
    private String deviceSn;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 修改人
     */
    private String modifier;
}
