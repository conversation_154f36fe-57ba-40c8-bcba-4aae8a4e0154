package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 设备数据上报
 * <AUTHOR> 2023/7/12
 */
@Data
public class DeviceShadowVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "故障数")
    private Integer faultCount;

    @ApiModelProperty(value = "故障列表")
    private List<DeviceFaultVo> listFault;

    /**
     *  设备数据最后上报时间戳
     */
    @ApiModelProperty(value = "设备数据最后上报时间戳")
    private Long reportTime;

}
