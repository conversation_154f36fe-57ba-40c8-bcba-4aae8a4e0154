package com.chervon.fleet.web.api.entity.vo;

import com.chervon.fleet.web.api.entity.consts.GlobalConsts;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * fleet app 充电器看板接口
 * @Description:图：Power Availability/Power On Charge/Total Battery
 * <AUTHOR>
 * @date 2023/7/24 14:31
 */
@Data
@Accessors(chain = true)
public class DashboardPowerAvailabilityVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 充电完成比例
     */
    @ApiModelProperty("充电完成比例")
    private BigDecimal readyRate;
    /**
     * 充电完成数量
     */
    @ApiModelProperty("充电完成数量")
    private Integer readyNumber;
    /**
     * 已充电量（Available）
     * 挂在充电器上的电量总和
     */
    @ApiModelProperty("已充电量（Available）")
    private BigDecimal availableAh;
    /**
     * 待充电量总和安时数
     */
    @ApiModelProperty("待充电量总和安时数")
    private BigDecimal chargingAh;

    /**
     * 充电完成电量（废弃不展示了）
     * 等于Available
     */
    @ApiModelProperty("充电完成电量（废弃不展示了）")
    private BigDecimal readyAh;
    /**
     * 正在充电电池包数量（不展示了）
     */
    @ApiModelProperty("正在充电电池包数量")
    private Integer chargingNumber;

    //以下Power on Charge
    /**
     * 充电器总数
     */
    @ApiModelProperty("充电器总数")
    private Integer totalChargerNumber;
    /**
     *  是否有数据上报
     */
    @ApiModelProperty("是否有数据上报")
    private Boolean haveData;
    /**
     * 充电器列表
     */
    @ApiModelProperty("充电器列表")
    private List<DashboardChargerVo> chargers;

    //以下Total Battery
    /**
     * 电池总数
     */
    @ApiModelProperty("电池总数")
    private Integer totalBatteryNumber;
    /**
     * 电池包列表数据
     * 老电池包类型名称为：Legacy Battery
     */
    @ApiModelProperty("电池列表")
    private List<DashboardBatteryVo> batteries;
    /**
     * 老电池包列表：HC2240T,BA4480T
     */
    @ApiModelProperty("老电池包列表：HC2240T,BA4480T")
    private String compatibleList = GlobalConsts.COMPATIBLE_LIST;
}
