package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * @Description: ReportCompleteStatus设备数据上报完成状态标记：1  一小时内上报完成有云朵标记   0  上报超时：超过一小时无云朵
 * <AUTHOR>
 * @Date: 2023/7/12 13:34
 */
public enum ReportCompleteStatusEnum {
    /**
     * 设备数据上报完成状态标记
     */
    REPORT_COMPLETE(1, "一小时内上报完成有云朵标记"),
    /**
     * 上报超时：超过一小时无云朵
     */
    REPORT_TIMED_OUT(0, "上报超时：超过一小时无云朵")
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    ReportCompleteStatusEnum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }


    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getValue(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(ReportCompleteStatusEnum::getValue)
                .findFirst()
                .orElse(null);
    }


    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static ReportCompleteStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
