package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 设备在线离线状态枚举OnlineStatus:1 在线 2离线
 * @Date: 2023/7/20 13:34
 */
public enum OnlineStatusEnum {
    /**
     * 在线
     */
    ONLINE(1, "connected"),
    /**
     * 离线
     */
    OFFLINE(0, "disconnected");

    /**
     * 类型
     */
    private int type;

    /**
     * 值
     */
    private String value;

    /**
     * 构造函数
     * @param type 类型值
     * @param value 描述值
     */
    OnlineStatusEnum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getValue() {
        return value;
    }


    /**
     * 根据描述获取类型
     * @param desc 描述
     * @return 返回值
     */
    public static Integer getTypeByDesc(String desc) {
        return Arrays.stream(values())
                .filter(x -> x.getValue().equals(desc))
                .map(OnlineStatusEnum::getType)
                .findFirst()
                .orElse(null);
    }


    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static OnlineStatusEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
