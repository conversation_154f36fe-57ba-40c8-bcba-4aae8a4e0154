package com.chervon.fleet.web.api.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * t_data_dictionary
 <AUTHOR>
 */
@Data
@ApiModel("配置字典表")
public class DataDictionaryProductVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 数据分组
     */
    @ApiModelProperty("数据分组")
    private String dataGroup;

    /**
     * 设置类型描述
     */
    @ApiModelProperty("设置类型描述")
    private String groupDesc;

    /**
     * 设置名称
     */
    @ApiModelProperty("设置名称")
    private String dataName;

    /**
     * 设置值
     */
    @ApiModelProperty("设置值")
    private String dataValue;

    /**
     * 排序编号
     */
    @ApiModelProperty("排序编号")
    private Integer sortId;
    /**
     * productCategory列表
     */
    @ApiModelProperty("productCategory列表")
    private List<DataDictionaryProductVo> productCategoryVos;

}