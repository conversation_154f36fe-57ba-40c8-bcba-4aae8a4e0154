package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 设备库存状态：0-Never Seen,1-In Warehouse，2-Out for Work，3-Unknown location
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WarehouseStatusEnum implements TypeEnum {
    /**
     * 绑定后初始值 0
     */
    NEVER_SEEN(0, "Never Seen"),
    /**
     * 固定网关扫描到并在线状态：在库
     */
    IN_WAREHOUSE(1, "In Warehouse"),
    /**
     * 移动网关扫描到并子设备在线状态： 2-外出工作
     */
    OUT_FOR_WORK(2, "Out for Work"),
    /**
     * 超过12小时未被网关扫描到：3-未知位置
     */
    UNKNOWN_CURRENT_LOCATION(3, "Unknown Current Location"),
    /**
     * 已删除
     */
    DELETED(4, "Deleted"),
    ;


    int type;
    String desc;

    /**
     * 根据类型获取描述
     * @param type 类型
     * @return 描述
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
            .filter(x -> x.getType() == type)
            .map(WarehouseStatusEnum::getDesc)
            .findFirst()
            .orElse(null);
    }

    /**
     * 根据枚举id获取枚举
     * @param type 类型
     * @return 返回枚举类型
     */
    public static WarehouseStatusEnum getEnum(int type) {
        return Arrays.stream(values())
            .filter(x -> x.getType() == type)
            .findFirst()
            .orElse(null);
    }

    /**
     * 根据描述获取枚举值
     * @param desc 描述
     * @return 返回值
     */
    public static Integer getTypeByDesc(String desc) {
        return Arrays.stream(values())
            .filter(x -> x.getDesc().equals(desc))
            .map(WarehouseStatusEnum::getType)
            .findFirst()
            .orElse(null);
    }

}
