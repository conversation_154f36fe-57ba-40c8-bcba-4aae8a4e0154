package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

import java.util.Arrays;

/**
 * 设备业务分类类型枚举
 */
public enum BizCategoryTypeEnum implements TypeEnum {
    /**
     * 1：tools
     */
    TOOL(1, "tools"),
    /**
     * 2 charger
     */
    CHARGER(2, "charger"),
    /**
     * 3 BATTERY
     */
    BATTERY(3, "battery"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务类型构造函数
     * @param type 类型id
     * @param desc 描述
     */
    BizCategoryTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(BizCategoryTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static BizCategoryTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
