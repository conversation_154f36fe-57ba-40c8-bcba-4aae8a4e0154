package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.dto.DataReportCompleteDto;
import com.chervon.fleet.web.api.entity.dto.GatewayScanReportingDto;
import com.chervon.fleet.web.api.entity.dto.IotUpdateOnlineStatusDto;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusTypeEnum;

/**
 * 远程规则引擎上报服务
 */
public interface RemoteRuleEngineService {

    /**
     * 接收规则引擎转发的网关扫描到的设备列表的状态
     *
     * @param req       入参
     * @param type      1硬件网关 2软网关
     * @param gatewayId 网关id
     * @throws ServiceException 自定义内部服务异常
     */
    void refreshWarehouseStatus(GatewayScanReportingDto req, Integer type, String gatewayId) throws ServiceException;

    /**
     * 接收规则引擎转发的设备数据上报完成状态
     *
     * @param req       入参
     * @param gatewayId 网关id
     * @param deviceId  设备id
     * @throws ServiceException 自定义内部服务异常
     */
    void dataReportComplete(DataReportCompleteDto req, String gatewayId, String deviceId) throws ServiceException;

    /**
     * 接收规则引擎转发的设备连接状态更新
     *
     * @param type 类型
     * @param req  入参
     * @throws ServiceException 自定义内部服务异常
     */
    void onlineStatusReport(OnlineStatusTypeEnum type, IotUpdateOnlineStatusDto req) throws ServiceException;

    /**
     * 接收规则引擎转发的设备上报日志
     *
     * @param deviceId 设备id
     * @param reported 报文
     * @throws ServiceException 自定义内部服务异常
     */
    void saveFaultData(String deviceId, Object reported) throws ServiceException;
}
