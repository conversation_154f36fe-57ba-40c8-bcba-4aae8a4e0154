package com.chervon.fleet.web.api.entity.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 库存查询对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "库存查询对象")
public class InventoryQuery implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "租户ID", required = true)
    private Long companyId;
}
