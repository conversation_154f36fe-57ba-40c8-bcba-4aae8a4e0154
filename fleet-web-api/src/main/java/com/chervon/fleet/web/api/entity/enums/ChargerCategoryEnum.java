package com.chervon.fleet.web.api.entity.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Description: 设备一级分类枚举
 * <AUTHOR>
 * @Date: 2023/7/20 13:34
 */
@Getter
public enum ChargerCategoryEnum {
    /**
     * PGX Power Bank
     */
    PGX_POWER_BANK("PGXPowerBank", "PGX Power Bank"),
    /**
     * PGX Hub
     */
    PGX_HUB("PGXHub", "PGX Hub"),
    /**
     * Turbo Charger
     */
    TURBO_CHARGER("TurboCharger", "Turbo Charger"),
    /**
     * Adaptor
     */
    ADAPTOR("Adaptor", "Adaptor"),
    /**
     * Unknown
     */
    UNKNOWN_DEVICE("Unknown", "Unknown"),
    ;

    /**
     * 类型
     */
    private final String code;

    /**
     * 值
     */
    private final String value;

    ChargerCategoryEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 获取类型编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return value;
    }


    /**
     * 获取枚举类型值
     * @param code 编码
     * @return 返回值
     */
    public static ChargerCategoryEnum getEnum(String code) {
        return Arrays.stream(values())
                .filter(x -> x.getCode().equals(code))
                .findFirst()
                .orElse(UNKNOWN_DEVICE);
    }
}
