package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;
import java.util.Arrays;

/**
 * APP业务类型 1：ego 2 fleet *
 */
public enum BusinessTypeEnum implements TypeEnum {
    /**
     * 1：ego
     */
    EGO_CONNECT(1, "ego"),
    /**
     * 2 fleet
     */
    FLEET(2, "fleet"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 业务类型构造函数
     * @param type 类型id
     * @param desc 描述
     */
    BusinessTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public int getType() {
        return type;
    }

    /**
     * 获取枚举类型值
     * @return 返回值
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static String getDesc(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .map(BusinessTypeEnum::getDesc)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取枚举类型值
     * @param type 类型值
     * @return 返回值
     */
    public static BusinessTypeEnum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(null);
    }
}
