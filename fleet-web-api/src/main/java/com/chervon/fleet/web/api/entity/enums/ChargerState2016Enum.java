package com.chervon.fleet.web.api.entity.enums;

import java.util.Arrays;

/**
 *  2016原始充电器的物模型充电状态枚举： 2016 *
 * 0：保留
 * 1：充电中
 * 2：充电完成（或接插设备无需充电）
 * 3：等待计划充电
 * 4：充电器过温
 * 5：充电器故障
 * 6：充电器高温慢充
 * <AUTHOR> 2023/9/5
 */
public enum ChargerState2016Enum {
    /**
     * 未知状态
     */
    Unknown(0,"未知状态"),
    /**
     * 充电中
     */
    Charging(1, "充电中"),
    /**
     * 充电完成
     */
    Complete(2,"充电完成"),
    /**
     * 等待充电
     */
    Standby(3, "等待充电"),
    /**
     * 电池过温
     */
    OverHot(4, "电池过温"),
    /**
     * 充电故障
     */
    Fault(5, "充电故障"),
    /**
     * 充电器高温慢充
     */
    OverHotLow(6, "充电器高温慢充"),
    /**
     * 相当于充电完成
     */
    Balanced(7,"均衡模式（美国）")
            ;

    /**
     * 类型
     */
    private final int type;

    /**
     * 值
     */
    private final String value;

    ChargerState2016Enum(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取类型
     */
    public int getType() {
        return type;
    }

    /**
     * 获取值
     */
    public String getValue() {
        return value;
    }


    /**
     * 获取枚举
     */
    public static ChargerState2016Enum getEnum(int type) {
        return Arrays.stream(values())
                .filter(x -> x.getType() == type)
                .findFirst()
                .orElse(Unknown);
    }
}
