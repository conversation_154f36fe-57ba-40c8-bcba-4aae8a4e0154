package com.chervon.fleet.web.api.entity.query.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 充电系统详情看板查询类
 * <AUTHOR>
 * @since 2023-07-31 15:05
 **/
@Data
@ApiModel("充电系统详情看板查询类")
public class ChargingSystemDetailDashboardQuery implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long companyId;
    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", required = true)
    private String deviceId;
}
