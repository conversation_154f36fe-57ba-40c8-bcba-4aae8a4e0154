package com.chervon.fleet.web.api.service;

import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.web.api.entity.dto.GatewayDeviceListDto;
import com.chervon.fleet.web.api.entity.dto.GatewayDto;
import com.chervon.fleet.web.api.entity.vo.*;

import java.util.List;

/**
 * 网关服务接口
 * <AUTHOR>
 * @date 2023/7/3 11:33
 */
public interface RemoteFleetGatewayService {

    /**
     * 创建网关
     *
     * @param req 创建参数
     * @throws ServiceException 自定义内部服务异常
     */
    void create(GatewayDto req) throws ServiceException;

    /**
     * 编辑网关
     *
     * @param req 编辑参数
     * @throws ServiceException 自定义内部服务异常
     */
    void edit(GatewayDto req) throws ServiceException;

    /**
     * 移除网关--传入gatewayId
     *
     * @param gatewayId 网关id
     * @throws ServiceException 自定义内部服务异常
     */
    void remove(String gatewayId) throws ServiceException;

    /**
     * 获取网关详情信息--传入gatewayId
     *
     * @param gatewayId 网关id
     * @return 网关详情
     * @throws ServiceException 自定义内部服务异常
     */
    GatewayVo getGatewayDetail(String gatewayId) throws ServiceException;

    /**
     * 判断当前用户是否有网关，如果有返回gateway信息
     *
     * @param uniqueId 手机唯一码
     * @return index信息
     * @throws ServiceException 自定义内部服务异常
     */
    GatewayIndexVo gatewayIndex(String uniqueId) throws ServiceException;

    /**
     * 批量查询设备信息
     *
     * @param req 查询条件
     * @return 设备信息
     * @throws ServiceException 自定义内部服务异常
     */
    List<GatewayDeviceInfoVo> listDevice(GatewayDeviceListDto req) throws ServiceException;

    /**
     * 获取固定网关坐标列表信息
     * @return
     * @throws ServiceException
     */
    FixGatewayCoordinateVo fixGatewayCoordinate() throws ServiceException;
}
