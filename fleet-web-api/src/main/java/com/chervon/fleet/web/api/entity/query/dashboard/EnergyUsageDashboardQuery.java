package com.chervon.fleet.web.api.entity.query.dashboard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 充电电量统计看板查询类
 * <AUTHOR>
 * @since 2023-07-31 17:42
 **/
@Data
@ApiModel("充电电量统计看板查询类")
public class EnergyUsageDashboardQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "租户ID", hidden = true)
    private Long companyId;
    @ApiModelProperty(value = "起始时间,yyyy-MM-dd字符串", required = true)
    private String startTime;
    @ApiModelProperty(value = "结束时间,yyyy-MM-dd字符串", required = true)
    private String endTime;
}
