package com.chervon.fleet.web.api.entity.dto;

import com.chervon.common.core.domain.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 租户设备分页查询对象
 * <AUTHOR>
 * @date 2023/8/2 16:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FleetCompanyDevicePageDto extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1;
    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String companyId;
    /**
     * 租户名称
     */
    @ApiModelProperty("租户名称")
    private String companyName;
    /**
     * 已绑定设备id
     */
    @ApiModelProperty("已绑定设备id")
    private String deviceId;
    /**
     * 已绑定设备sn
     */
    @ApiModelProperty("已绑定设备sn")
    private String deviceSn;
    /**
     * 已绑定设备fleet二级品类code
     */
    @ApiModelProperty("已绑定设备fleet二级品类code")
    private String categoryCode;
    /**
     * 已绑定设备品牌id
     */
    @ApiModelProperty("已绑定设备品牌id")
    private Long brandId;
    /**
     * 已绑定设备model#
     */
    @ApiModelProperty("已绑定设备model#")
    private String model;

}
