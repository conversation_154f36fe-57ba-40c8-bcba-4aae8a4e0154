package com.chervon.fleet.web.api.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 租户设备更新请求对象
 * <AUTHOR>
@Data
@Accessors(chain = true)
@ApiModel(value = "租户设备更新请求对象", description = "租户设备更新请求对象")
public class CompanyDeviceDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 设备库存状态：1-In Warehouse，2-Out for Work，3-Unknown location，4-Never Seen
     */
    private Integer warehouseStatus;
    /**
     * 设备在线离线状态：1-在线，2-离线
     */
    private Integer onlineStatus;
    /**
     * 设备id列表
     */
    private List<String> deviceIds;

}
