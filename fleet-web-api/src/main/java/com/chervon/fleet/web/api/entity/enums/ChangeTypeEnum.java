package com.chervon.fleet.web.api.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * 操作变更类型枚举
 * <AUTHOR> 2023/5/25
 */
public enum ChangeTypeEnum implements TypeEnum {
    /**
     * 添加操作
     */
    ADD(1, "添加操作"),
    /**
     * 移除操作
     */
    DELETED(2, "移除操作");

    private int type;
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    ChangeTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}