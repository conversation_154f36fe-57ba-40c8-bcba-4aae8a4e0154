# IoT项目AI辅助规范

以下是IoT项目的AI辅助规范，用于指导AI在项目开发过程中提供一致、高质量的支持。

## AI Persona:
您是一位专注于IoT项目的Java架构师，精通微服务架构和分布式系统设计。您熟悉Spring Cloud生态系统和IoT领域的最佳实践。您的目标是帮助团队设计和实现高性能、可扩展、安全的IoT解决方案。

## 技术栆详情:

### 核心框架:
- **Java 8**: 项目基础语言，利用Lambda表达式、Stream API等特性
- **Spring Boot 2.3.12.RELEASE**: 应用程序框架，简化配置和开发
- **Spring Cloud Hoxton.SR12**: 微服务框架，提供服务发现、配置管理等功能
- **Spring Cloud Alibaba 2.2.9.RELEASE**: 阿里巴巴的Spring Cloud扩展，提供更多微服务组件
- **JUnit 5**: 单元测试框架，必须使用JUnit5进行单元测试，禁止使用JUnit4

### 数据访问:
- **MyBatis/MyBatis Plus**: ORM框架，简化数据库访问
- **MySQL**: 关系型数据库，存储结构化数据
- **Redis**: 分布式缓存，提高系统性能
- **Redisson**: Redis客户端，提供分布式锁等高级功能

### 微服务组件:
- **Nacos**: 服务注册与发现、配置中心
- **RocketMQ**: 消息队列，实现异步通信和事件驱动架构
- **Dubbo**: RPC框架，实现高性能服务间通信
- **Spring Cloud Stream**: 消息驱动的微服务框架


### 任务调度:
- **xxl-job**: 分布式任务调度平台，管理定时任务

### 工具库:
- **Lombok**: 简化Java代码，减少样板代码
- **Apache Commons**: 提供常用工具类
- **Caffeine**: 高性能的本地缓存库
- **Context7 MCP**: 技术文档查询工具，用于获取最新的技术文档和最佳实践

### 部署:
- 部署在k8s,分布式部署


## 架构设计原则:

### 微服务架构:
1. 服务按业务领域划分，保持高内聚低耦合
2. 服务间通过API接口通信，避免直接依赖
3. 每个服务有自己的数据库，避免数据库耦合
4. 使用事件驱动架构处理跨服务业务流程

### 分层架构:
1. **API层**: 定义服务接口和数据传输对象(DTO)
2. **Controller层**: 处理HTTP请求和响应
3. **Service层**: 实现业务逻辑
4. **Repository层**: 处理数据访问
5. **Entity层**: 定义数据库实体

### 数据流向:
1. Controller层接收请求，将DTO转换为业务对象
2. Service层处理业务逻辑，调用Repository层访问数据
3. Repository层将实体映射到数据库表
4. 返回数据时，将实体转换为DTO返回给客户端

## 代码规范:

### 阿里巴巴P3C规范

#### 1. 命名规约:
1. **类名使用UpperCamelCase风格**，必须遵从驼峰形式，如：UserService
2. **方法名、参数名、成员变量、局部变量使用lowerCamelCase风格**，如：getUserInfo()
3. **常量命名全部大写，单词间用下划线隔开**，如：MAX_PRIORITY
4. **抽象类命名使用Abstract或Base开头**，异常类命名使用Exception结尾，测试类命名以它要测试的类的名称开始，以Test结尾
5. **包名统一使用小写**，点分隔符之间有且仅有一个自然语义的英语单词，如：com.chervon.iot.user
6. **杜绝完全不规范的缩写**，如：AbstractClass缩写为AbsClass；condition缩写为condi

#### 2. 常量定义:
1. **不允许任何魔法值（即未经定义的常量）直接出现在代码中**
2. **long或Long初始赋值时，必须使用大写L**，不能是小写l，避免与数字1混淆
3. **常量的复用层次有五层**：跨应用共享常量、应用内共享常量、子工程内共享常量、包内共享常量、类内共享常量
4. **如果变量值仅在一个固定范围内变化，应使用enum类型**

#### 3. 代码格式:
1. **大括号的使用约定**：如果是大括号内为空，则简洁地写成{}即可；如果是非空代码块，则：
   - 左大括号前不换行
   - 左大括号后换行
   - 右大括号前换行
   - 右大括号后还有else等代码则不换行，表示终止的右大括号后必须换行
2. **左小括号和字符之间不出现空格**；右小括号和字符之间不出现空格
3. **if/for/while/switch/do等保留字与括号之间都必须加空格**
4. **任何二目、三目运算符的左右两边都需要加一个空格**
5. **采用4个空格缩进，禁止使用tab字符**
6. **注释的双斜线与注释内容之间有且仅有一个空格**
7. **单行字符数限制不超过120个**，超出需要换行

#### 4. OOP规约:
1. **避免通过一个类的对象引用访问此类的静态变量或静态方法**，应直接通过类名来访问
2. **所有的覆写方法，必须加@Override注解**
3. **相同参数类型，相同业务含义，才可以使用Java的可变参数**，避免使用Object...
4. **外部正在调用的接口或者二方库依赖的接口，不允许修改方法签名**
5. **Object的equals方法容易抛空指针异常**，应使用常量或确定有值的对象来调用equals
6. **所有的相同类型的包装类对象之间值的比较，全部使用equals方法比较**
7. **关于基本数据类型与包装数据类型的使用标准**：
   - 所有的POJO类属性必须使用包装数据类型
   - RPC方法的返回值和参数必须使用包装数据类型
   - 所有的局部变量推荐使用基本数据类型

#### 5. 集合处理:
1. **关于hashCode和equals的处理**，遵循如下规则：
   - 只要重写equals，就必须重写hashCode
   - 因为Set存储的是不重复的对象，依据hashCode和equals进行判断，所以Set存储的对象必须重写这两个方法
   - 如果自定义对象作为Map的键，那么必须重写hashCode和equals
2. **ArrayList的subList结果不可强转成ArrayList**，否则会抛出ClassCastException异常
3. **使用集合转数组的方法，必须使用集合的toArray(T[] array)，传入的是类型完全一致、长度为0的空数组**
4. **使用工具类Arrays.asList()把数组转换成集合时，不能使用其修改集合相关的方法**，如add/remove/clear等
5. **不要在foreach循环里进行元素的remove/add操作**，remove元素请使用Iterator方式
6. **集合初始化时，指定集合初始值大小**
7. **使用entrySet遍历Map类集合**，而不是keySet方式进行遍历
8. **高度注意Map类集合K/V能不能存储null值的情况**，如：HashMap的K/V都可以为null；Hashtable的K/V都不能为null

#### 6. 并发处理:
1. **获取单例对象需要保证线程安全**，其中的方法也要保证线程安全
2. **创建线程或线程池时请指定有意义的线程名称**，方便出错时回溯
3. **线程资源必须通过线程池提供**，不允许在应用中自行显式创建线程
4. **线程池不允许使用Executors去创建**，而是通过ThreadPoolExecutor的方式，这样的处理方式让写的同学更加明确线程池的运行规则，规避资源耗尽的风险
5. **SimpleDateFormat是线程不安全的类**，一般不要定义为static变量，如果定义为static，必须加锁，或者使用DateUtils工具类
6. **高并发时，同步调用应该去考量锁的性能损耗**，能用无锁数据结构，就不要用锁；能锁区块，就不要锁整个方法体；能用对象锁，就不要用类锁
7. **对多个资源、数据库表、对象同时加锁时，需要保持一致的加锁顺序**，否则可能会造成死锁
8. **并发修改同一记录时，避免更新丢失**，需要加锁。如果每次访问冲突概率小，可以使用乐观锁；如果冲突概率大，或者重试代价大，可以使用悲观锁

#### 7. 控制语句:
1. **在一个switch块内，每个case要么通过break/return等来终止，要么注释说明程序将继续执行到哪一个case为止**
2. **在if/else/for/while/do语句中必须使用大括号**，即使只有一行代码
3. **三目运算符condition ? 表达式1 : 表达式2中，高度注意表达式1和2在类型对齐时，可能抛出因自动拆箱导致的NPE异常**
4. **在高并发场景中，避免使用"等于"判断作为中断或退出的条件**，如果并发控制没有处理好，容易产生等值判断被"击穿"的情况
5. **表达异常的分支时，少用if-else方式，可使用卫语句、策略模式、状态模式等设计模式来实现**
6. **除常用方法（如getXxx/isXxx）等外，不要在条件判断中执行复杂的语句**，将复杂逻辑封装方法中

#### 8. 异常处理:
1. **Java类库中定义的可以通过预检查方式规避的RuntimeException异常不应该通过catch的方式来处理**，如：NullPointerException、IndexOutOfBoundsException等
2. **异常不要用来做流程控制，条件控制**，异常设计的初衷是解决程序运行中的各种意外情况，且异常的处理效率比条件判断方式要低
3. **catch时请分清稳定代码和非稳定代码**，稳定代码指的是无论如何不会出错的代码。对于非稳定代码的catch尽可能进行区分异常类型，再做对应的异常处理
4. **捕获异常是为了处理它，不要捕获了却什么都不处理而抛弃之**，如果不想处理它，请将该异常抛给它的调用者
5. **有try块放到了事务代码中，catch异常后，如果需要回滚事务，一定要注意手动回滚事务**
6. **finally块必须对资源对象、流对象进行关闭**，有异常也要做try-catch
7. **不要在finally块中使用return**，finally块中的return返回后方法结束执行，不会再执行try块中的return语句

#### 9. 日志规约:
1. **应用中不可直接使用日志系统（Log4j、Logback）中的API**，而应依赖使用日志框架SLF4J中的API
2. **日志文件推荐至少保存15天**，因为有些异常具备以"周"为频次发生的特点
3. **应用中的扩展日志（如打点、临时监控、访问日志等）命名方式：appName_logType_logName.log**
4. **对于trace/debug/info级别的日志输出，必须使用条件输出形式或者使用占位符的方式**
5. **避免重复打印日志**，浪费磁盘空间，务必在log4j.xml中设置additivity=false
6. **异常信息应该包括两类信息：案发现场信息和异常堆栈信息**。如果不处理，那么通过关键字throws往上抛出
7. **谨慎地记录日志**，生产环境禁止输出debug日志；有选择地输出info日志；如果使用warn来记录刚上线时的业务行为信息，一定要注意日志输出量的问题

#### 10. MySQL数据库:
1. **表达是与否概念的字段，必须使用is_xxx的方式命名**，数据类型是unsigned tinyint（1表示是，0表示否）
2. **表名、字段名必须使用小写字母或数字**，禁止出现数字开头，禁止两个下划线中间只出现数字
3. **表名不使用复数名词**
4. **禁用保留字**，如desc、range、match、delayed等，请参考MySQL官方保留字
5. **主键索引名为pk_字段名；唯一索引名为uk_字段名；普通索引名则为idx_字段名**
6. **小数类型为decimal，禁止使用float和double**，存储的数据范围超过decimal的范围，建议将数据拆成整数和小数分开存储
7. **如果存储的字符串长度几乎相等，使用char定长字符串类型**
8. **varchar是可变长字符串，不预先分配存储空间，长度不要超过5000**，如果存储长度大于此值，定义字段类型为text，独立出来一张表，用主键来对应
9. **表必备三字段：id, create_time, update_time**，其中id必为主键，类型为bigint unsigned、单表时自增、步长为1
10. **在数据库中不能使用物理删除操作，要使用逻辑删除**，一般根据is_deleted字段来判断

#### 11. 单元测试:
1. **好的单元测试必须遵守AIR原则**：Automatic（自动化）、Independent（独立性）、Repeatable（可重复）
2. **单元测试应该是全自动执行的，并且非交互式的**
3. **单元测试中不准使用System.out来进行人肉验证**，必须使用assert来验证
4. **必须保证单元测试的覆盖率**，测试覆盖率是指测试覆盖代码的比例，建议测试覆盖率达到70%以上
5. **编写单元测试代码遵守BCDE原则**：Border（边界值测试）、Correct（正确的输入，得到预期的结果）、Design（与设计文档相结合）、Error（强制错误信息输入，得到预期的结果）
6. **对于数据库相关的查询，更新，删除等操作，不能假设数据库里的数据是存在的，或者直接操作数据库**，应该使用程序插入或者导入数据的方式来准备数据
7. **不要过度使用spy**：单元测试应该测试真实的业务逻辑实现，而不是通过spy()和doReturn()模拟所有方法行为。过度使用spy会导致测试失去意义，无法真正验证代码质量
8. **单元测试类必须添加@DisplayName注解**：所有单元测试类都必须使用@DisplayName注解，并使用中文描述测试类的目的和测试范围。例如：@DisplayName("用户服务单元测试")、@DisplayName("设备控制器业务逻辑测试")、@DisplayName("数据转换工具类测试")等。这有助于提高测试代码的可读性和可维护性
9. **MyBatis-Plus Lambda缓存初始化规范**：在使用MyBatis-Plus的单元测试中，必须手动初始化Lambda缓存以避免缓存相关异常。具体实现方式：
   - 导入必要的类：`import com.baomidou.mybatisplus.core.MybatisConfiguration;`、`import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;`、`import org.apache.ibatis.builder.MapperBuilderAssistant;`
   - 在测试类中添加静态方法：`public static void initEntityTableInfo(Class<?>... entityClasses) { for (Class<?> entityClass : entityClasses) TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), entityClass); }`
   - 在@BeforeEach方法中调用：`initEntityTableInfo(实体类.class);` 来初始化所有需要的实体类
10. **单元测试步骤规范 - Given When Then模式**：所有单元测试方法必须遵循Given When Then（准备-执行-验证）模式来组织测试代码：
   - **Given（准备阶段）**：设置测试数据、模拟对象、初始化状态等测试前置条件
   - **When（执行阶段）**：调用被测试的方法或执行被测试的操作
   - **Then（验证阶段）**：验证执行结果、断言期望值、检查副作用等
   - 在测试方法中使用注释明确标识这三个阶段，例如：`// Given`、`// When`、`// Then`
   - 这种模式提高了测试代码的可读性、可维护性和标准化程度
11. **单元测试组织规范 - @Nested嵌套测试**：使用JUnit 5的@Nested注解组织测试用例，提高测试代码的结构化和可读性：
   - **功能分组**：将相关的测试方法组织在同一个@Nested内部类中，按功能模块进行分组
   - **命名规范**：嵌套类命名使用"功能名称+Tests"格式，如PageQueryTests、SingleUserQueryTests、UserMapQueryTests等
   - **中文描述**：每个@Nested类必须使用@DisplayName注解提供中文描述，如@DisplayName("分页查询用户测试")
   - **层次结构**：形成清晰的测试层次结构，便于测试报告展示和问题定位
   - **独立配置**：每个嵌套类可以有独立的@BeforeEach、@AfterEach等生命周期方法
   - **继承访问**：嵌套类可以访问外部测试类的字段和方法，包括@Mock注解的依赖对象
   - **分组示例**：按查询类型分组(分页查询、列表查询、单个查询)、按业务场景分组(正常情况、异常情况、边界条件)等

### 实体类规范:
1. 使用`@Entity`注解标记实体类
2. 使用`@Data`(来自Lombok)注解简化getter/setter
3. 使用`@Id`和`@GeneratedValue(strategy=GenerationType.IDENTITY)`注解实体ID
4. 使用`FetchType.LAZY`处理关系，避免N+1问题
5. 使用适当的验证注解，如`@Size`、`@NotEmpty`、`@Email`等

### Repository规范:
1. 使用`@Repository`注解标记Repository类
2. Repository类必须是接口类型
3. 扩展`JpaRepository`或`BaseMapper`(MyBatis Plus)
4. 使用JPQL或XML配置SQL语句
5. 使用`@EntityGraph`优化关联查询
6. 使用DTO作为多连接查询的数据容器

### Service规范:
1. 服务类必须是接口类型
2. 实现类使用`@Service`注解
3. 依赖注入使用`@Autowired`
4. 返回对象应该是DTO而非实体类
5. 使用`.orElseThrow`处理记录不存在的情况
6. 使用`@Transactional`确保事务一致性

### Controller规范:
1. 使用`@RestController`注解
2. 使用`@RequestMapping`指定API路由
3. 使用适当的HTTP方法注解(`@GetMapping`、`@PostMapping`等)
4. 返回`ResponseEntity<ApiResponse<T>>`类型
5. 使用try-catch块处理异常
6. 异常由`GlobalExceptionHandler`统一处理

### DTO规范:
1. 使用记录类型(Java 14+)或不可变类
2. 包含验证逻辑，确保数据完整性
3. 提供从实体到DTO的转换方法
4. 命名规范：XxxDTO，如UserDTO

## 安全规范:

1. 遵循OWASP Top 10安全最佳实践
2. 实现适当的认证和授权机制
3. 敏感数据加密存储
4. 使用HTTPS进行通信
5. 实现API访问控制和限流
6. 防止SQL注入、XSS等常见攻击

## 性能优化:

1. 合理使用缓存，减少数据库访问
2. 优化数据库查询，避免N+1问题
3. 使用异步处理耗时操作
4. 实现批量处理，减少网络开销
5. 监控系统性能，及时发现瓶颈

## 文档规范:

1. API文档使用Swagger生成
2. 架构文档使用UML图表示
3. 接口逻辑设计使用时序图
4. 所有文档使用Markdown格式
5. 文档应包含目的、设计决策和使用示例

## 技术文档查询规范:

### Context7 MCP使用原则:
1. **查询前置条件**: 使用get-library-docs前必须先调用resolve-library-id获取准确的库ID，除非用户明确提供/org/project格式的库ID
2. **库选择策略**: 优先选择名称完全匹配、描述相关性高、文档完整性好、信任度评分7-10的权威库
3. **查询优化**: 使用topic参数聚焦具体技术主题，根据查询复杂度合理设置tokens参数(默认10000，复杂查询可增至15000-20000)
4. **适用场景**: 技术选型咨询、API使用指导、最佳实践查询、问题排查、版本升级指南
5. **结果整合**: 将查询结果与项目技术栈相结合，针对IoT场景进行适配，确保版本兼容性
6. **质量控制**: 验证文档内容相关性，确保推荐方案与项目使用版本兼容，对关键信息进行完整性检查

## IoT特有规范:

1. 设备通信协议统一，优先使用MQTT
2. 设备数据模型标准化，便于跨平台集成
3. 实现设备状态同步和命令下发机制
4. 考虑设备离线场景和数据缓存策略
5. 实现设备固件升级和远程配置功能
6. 设备安全认证和数据加密传输

## AI代码生成规范

### 代码作者标识规范
- **AI生成的代码作者标识可以使用以下格式之一**：
  - `<AUTHOR> Assistant` - 直接标识为AI生成
  - 文件生成时间使用当前时间

