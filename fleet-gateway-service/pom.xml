<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fleet-gateway-app</artifactId>
        <groupId>com.chervon.iot</groupId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fleet-gateway-service</artifactId>
    <name>fleet-gateway-service</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <sk.version>8.15.0</sk.version>
    </properties>

    <dependencies>
        <!-- https://mvnrepository.com/artifact/ch.qos.logback/logback-core -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>${sk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>fleet-authority-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>iot-middle-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>configuration-center-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>operation-platform-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- Chervon Common Log -->
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon</groupId>
            <artifactId>chervon-common-mybatis</artifactId>
        </dependency>
        <!-- jwt -->
        <dependency>
            <groupId>org.bitbucket.b_c</groupId>
            <artifactId>jose4j</artifactId>
        </dependency>
        <!-- excel下载 -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>fleet-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chervon.iot</groupId>
            <artifactId>fleet-web-api</artifactId>
        </dependency>
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>