package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.domain.R;
import com.chervon.fleet.web.api.entity.enums.BusinessTypeEnum;
import com.chervon.iot.middle.api.service.RemoteAppService;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertP12Vo;
import com.chervon.iot.middle.api.vo.device.IotDeviceCertVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;

/**
 * <AUTHOR>
 * @date 2024-06-26
 */
@Api(tags = "iotCore 交互相关接口")
@RestController
@RequestMapping("/iotCore")
public class IotCoreController {
    @DubboReference
    private RemoteAppService remoteAppService;

    /**
     * 获取IoT core证书，返回pem格式字符串
     * @return
     */
    @ApiOperation("取IoT core证书，返回pem格式字符串")
    @PostMapping("/cert/pem")
    public R<IotDeviceCertVo> getIotCertPem() {
        IotDeviceCertVo iotCertPem = remoteAppService.getIotCertPemFromS3(BusinessTypeEnum.FLEET.getType());
        return R.ok(iotCertPem);
    }

    /**
     * 获取IoT core证书，返回pem格式字符串
     * @return
     */
    @ApiOperation("获取IoT core证书，返回p12格式字符串")
    @PostMapping("/cert/p12")
    public R<IotDeviceCertP12Vo> getIotCertP12()
            throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException {
        IotDeviceCertP12Vo iotCertP12 = remoteAppService.getIotCertP12(BusinessTypeEnum.FLEET.getType());
        return R.ok(iotCertP12);
    }

}
