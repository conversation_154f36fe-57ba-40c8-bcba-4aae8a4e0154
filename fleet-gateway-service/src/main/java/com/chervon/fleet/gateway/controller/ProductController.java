package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.dto.BaseProductDto;
import com.chervon.fleet.web.api.entity.vo.ProductInfoVo;
import com.chervon.fleet.web.api.service.RemoteFleetProductService;
import com.chervon.operation.api.RemoteIntroductionService;
import com.chervon.operation.api.RemoteOperationCacheService;
import com.chervon.operation.api.RemotePostSaleService;
import com.chervon.operation.api.dto.IntroductionTypeDto;
import com.chervon.operation.api.vo.IntroductionVo;
import com.chervon.operation.api.vo.ProductWikiVo;
import com.chervon.operation.api.vo.cache.ProductCache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 16:04
 */
@Slf4j
@Api(tags = "产品")
@RestController
@RequestMapping("/product")
public class ProductController {

    @DubboReference
    private RemoteFleetProductService remoteFleetProductService;

    @DubboReference
    private RemoteIntroductionService remoteIntroductionService;
    @DubboReference
    private RemotePostSaleService remotePostSaleService;
    @DubboReference
    private RemoteOperationCacheService remoteOperationCacheService;

    /**********************产品接口************************/

    @ApiOperation("分页获取产品信息")
    @GetMapping("/list")
    public List<ProductInfoVo> list() {
        return remoteFleetProductService.listProduct();
    }

    @ApiOperation("根据产品信息获取产品详情")
    @PostMapping("/detail")
    public ProductInfoVo detail(@RequestBody BaseProductDto req) {
        return remoteFleetProductService.detailProduct(req);
    }

    /**********************引导页接口************************/

    @ApiOperation("根据类型获取引导页列表")
    @PostMapping("/introduction/list")
    public List<IntroductionVo> introductionList(@RequestBody IntroductionTypeDto req) {
        return remoteIntroductionService.listIntroductionByType(req);
    }

    @ApiOperation("产品百科(售后)")
    @PostMapping("/productWiki")
    public ProductWikiVo productWiki(@RequestBody BaseProductDto req) {
        Assert.notNull(req.getProductId(), ErrorCode.PARAMETER_NOT_PROVIDED,"productId");
        String language = UserContext.getLanguage();
        ProductWikiVo res = remotePostSaleService.productWiki(req.getProductId(), language);
        ProductCache productCache = remoteOperationCacheService.getProduct(req.getProductId());
        res.getProduct().setProductName(productCache.getDefaultName());
        res.getProduct().setProductPicUrl(productCache.getUrl());
        res.getProduct().setCommodityModel(productCache.getCommodityModel());
        return res;
    }

}
