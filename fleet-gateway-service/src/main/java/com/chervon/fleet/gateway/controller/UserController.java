package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.fleet.gateway.entity.consts.StringConst;
import com.chervon.fleet.user.api.entity.dto.ForgetPasswordRequestDto;
import com.chervon.fleet.user.api.entity.dto.ModifyPasswordDto;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.api.service.RemoteAuthService;
import com.chervon.fleet.user.api.service.RemoteSendMailService;
import com.chervon.fleet.user.api.service.RemoteUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * App用户相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "用户")
@AllArgsConstructor
@RestController
@RequestMapping("/user")
public class UserController {
    private RemoteAuthService remoteAuthService;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteSendMailService remoteSendMailService;

    @ApiOperation("获取登录信息")
    @PostMapping(value = "/getLoginUser", produces = "application/json")
    public R<LoginResultVo> getLoginUser() {
        final String token = HeaderUtils.getToken();
        final LoginResultVo loginUser = remoteAuthService.getUserInfoByDb(token);
        return R.ok(loginUser);
    }

    /**
     * 修改密码流程1:验证旧密码
     * userId, oldPassword必填
     *
     * @param modifyPasswordDto 修改密码Dto
     * @return 修改结果
     */
    @ApiOperation("修改密码流程1:验证旧密码")
    @PostMapping(value = "/checkOldPassword", produces = "application/json")
    public R<Boolean> modifyPasswordCheck(@RequestBody ModifyPasswordDto modifyPasswordDto) {
        Assert.notNull(modifyPasswordDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(modifyPasswordDto.getOldPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.OLD_PASSWORD);
        //从请求头读取用户Id
        final Long userId = HeaderUtils.getUserId();
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        modifyPasswordDto.setUserId(userId);
        //验证旧密码
        remoteUserService.modifyPasswordCheck(modifyPasswordDto);
        return R.ok(true);
    }

    /**
     * 修改密码流程2:设置新密码
     *
     * @param modifyPasswordDto 修改密码Dto
     * @return 修改结果
     */
    @ApiOperation("修改密码流程2:设置新密码")
    @PostMapping(value = "/modifyPassword", produces = "application/json")
    public R<Boolean> modifyPassword(@RequestBody ModifyPasswordDto modifyPasswordDto) {
        Assert.notNull(modifyPasswordDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.isId(modifyPasswordDto.getUserId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.USER_ID);
        Assert.hasText(modifyPasswordDto.getOldPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.OLD_PASSWORD);
        Assert.hasText(modifyPasswordDto.getNewPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.NEW_PASSWORD);
        remoteUserService.modifyPassword(modifyPasswordDto);
        return R.ok(true);
    }

    /**
     * 忘记密码发送超文本链接修改密码邮件
     * @param email 邮箱
     * @return 修改结果
     */
    @ApiOperation("忘记密码发送超文本链接修改密码邮件")
    @GetMapping(value = "/forgetSendMail", produces = "application/json")
    public R<Boolean> forgetPasswordSendMail(@RequestParam("email") String email) {
        Assert.hasText(email, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        // 验证是否存在该email的已激活的帐号
        List<UserVo> userVos = remoteUserService.getList(new UserQuery().setEmail(email).setAccountStatus(AccountStatusEnum.ACTIVATED.getType()));
        if (CollectionUtils.isEmpty(userVos)) {
            throw new ServiceException(UserErrorCodeEnum.USER_NOT_FOUND);
        }
        // 验证通过发送邮件验证码
        remoteSendMailService.sendForgetPasswordHtml(email, userVos.get(0).getFirstName(), userVos.get(0).getLastName());
        return R.ok(true);
    }

    @ApiOperation("忘记密码点击超链接跳转验证身份")
    @PostMapping(value = "/forgetCheckUser", produces = "application/json")
    public R<Boolean> forgetPasswordCheck(@RequestBody ForgetPasswordRequestDto forgetPasswordRequestDto) {
        //验证请求参数
        Assert.notNull(forgetPasswordRequestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(forgetPasswordRequestDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(forgetPasswordRequestDto.getVerificationCode(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        //rpc调用验证验证码是否过期和正确
        remoteUserService.forgetPasswordCheck(forgetPasswordRequestDto);
        return R.ok(true);
    }

    @ApiOperation("忘记密码重新设置新密码")
    @PostMapping(value = "/resetPassword", produces = "application/json")
    public R<Boolean> forgetPasswordConfirm(@RequestBody ForgetPasswordRequestDto requestDto) {
        //验证请求参数
        Assert.notNull(requestDto, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Assert.hasText(requestDto.getEmail(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(requestDto.getVerificationCode(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        Assert.hasText(requestDto.getNewPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.NEW_PASSWORD);
        //rpc调用设置新密码
        remoteUserService.forgetPasswordSetNewPassword(requestDto);
        return R.ok(true);
    }


}
