package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.entity.I18nEnumAttribute;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.gateway.entity.consts.StringConst;
import com.chervon.fleet.user.api.entity.dto.AppSettingDto;
import com.chervon.fleet.user.api.entity.vo.AppSettingVo;
import com.chervon.fleet.user.api.service.RemoteAppSettingService;
import com.chervon.fleet.web.api.entity.consts.GlobalConsts;
import com.chervon.fleet.web.api.entity.vo.LanguageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Dictionary通用数据字典表相关接口
 *
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
@RestController
@RequestMapping("/setting")
@Api(tags = "用户设置setting")
public class UserSettingController {

    @DubboReference
    private RemoteAppSettingService remoteAppSettingService;

    @ApiOperation("语言列表信息")
    @GetMapping("/language")
    public R<List<LanguageVo>> getLanguageList() {
        //通过I18N资源包缓存读取枚举列表
        final List<I18nEnumAttribute> enumLanguages = I18nController.getEnum(GlobalConsts.DEFAULT_LANGUAGE_CODE, StringConst.LANGUAGE);
        //转换为语言列表结构返回
        final List<LanguageVo> languageVoList = enumLanguages.stream().map(a -> {
            return new LanguageVo().setLanguage(a.getDesc()).setLangCode(a.getType());
        }).collect(Collectors.toList());
        return R.ok(languageVoList);
    }

    @ApiOperation("获取用户配置")
    @GetMapping("/get")
    public R<AppSettingVo> getUserSetting() {
        final Long userId = UserContext.getUserId();
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.UID);
        final AppSettingVo userSetting = remoteAppSettingService.getUserSetting(userId);
        return R.ok(userSetting);
    }

    @ApiOperation("更新用户配置")
    @PostMapping("/save")
    public R<Void> saveUserSetting(@RequestBody AppSettingDto request) {
        Assert.notNull(request, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.REQUEST_DTO);
        Long userId = request.getUserId();
        if (userId == null) {
            userId = UserContext.getClientInfo().getUserId();
            request.setUserId(userId);
            request.setCompanyId(UserContext.getClientInfo().getCompanyId());
        }
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.USER_ID);
        remoteAppSettingService.saveSetting(request);
        return R.ok();
    }

}
