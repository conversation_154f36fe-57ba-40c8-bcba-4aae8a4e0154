package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.fleet.gateway.entity.consts.StringConst;
import com.chervon.fleet.user.api.entity.dto.LoginDto;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.service.RemoteAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * 用户登录、获取公钥、注销、鉴权相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@RestController
@Slf4j
@RequestMapping("/app/auth")
@Api(tags = "用户鉴权")
public class UserAuthController {

    @DubboReference
    private RemoteAuthService remoteAuthService;

    @ApiOperation("fleet用户登录")
    @PostMapping(value = "/login", produces = "application/json")
    public R<LoginResultVo> login(@RequestBody LoginDto loginDto) {
        Assert.hasText(HeaderUtils.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_ID);
        Assert.notNull(HeaderUtils.getDeviceType(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.DEVICE_TYPE);
        LoginResultVo loginInfo = remoteAuthService.login(loginDto);
        return R.ok(loginInfo);
    }

    @ApiOperation("获取公钥用于加密密码")
    @GetMapping(value = "/publicKey")
    public R<String> getPublicKey() {
        String publicKey = remoteAuthService.getPublicKey();
        return R.ok(publicKey);
    }

    /**
     * 用户注销登录状态
     *
     * @return
     */
    @ApiOperation("用户注销接口")
    @GetMapping(value = "/logout", produces = "application/json")
    public R<Boolean> logout() {
        return R.ok(remoteAuthService.logout());
    }
}
