package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.dto.BaseDeviceDto;
import com.chervon.fleet.web.api.entity.dto.RnDetailRequest;
import com.chervon.fleet.web.api.entity.enums.BusinessTypeEnum;
import com.chervon.fleet.web.api.entity.vo.DeviceErrorVo;
import com.chervon.fleet.web.api.entity.vo.RnDetailVo;
import com.chervon.fleet.web.api.service.RemoteFleetAppService;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import com.chervon.technology.api.RemoteProductRnService;
import com.chervon.technology.api.dto.LastRnPackageDto;
import com.chervon.technology.api.vo.LastRnPackageInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设备RN面板相关接口
 *
 * <AUTHOR> 2023/6/28
 */
@Api(tags = "设备RN面板相关接口")
@RestController
@Slf4j
@RequestMapping("/device/rn")
public class DeviceRnController {
    @DubboReference
    private RemoteFleetDeviceService remoteFleetDeviceService;
    @DubboReference
    private RemoteProductRnService remoteProductRnService;
    @DubboReference
    private RemoteFleetAppService remoteFleetAppService;

    @ApiOperation(value = "RN面板设备信息详情")
    @PostMapping(value = "/detail")
    public RnDetailVo getRnDetail(@RequestBody RnDetailRequest req) {
        Assert.notNull(req.getDeviceId(), ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
        Assert.notNull(req.getAppType(), ErrorCode.PARAMETER_NOT_PROVIDED, "appType");
        Assert.notNull(req.getAppVersion(), ErrorCode.PARAMETER_NOT_PROVIDED, "appVersion");
        req.setBusinessType(BusinessTypeEnum.FLEET.getType());
        return remoteFleetDeviceService.getRnDetail(req);
    }

    /**
     * 检查Rn版本
     * @param req 入参
     * @return 最新rn包信息
     */
    @ApiOperation("获取指定产品下的最新rn包信息")
    @PostMapping("/lastInfo")
    public LastRnPackageInfoVo lastInfo(@Validated @RequestBody LastRnPackageDto req, @RequestHeader("appVersion") String appVersion) {
        //req报文体内appVersion app传的固定值，现改为以请求头为准
        if(StringUtils.isNotEmpty(appVersion)){
            req.setAppVersion(appVersion);
        }
        final Long userId = UserContext.getUserId();
        req.setBusinessType(BusinessTypeEnum.FLEET.getType());
        LastRnPackageInfoVo res = remoteProductRnService.lastInfo(req, userId);
        res.setProductId(req.getProductId());
        return res;
    }

    /**
     * 获取指定设备故障信息
     * @param request 入参
     * @return 最新rn包信息
     */
    @ApiOperation("获取指定设备故障信息")
    @PostMapping("/faultInfo")
    public DeviceErrorVo getDeviceFaultInfo(@RequestBody BaseDeviceDto request) {
        Assert.notNull(request.getDeviceId(),ErrorCode.PARAMETER_NOT_PROVIDED,"deviceId");
        return remoteFleetAppService.getLastDeviceError(request.getDeviceId(),24*7);
    }
}