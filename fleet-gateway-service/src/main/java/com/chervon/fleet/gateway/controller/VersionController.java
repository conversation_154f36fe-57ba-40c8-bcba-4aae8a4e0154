package com.chervon.fleet.gateway.controller;

import com.chervon.fleet.web.api.entity.enums.BusinessTypeEnum;
import com.chervon.operation.api.RemoteAppVersionService;
import com.chervon.operation.api.vo.AppVersionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 安卓版本相关接口
 *
 * <AUTHOR> 2023/6/28
 */
@Slf4j
@Api(tags = "安卓版本")
@RestController
@RequestMapping("/version")
public class VersionController {

    @DubboReference
    private RemoteAppVersionService remoteAppVersionService;

    @ApiOperation("获取fleet gateway app最新版本")
    @GetMapping(value = "/android/latest")
    public AppVersionVo getLatestVersion() {
        return remoteAppVersionService.latest(BusinessTypeEnum.FLEET.getType());
    }
}
