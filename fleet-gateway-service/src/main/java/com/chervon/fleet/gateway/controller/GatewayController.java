package com.chervon.fleet.gateway.controller;

import com.chervon.fleet.web.api.entity.dto.GatewayDeviceListDto;
import com.chervon.fleet.web.api.entity.dto.GatewayDto;
import com.chervon.fleet.web.api.entity.vo.FixGatewayCoordinateVo;
import com.chervon.fleet.web.api.entity.vo.GatewayDeviceInfoVo;
import com.chervon.fleet.web.api.entity.vo.GatewayIndexVo;
import com.chervon.fleet.web.api.entity.vo.GatewayVo;
import com.chervon.fleet.web.api.service.RemoteFleetGatewayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 网关相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@Slf4j
@RestController
@RequestMapping("/gateway")
@Api(tags = "网关")
public class GatewayController {

    @DubboReference
    private RemoteFleetGatewayService remoteFleetGatewayService;

    @ApiOperation("判断当前用户是否有软网关，如果有返回gateway信息")
    @GetMapping(value = "/gatewayIndex")
    public GatewayIndexVo gatewayIndex(@RequestParam("uniqueId") String uniqueId) {
        return remoteFleetGatewayService.gatewayIndex(uniqueId);
    }

    @ApiOperation("创建网关")
    @PostMapping(value = "/create")
    public void create(@RequestBody GatewayDto req) {
        remoteFleetGatewayService.create(req);
    }

    @ApiOperation("编辑网关")
    @PostMapping(value = "/edit")
    public void edit(@RequestBody GatewayDto req) {
        remoteFleetGatewayService.edit(req);
    }

    @ApiOperation("移除网关--传入gatewayId")
    @GetMapping(value = "/remove")
    public void remove(@RequestParam("gatewayId") String gatewayId) {
        remoteFleetGatewayService.remove(gatewayId);
    }

    @ApiOperation("获取网关详情信息--传入gatewayId")
    @GetMapping(value = "/getGatewayDetail")
    public GatewayVo getGatewayDetail(@RequestParam("gatewayId") String gatewayId) {
        return remoteFleetGatewayService.getGatewayDetail(gatewayId);
    }

    @ApiOperation("网关下扫描到的设备列表")
    @PostMapping(value = "/listDevice")
    public List<GatewayDeviceInfoVo> listDevice(@RequestBody GatewayDeviceListDto req) {
        return remoteFleetGatewayService.listDevice(req);
    }

    @ApiOperation("获取固定网关坐标列表")
    @PostMapping(value = "/fixGatewayCoordinate")
    public FixGatewayCoordinateVo fixGatewayCoordinate() {
        return remoteFleetGatewayService.fixGatewayCoordinate();
    }

}
