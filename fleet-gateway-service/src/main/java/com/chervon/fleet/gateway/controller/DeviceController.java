package com.chervon.fleet.gateway.controller;

import com.chervon.fleet.web.api.entity.dto.BaseDeviceDto;
import com.chervon.fleet.web.api.entity.dto.DeviceEditDto;
import com.chervon.fleet.web.api.entity.vo.BasicStatusVo;
import com.chervon.fleet.web.api.entity.vo.DeviceInfoVo;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * 设备相关接口
 *
 * <AUTHOR> 2023/6/28
 */
@Api(tags = "设备绑定解绑详情接口")
@RestController
@Slf4j
@RequestMapping("/device")
public class DeviceController {

    @DubboReference
    private RemoteFleetDeviceService remoteFleetDeviceService;

    @ApiOperation(value = "绑定设备到租户下")
    @PostMapping(value = "/bind")
    public void bind(@RequestBody BaseDeviceDto req) {
        remoteFleetDeviceService.bind(req);
    }

    @ApiOperation("解绑设备")
    @GetMapping("unbind")
    public void unbind(@RequestParam("deviceId") String deviceId) {
        remoteFleetDeviceService.unbind(deviceId);
    }

    /**
     * 编辑设备名称
     * @param req 请求对象
     */
    @ApiOperation("编辑设备名称")
    @PostMapping("edit")
    public void editName(@RequestBody DeviceEditDto req) {
        remoteFleetDeviceService.editName(req);
    }

    @ApiOperation("校验SN的合法性之后获取产品ID")
    @GetMapping("getProductIdBySn")
    public Long getProductIdBySn(@RequestParam("deviceSn") String deviceSn) {
        return remoteFleetDeviceService.getProductIdBySn(deviceSn);
    }

    /**
     * 获取设备状态(点击卡片调用)
     * @param  requestDto 请求对象
     * @return 设备状态
     */
    @ApiOperation(value = "获取设备状态(点击卡片调用)")
    @PostMapping("/get/status")
    public BasicStatusVo getStatus(@RequestBody BaseDeviceDto requestDto) {
        return remoteFleetDeviceService.getDeviceStatus(requestDto);
    }

    @ApiOperation(value = "根据设备id、sn查询设备基础信息详情")
    @PostMapping(value = "/detail")
    public DeviceInfoVo detail(@RequestBody BaseDeviceDto req) {
        return remoteFleetDeviceService.detail(req);
    }
}
