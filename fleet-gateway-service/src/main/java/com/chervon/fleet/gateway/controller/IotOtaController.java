package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.domain.R;
import com.chervon.common.core.domain.SingleInfoReq;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.web.api.entity.enums.BusinessTypeEnum;
import com.chervon.technology.api.RemoteOtaJobService;
import com.chervon.technology.api.dto.DeviceJobResultDto;
import com.chervon.technology.api.dto.ota.ComponentResultDto;
import com.chervon.technology.api.vo.ota.OtaHistoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-17
 */
@Api(tags = "固件升级相关接口")
@RestController
@RequestMapping("/ota")
public class IotOtaController {

    @DubboReference
    private RemoteOtaJobService remoteOtaJobService;

    /**
     * 获取设备升级历史
     *
     * @return
     */
    @ApiOperation("获取设备升级历史")
    @PostMapping("/history")
    public R<List<OtaHistoryVo>> getOtaHistory(@RequestBody SingleInfoReq<String> deviceId) {
        long userId = UserContext.getUserId();
        List<OtaHistoryVo> otaHistoryVoList = remoteOtaJobService.getOtaHistory(deviceId.getReq(), userId);
        return R.ok(otaHistoryVoList);
    }

    /**
     * 获取设备总成升级状态
     *
     * @return/device/component/result
     */
    @ApiOperation("获取设备总成升级状态")
    @PostMapping("/device/component/result")
    public R<List<ComponentResultDto>> getDeviceComponentResult(@RequestBody DeviceJobResultDto deviceJobResultDto) {
        return R.ok(remoteOtaJobService.getDeviceComponentResult(deviceJobResultDto));
    }

    @ApiOperation("app端确认设备升级")
    @PostMapping("/device/upgrade/confirm")
    public void confirmDeviceUpgrade(@RequestBody DeviceJobResultDto deviceJobResultDto) {
        if(deviceJobResultDto.getUserId()==null || deviceJobResultDto.getUserId()==0L){
            deviceJobResultDto.setUserId(UserContext.getUserId());
        }
        if(CollectionUtils.isEmpty(deviceJobResultDto.getJobId())
                || StringUtils.isEmpty(deviceJobResultDto.getDeviceId())){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"jobId or deviceId");
        }
        deviceJobResultDto.setBusinessType(BusinessTypeEnum.FLEET.getType());
        remoteOtaJobService.confirmDeviceUpgrade(deviceJobResultDto);
    }
}
