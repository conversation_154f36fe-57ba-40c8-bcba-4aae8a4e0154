package com.chervon.fleet.gateway.controller;

import com.chervon.fleet.web.api.entity.dto.InventorySearchDto;
import com.chervon.fleet.web.api.entity.vo.FilterConditionVo;
import com.chervon.fleet.web.api.entity.vo.InventoryInfoVo;
import com.chervon.fleet.web.api.entity.vo.InventorySearchVo;
import com.chervon.fleet.web.api.service.RemoteFleetInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * 设备库存相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@Slf4j
@RestController
@RequestMapping("/inventory")
@Api(tags = "库存")
public class InventoryController {

    @DubboReference
    private RemoteFleetInventoryService remoteFleetInventoryService;

    @ApiOperation(value = "根据登录的租户id获取filter筛选面板")
    @GetMapping(value = "/filterCondition")
    public FilterConditionVo filterCondition(@RequestParam("search") String search) {
        return remoteFleetInventoryService.filterCondition(search);
    }

    /**
     * 根据filter条件和输入的设备名称筛选出的数量，区分库存和盘点
     * @param req
     * @return
     */
    @ApiOperation(value = "根据filter条件和输入的设备名称筛选出的数量，区分库存和盘点")
    @PostMapping(value = "/countFilterAndSearch")
    public Long countFilterAndSearch(@RequestBody InventorySearchDto req) {
        return remoteFleetInventoryService.countFilterAndSearch(req);
    }

    /**
     * 根据filter条件和输入的设备名称筛选出的数据列表，区分库存和盘点
     * @param req
     * @return
     */
    @ApiOperation(value = "根据filter条件和输入的设备名称筛选出的数据列表，区分库存和盘点")
    @PostMapping(value = "/listFilterAndSearch")
    public InventorySearchVo listFilterAndSearch(@RequestBody InventorySearchDto req) {
        return remoteFleetInventoryService.listFilterAndSearch(req);
    }

    @ApiOperation(value = "设备详情")
    @GetMapping(value = "/detail")
    public InventoryInfoVo detail(@RequestParam("deviceId") String deviceId) {
        return remoteFleetInventoryService.detail(deviceId);
    }

}
