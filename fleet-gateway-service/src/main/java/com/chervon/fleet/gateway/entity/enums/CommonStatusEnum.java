package com.chervon.fleet.gateway.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * 通用状态枚举
 * <AUTHOR> 2023/5/25
 */
public enum CommonStatusEnum implements TypeEnum {
    /**
     * 关闭
     */
    OFF(0, "关闭"),
    /**
     * 打开
     */
    ON(1, "打开");

    private int type;
    private String desc;

    CommonStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}