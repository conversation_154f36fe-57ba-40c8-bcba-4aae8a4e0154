package com.chervon.fleet.gateway.controller;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.web.api.entity.dto.DataReportCompleteDto;
import com.chervon.fleet.web.api.entity.dto.GatewayScanReportingDto;
import com.chervon.fleet.web.api.entity.dto.IotUpdateOnlineStatusDto;
import com.chervon.fleet.web.api.entity.enums.OnlineStatusTypeEnum;
import com.chervon.fleet.web.api.entity.enums.SoftGatewayTypeEnum;
import com.chervon.fleet.web.api.service.RemoteRuleEngineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 接收规则引擎消息
 * @date 2023/7/7 11:47
 */
@RestController
@AllArgsConstructor
@RequestMapping("/rule")
@Api(tags = "规则引擎消息处理")
@Slf4j
public class RuleEngineController {

    @DubboReference
    private RemoteRuleEngineService remoteRuleEngineService;

    /**
     * 接收规则引擎目标地址确认token,用于云端配置规则验证激活
     *
     * @param confirmationToken: 目标地址确认token
     * <AUTHOR>
     * @date 16:53 2022/5/16
     **/
    @PostMapping
    @ApiOperation("接收规则引擎目标地址确认token")
    public void receiveConfirmationToken(@RequestParam String confirmationToken) {
        log.info("confirmationToken:{}", confirmationToken);
    }

    /**
     * 接收规则引擎转发的网关扫描到的设备列表的状态
     * 云端规则引擎对应的主题: aws/fleet/gateway/${type}/${gatewayId}/scanStatus
     * 说明：${type}：1硬件网关 2软网关  ${gatewayId}：网关id
     * 规则引擎: *_online_state_update
     * eg:  aws/fleet/gateway/1/1/scanStatus
     * <p>
     * 本质上是请求到后端然后对数据进行判断，如果数据存在且正常，则：
     * 1.发布mqtt消息通知android端更新设备库存状态
     * 发送的目标topic: aws/fleet/gateway/scanStatus/1/get/accepted
     * 2.异步批量更新设备表库存状态
     *
     * @param gatewayScanReportingDto:
     * <AUTHOR>
     * @date 2023/7/12
     **/
    @PostMapping("/gateway/scanStatus")
    @ApiOperation("接收规则引擎转发的网关扫描到的设备列表的状态")
    public void fleetScanStatus(@RequestHeader String topic, @RequestBody GatewayScanReportingDto gatewayScanReportingDto) {
        //保存网关扫描的设备库存状态到redis进行缓存，保存两个缓存结构，1：按租户维度维护库存状态  2：按网关Id维度维护网关和设备的关系
        //更新t_company_device表网关对应设备id的在库状态
        //发布topic给前端更新状态
        // 1硬件网关 2软网关Soft and hard gateway types
        Integer softHardGatewayType = Integer.parseInt(topic.split("/")[CommonConstant.THREE]);
        if(Objects.isNull(softHardGatewayType)){
            return;
        }
        // 网关id
        String gatewayId = topic.split("/")[CommonConstant.FOUR];
        if (Objects.isNull(gatewayScanReportingDto)
                || Objects.isNull(gatewayScanReportingDto.getGateway())
                || CollectionUtils.isEmpty(gatewayScanReportingDto.getDevices())) {
            log.error("网关扫描设备库存状态上报请求参数为空！");
            return;
        }
        //兼容设备端不上报固定或移动网关类型：1固定网关，2移动网关 做的兼容；
        if(Objects.isNull(gatewayScanReportingDto.getGateway().getGatewayType())){
            gatewayScanReportingDto.getGateway().setGatewayType(SoftGatewayTypeEnum.FIXED.getType());
        }
        remoteRuleEngineService.refreshWarehouseStatus(gatewayScanReportingDto, softHardGatewayType, gatewayId);
    }

    /**
     * 接收规则引擎转发的设备数据上报完成状态
     * 对应的主题: aws/fleet/device/${gatewayId}/${deviceId}/dataReportComplete
     * 规则引擎: *_fleet_device_dataReportComplete_status
     * eg:  $aws/events/presence/disconnected/XTR012283419494
     * $aws/events/presence/connected/XTR012283419494
     *
     * @param dataReportCompleteDto:
     * <AUTHOR>
     * @date 2023/7/12
     **/
    @PostMapping("/device/dataReportComplete")
    @ApiOperation("接收规则引擎转发的设备数据上报完成状态")
    public void dataReportComplete(@RequestHeader String topic, @RequestBody DataReportCompleteDto dataReportCompleteDto) {
        //更新设备数据上报完成标记
        // 网关id
        String gatewayId = topic.split("/")[CommonConstant.THREE];
        // 设备id
        String deviceId = topic.split("/")[CommonConstant.FOUR];
        if (Objects.isNull(dataReportCompleteDto)
                || StringUtils.isEmpty(gatewayId)
                || StringUtils.isEmpty(deviceId)) {
            log.error("设备上报数据完成请求参数为空！");
            return;
        }
        remoteRuleEngineService.dataReportComplete(dataReportCompleteDto, gatewayId, deviceId);
    }

    /**
     * 接收规则引擎转发的设备连接状态更新（软网关）
     * 对应的主题:$aws/events/presence/+/#
     * 规则引擎: *_fleet_gateway_online_state_update
     * eg:  $aws/events/presence/disconnected/gatewayIdXTR012283419494
     *      $aws/events/presence/connected/gatewayIdXTR012283419494
     *
     * @param req:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     **/
    @PostMapping("/online")
    @ApiOperation("接收规则引擎转发的设备连接状态更新")
    public void receiveGatewayOnlineStatus(@RequestBody IotUpdateOnlineStatusDto req) {
        if (Objects.isNull(req)) {
            log.error("网关在线离线状态上报请求参数为空！");
            return;
        }
        //兼容赋值
        if(StringUtils.isEmpty(req.getStatus()) && !StringUtils.isEmpty(req.getEventType())){
            req.setStatus(req.getEventType());
        }
        remoteRuleEngineService.onlineStatusReport(OnlineStatusTypeEnum.GATEWAY, req);
    }

    /**
     * 接收规则引擎转发的设备上报日志
     * 对应的topic：$aws/things/+/shadow/update/accepted
     * 规则引擎：*_shadow_reported
     *
     * @param reported:
     * <AUTHOR>
     * @date 16:34 2022/5/16
     **/
    @PostMapping(("/log/reported"))
    @ApiOperation(value = "接收规则引擎转发的设备上报日志")
    public void reportedLog(@RequestBody Object reported, @RequestHeader String topic) {
        String deviceId = topic.substring(CommonConstant.TWELVE, topic.indexOf("/shadow/update"
                + "/accepted"));
        remoteRuleEngineService.saveFaultData(deviceId, reported);
    }

}
