package com.chervon.fleet.gateway;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Spring Boot Starter
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.chervon.common","com.chervon.fleet"},exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
@EnableDubbo
@EnableAsync
public class FleetGatewayApplication {
    public static void main(String[] args) {
        SpringApplication.run(FleetGatewayApplication.class, args);
    }
}