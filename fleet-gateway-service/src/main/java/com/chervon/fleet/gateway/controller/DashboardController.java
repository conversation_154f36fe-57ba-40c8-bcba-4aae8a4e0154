package com.chervon.fleet.gateway.controller;

import com.chervon.fleet.web.api.entity.vo.*;
import com.chervon.fleet.web.api.service.RemoteFleetAppDashboardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * app看板相关接口
 *
 * <AUTHOR> 2023/6/27
 */
@Api(tags = "库存及充电器看板")
@RestController
@Slf4j
@RequestMapping("/dashboard")
public class DashboardController {

    @DubboReference
    private RemoteFleetAppDashboardService remoteFleetAppDashboardService;

    @ApiOperation(value = "Inventory")
    @GetMapping(value = "/inventory")
    public DashboardInventoryVo inventory() {
        return remoteFleetAppDashboardService.inventory();
    }

    @ApiOperation(value = "Inventory-故障详情列表")
    @GetMapping(value = "/inventory/error")
    public List<DashboardErrorVo> inventoryError() {
        return remoteFleetAppDashboardService.inventoryError();
    }

    /**
     * UI Power部分接口：查询t_bi_charging_status
     * company_device,bi_charging_status,bi_device_error_list
     *
     * @return
     */
    @ApiOperation(value = "Power：查询充电器总览信息：t_bi_charging_status")
    @GetMapping(value = "/power")
    public DashboardPowerVo power() {
        return remoteFleetAppDashboardService.power();
    }

    /**
     * Power Availability和Power on Charge、Total Battery
     * @return
     */
    @ApiOperation(value = "Power Availability和Power on Charge、Total Battery")
    @GetMapping(value = "/powerAvailability")
    public DashboardPowerAvailabilityVo powerAvailability() {
        return remoteFleetAppDashboardService.powerAvailability();
    }

    /**
     * 查询PowerHub表和故障列表
     * @return
     */
    @ApiOperation(value = "Charging Overview")
    @GetMapping(value = "/powerOverview")
    public List<DashboardPowerHubVo> powerOverview() {
        return remoteFleetAppDashboardService.powerOverview();
    }

}
