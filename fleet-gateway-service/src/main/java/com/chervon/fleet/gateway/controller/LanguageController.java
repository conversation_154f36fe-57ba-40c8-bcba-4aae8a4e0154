package com.chervon.fleet.gateway.controller;

import com.chervon.common.web.util.UserContext;
import com.chervon.configuration.api.core.MultiLanguageRespBo;
import com.chervon.configuration.api.core.StaticMultiLanguageReqDto;
import com.chervon.configuration.api.service.RemoteMultiLanguageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/1 16:31
 */
@Api(tags = "多语言相关")
@RestController
@RequestMapping("/language")
@Slf4j
public class LanguageController {

    @DubboReference(timeout = 20000)
    private RemoteMultiLanguageService remoteMultiLanguageService;

    @ApiOperation("获取app相关的多语言")
    @PostMapping
    public Map<String, Map<String, String>> list(@RequestBody List<String> list) {
        String lang= UserContext.getLanguage();
        return remoteMultiLanguageService.listLanguageBySysCodes(list, lang);
    }

    @ApiOperation("获取app新增和变更的多语言词条")
    @PostMapping("/getDiff")
    public MultiLanguageRespBo getDiffList(@RequestBody StaticMultiLanguageReqDto multiLanguageRequestDto) {
        return remoteMultiLanguageService.getMultiLanguage(multiLanguageRequestDto);
    }


}
