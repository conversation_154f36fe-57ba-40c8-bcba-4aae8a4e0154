<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 日志文件目录 -->
    <property name="GROUP" value="/opt/iot-group"/>
    <property name="LOG_HOME" value="/logs" />
    <!-- 说明： 1. 文件的命名和加载顺序有关 logback.xml早于application.yml加载，logback-spring.xml晚于application.yml加载 如果logback配置需要使用application.yml中的属性，需要命名为logback-spring.xml 2. logback使用application.yml中的属性 使用springProperty才可使用application.yml中的值 可以设置默认值 -->
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProperty scope="context" name="springProfile" source="spring.profiles.active"/>

    <property name="STDOUT_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(-%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr(%-40.40logger{39}){cyan} %clr(#%-5line) %X{traceId:-} %clr(:){faint} %clr(%mdc{requestId}){magenta} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!-- %m输出的信息,%p日志级别,%t线程名,%d日期,%c类的全名,%i索引【从数字0开始递增】,,, -->
    <!-- appender是configuration的子节点，是负责写日志的组件。 -->
    <!-- ConsoleAppender：把日志输出到控制台 -->
<!--    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">-->
<!--        <encoder>-->
<!--            <pattern>${STDOUT_PATTERN}</pattern>-->
<!--            &lt;!&ndash; 控制台也要使用UTF-8，不要使用GBK，否则会中文乱码 &ndash;&gt;-->
<!--            <charset>UTF-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${STDOUT_PATTERN}</pattern>
            </layout>
            <!-- 控制台也要使用UTF-8，不要使用GBK，否则会中文乱码 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 使用json格式保存日志文件(非ERROR) -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GROUP}/${springProfile}/${LOG_HOME}/${springAppName}/${hostname}-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 活动文件的名字会根据fileNamePattern的值，每隔一段时间改变一次 -->
            <fileNamePattern>${GROUP}/${springProfile}/${LOG_HOME}/${springAppName}/${hostname}-info.%d.%i.log</fileNamePattern>
            <!-- 每产生一个日志文件，该日志文件的保存期限为10天 -->
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
<!--        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">-->
<!--            <pattern>-->
<!--                {"date":"%d{yyyy-MM-dd HH:mm:ss.SSS}","severity": "%level","service": "${springAppName:-}","env": "${springProfile:-}","hostname": "${hostname}","trace": "%X{traceId:-}","TxId": "%X{PtxId}","span": "%X{X-B3-SpanId:-}","parent": "%X{X-B3-ParentSpanId:-}","exportable": "%X{X-Span-Export:-}","pid": "${PID:-}","thread": "%thread","class": "%logger{40}","rest": "%message","exception": "%exception{full}"}%n-->
<!--            </pattern>-->
<!--        </encoder>-->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    {"date":"%d{yyyy-MM-dd HH:mm:ss.SSS}","severity": "%level","service": "${springAppName:-}","env": "${springProfile:-}","hostname": "${hostname}","trace": "%tid","pid": "${PID:-}","thread": "%thread","class": "%logger{40}","rest": "%message","exception": "%exception{full}"}%n
                </pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
    </appender>

    <!-- 按照每天生成日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${GROUP}/${springProfile}/${LOG_HOME}/${springAppName}/${hostname}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <fileNamePattern>${GROUP}/${springProfile}/${LOG_HOME}/${springAppName}/${hostname}-error.%d.log</fileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>7</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!--<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
            <pattern>
                {"date":"%d{yyyy-MM-dd HH:mm:ss.SSS}","severity": "%level","service": "${springAppName:-}","env": "${springProfile:-}","hostname": "${hostname}","trace": "%X{traceId:-}","TxId": "%X{PtxId}","span": "%X{X-B3-SpanId:-}","parent": "%X{X-B3-ParentSpanId:-}","exportable": "%X{X-Span-Export:-}","pid": "${PID:-}","thread": "%thread","class": "%logger{40}","rest": "%message","exception": "%exception{full}"}%n
            </pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <appender name="ASYNC_INFO_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <appender-ref ref="INFO_FILE"/>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <appender name="sk-log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <!-- 日志输出编码 -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>
                    {"date":"%d{yyyy-MM-dd HH:mm:ss.SSS}","severity": "%level","service": "${springAppName:-}","env": "${springProfile:-}","hostname": "${hostname}","trace": "%X{tid}","pid": "${PID:-}","thread": "%thread","class": "%logger{40}","rest": "%message","exception": "%exception{full}"}%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 指定项目中某个包，当有日志操作行为时的日志记录级别 -->
    <!-- com.liyan为根包，也就是只要是发生在这个根包下面的所有日志操作行为的账户都是DEBUG -->
    <!-- 级别依次为【从高到低】：FATAL > ERROR > WARN > INFO > DEBUG > TRACE -->
    <logger name="org.springframework" level="WARN"/>

    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="sk-log"/>
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
        <logger name="com.baomidou" level="DEBUG"/>
    </springProfile>

    <springProfile name="sit">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="sk-log"/>
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
        <logger name="io" level="WARN"/>
        <logger name="org" level="WARN"/>
        <logger name="javax" level="WARN"/>
        <logger name="com.netflix" level="WARN"/>
        <logger name="springfox" level="WARN"/>
        <logger name="com.baomidou" level="WARN"/>
        <logger name="reactor" level="WARN"/>
    </springProfile>

    <springProfile name="pre">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="sk-log"/>
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
        <logger name="io" level="WARN"/>
        <logger name="org" level="WARN"/>
        <logger name="javax" level="WARN"/>
        <logger name="com.netflix" level="WARN"/>
        <logger name="springfox" level="WARN"/>
        <logger name="com.baomidou" level="WARN"/>
        <logger name="reactor" level="WARN"/>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="sk-log"/>
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
        <logger name="io" level="WARN"/>
        <logger name="org" level="WARN"/>
        <logger name="javax" level="WARN"/>
        <logger name="com.netflix" level="WARN"/>
        <logger name="springfox" level="WARN"/>
        <logger name="com.baomidou" level="WARN"/>
        <logger name="reactor" level="WARN"/>
    </springProfile>

</configuration>