[{"key": "language", "attributeList": [{"type": "en", "desc": "English"}, {"type": "fr", "desc": "Français"}, {"type": "de", "desc": "De<PERSON>ch"}, {"type": "nl", "desc": "Nederlands"}, {"type": "es", "desc": "Español"}]}, {"key": "WarehouseStatusEnum", "attributeList": [{"type": "0", "desc": "<PERSON><PERSON> g<PERSON>"}, {"type": "1", "desc": "<PERSON><PERSON>"}, {"type": "2", "desc": "Im Einsatz außer <PERSON>"}, {"type": "3", "desc": "Unbekannter Standort"}, {"type": "4", "desc": "Gelöscht"}]}, {"key": "FleetDeviceOnlineStatusEnum", "attributeList": [{"type": "0", "desc": "Offline"}, {"type": "1", "desc": "Online"}]}, {"key": "MaintenanceStatusEnum", "attributeList": [{"type": "1", "desc": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "2", "desc": "Ein"}, {"type": "3", "desc": "Aus"}]}, {"key": "INVENTORY_TITLE", "attributeList": [{"type": "name", "desc": "Name"}, {"type": "sn", "desc": "SN"}, {"type": "category", "desc": "<PERSON><PERSON><PERSON>"}, {"type": "model", "desc": "Modell Nr."}, {"type": "status", "desc": "Inventarstatus"}]}, {"key": "MAINTENANCE_TITLE", "attributeList": [{"type": "name", "desc": "Name"}, {"type": "sn", "desc": "SN"}, {"type": "category", "desc": "<PERSON><PERSON><PERSON>"}, {"type": "model", "desc": "Modell Nr."}, {"type": "serviceType", "desc": "Art der Dienstleistung"}, {"type": "maintenanceDate", "desc": "Datum"}, {"type": "runtime", "desc": "Laufzeit"}, {"type": "laborTime", "desc": "Arbeitszeit"}, {"type": "expenses", "desc": "Ausgaben"}, {"type": "performed<PERSON><PERSON>", "desc": "Durchgefü<PERSON><PERSON> von"}, {"type": "contents", "desc": "Inhalt"}]}, {"key": "MaintenanceLogServiceTypeEnum", "attributeList": [{"type": "1", "desc": "Wartung"}, {"type": "2", "desc": "Inspektion"}, {"type": "3", "desc": "Reparatur"}]}, {"key": "USAGE_TITLE", "attributeList": [{"type": "name", "desc": "Name"}, {"type": "sn", "desc": "SN"}, {"type": "category", "desc": "<PERSON><PERSON><PERSON>"}, {"type": "model", "desc": "Modell Nr."}, {"type": "tag", "desc": "Tag"}, {"type": "dailyAverageUsageTime", "desc": "Tägliche durchschnittliche Nutzungsdauer"}, {"type": "usageTimeSelectedRange", "desc": "Nutzungsdauer in vorgewählter Zeitspanne"}, {"type": "totalUsageTime", "desc": "Gesamte Nutzungsdauer"}, {"type": "dataStartTime", "desc": "Startzeit der Daten"}, {"type": "dataEndTime", "desc": "Uhrzeit des Datenendes"}]}]