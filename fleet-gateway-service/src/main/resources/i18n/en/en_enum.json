[{"key": "language", "attributeList": [{"type": "en", "desc": "English"}, {"type": "fr", "desc": "Français"}, {"type": "de", "desc": "De<PERSON>ch"}, {"type": "nl", "desc": "Nederlands"}, {"type": "es", "desc": "Español"}]}, {"key": "WarehouseStatusEnum", "attributeList": [{"type": "0", "desc": "Never Seen"}, {"type": "1", "desc": "In Warehouse"}, {"type": "2", "desc": "Out for Work"}, {"type": "3", "desc": "Unknown Current Location"}, {"type": "4", "desc": "Deleted"}]}, {"key": "FleetDeviceOnlineStatusEnum", "attributeList": [{"type": "0", "desc": "Offline"}, {"type": "1", "desc": "Online"}]}, {"key": "MaintenanceStatusEnum", "attributeList": [{"type": "1", "desc": "Due"}, {"type": "2", "desc": "On"}, {"type": "3", "desc": "Off"}]}, {"key": "INVENTORY_TITLE", "attributeList": [{"type": "name", "desc": "Name"}, {"type": "sn", "desc": "SN"}, {"type": "category", "desc": "Category"}, {"type": "model", "desc": "Model#"}, {"type": "status", "desc": "Inventory Status"}]}, {"key": "MAINTENANCE_TITLE", "attributeList": [{"type": "name", "desc": "Name"}, {"type": "sn", "desc": "SN"}, {"type": "category", "desc": "Category"}, {"type": "model", "desc": "Model#"}, {"type": "serviceType", "desc": "Service Type"}, {"type": "maintenanceDate", "desc": "Date"}, {"type": "runtime", "desc": "Runtime"}, {"type": "laborTime", "desc": "Labor Time"}, {"type": "expenses", "desc": "Expenses"}, {"type": "performed<PERSON><PERSON>", "desc": "Performed By"}, {"type": "contents", "desc": "Contents"}]}, {"key": "MaintenanceLogServiceTypeEnum", "attributeList": [{"type": "1", "desc": "Maintenance"}, {"type": "2", "desc": "Inspection"}, {"type": "3", "desc": "Repair"}]}, {"key": "USAGE_TITLE", "attributeList": [{"type": "name", "desc": "Name"}, {"type": "sn", "desc": "SN"}, {"type": "category", "desc": "Category"}, {"type": "model", "desc": "Model#"}, {"type": "tag", "desc": "Tag"}, {"type": "dailyAverageUsageTime", "desc": "Daily average usage time"}, {"type": "usageTimeSelectedRange", "desc": "Usage time-selected range"}, {"type": "totalUsageTime", "desc": "Total usage time"}, {"type": "dataStartTime", "desc": "Data start time"}, {"type": "dataEndTime", "desc": "Data end time"}]}]