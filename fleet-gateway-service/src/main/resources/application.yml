# Tomcat
server:
  port: 8090

# Spring
spring:
  application:
    # 应用名称
    name: fleet-gateway
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
#      server-addr: ${NACOS_SERVER_ADDR}
#      server-addr: nacos.sit.na.chervoncloud.com:8848
      discovery:
        # 注册组
        group: IOT_GROUP
        namespace: iot
      config:
        # 配置组
        group: IOT_GROUP
        namespace: iot
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
