[{"code": "500", "reason": "Service internal exception"}, {"code": "602", "reason": "Service timeout!"}, {"code": "1401", "reason": "The user is not logged in."}, {"code": "1403", "reason": "Permission denied."}, {"code": "1405", "reason": "[{0}] Not Supported"}, {"code": "1450", "reason": "Parameter [{0}] error！"}, {"code": "1451", "reason": "The parameter [{0}] is not provided."}, {"code": "1452", "reason": "The parameter [{0}] format error."}, {"code": "1464", "reason": "The parameter [{0}] is too long."}, {"code": "1466", "reason": "Background business operation is in progress, please wait"}, {"code": "10001", "reason": "no data found:[{0}]"}, {"code": "10002", "reason": "data duplication:[{0}]"}, {"code": "10008", "reason": "database operate error:[{0}]"}]