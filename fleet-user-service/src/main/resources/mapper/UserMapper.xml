<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.fleet.user.mapper.UserMapper">

    <select id="search" resultType="com.chervon.fleet.user.api.entity.vo.FleetUserVo">
        select t1.id as userId,t1.email as userEmail,t1.first_name as userFirstName,t1.last_name as userLastName,t1.user_type as userRole,
               t1.company_id as companyId,t2.company_name as companyName,t1.source_type as userSourceType,t3.email as invitationEmail,
        case  when t1.is_deleted = 1 then  (4)
        else (t1.account_status) end as userState,
        t1.create_time as registerTime,t1.activation_time as activeTime,t1.is_enabled as isEnabled
        from t_user as t1
        left join t_company as t2 on t1.company_id = t2.id and t2.is_deleted = 0
        left join t_user as t3 on t1.parent_id = t3.id
        where 1 = 1
        <if test="search.registerStartTime != null">
            and t1.create_time &gt;= #{search.registerStartTime}
        </if>
        <if test="search.registerEndTime != null">
            and t1.create_time &lt;= #{search.registerEndTime}
        </if>
        <if test="search.activeStartTime != null">
            and t1.activation_time &gt;= #{search.activeStartTime}
        </if>
        <if test="search.activeEndTime != null">
            and t1.activation_time &lt;= #{search.activeEndTime}
        </if>
        <if test="search.userId != null and search.userId != ''">
            and t1.id like concat('%',#{search.userId},'%')
        </if>
        <if test="search.userEmail != null and search.userEmail != ''">
            and t1.email like concat('%',#{search.userEmail},'%')
        </if>
        <if test="search.userName != null and search.userName != ''">
            and t1.name like concat('%',#{search.userName},'%')
        </if>

        <if test="search.userRoles != null and search.userRoles.size() > 0">
            and t1.user_type in
            <foreach collection="search.userRoles" item="userRole" open="(" separator="," close=")">
                #{userRole}
            </foreach>
        </if>

        <if test="search.companyId != null and search.companyId != ''">
            and t1.company_id like concat('%',#{search.companyId},'%')
        </if>
        <if test="search.userSourceTypes != null and search.userSourceTypes.size() > 0">
            and t1.source_type in
            <foreach collection="search.userSourceTypes" item="userSourceType" open="(" separator="," close=")">
                #{userSourceType}
            </foreach>
        </if>
        <if test="search.invitationEmail != null and search.invitationEmail != ''">
            and t3.email like concat('%',#{search.invitationEmail},'%')
        </if>
        <if test="search.companyName != null and search.companyName != ''">
            and t2.company_name like concat('%',#{search.companyName},'%')
        </if>
        <if test="search.userStates != null and search.userStates.size() > 0">
            and (t1.is_deleted = 0 and t1.account_status in
            <foreach collection="search.userStates" item="userState" open="(" separator="," close=")">
                #{userState}
            </foreach>
            <if test="search.userStates.contains(&quot;4&quot;)">
                or t1.is_deleted = 1
            </if>
            )
        </if>
        <if test="search.isEnabled != null ">
            and t1.is_enabled = #{search.isEnabled}
        </if>
        order by t1.create_time desc
    </select>

    <select id="searchList" resultType="com.chervon.fleet.user.api.entity.vo.FleetUserVo">
        select
        t1.id as userId,
        t1.email as userEmail,
        t1.first_name as userFirstName,
        t1.last_name as userLastName,
        t1.user_type as userRole,
        t1.company_id as companyId,
        t2.company_name as companyName,
        t1.source_type as userSourceType,
        t3.email as invitationEmail,
        case
        when t1.is_deleted = 1 then
        (4)
        else
        (t1.account_status)
        end as userState,
        t1.create_time as registerTime,
        t1.activation_time as activeTime,
        t1.is_enabled as isEnabled
        from t_user as t1 left join t_company as t2 on t1.company_id = t2.id and t2.is_deleted = 0 left join t_user as
        t3 on t1.parent_id =
        t3.id
        where 1 = 1
        <if test="search.registerStartTime != null">
            and t1.create_time &gt;= #{search.registerStartTime}
        </if>
        <if test="search.registerEndTime != null">
            and t1.create_time &lt;= #{search.registerEndTime}
        </if>
        <if test="search.activeStartTime != null">
            and t1.activation_time &gt;= #{search.activeStartTime}
        </if>
        <if test="search.activeEndTime != null">
            and t1.activation_time &lt;= #{search.activeEndTime}
        </if>
        <if test="search.userId != null and search.userId != ''">
            and t1.id like concat('%',#{search.userId},'%')
        </if>
        <if test="search.userEmail != null and search.userEmail != ''">
            and t1.email like concat('%',#{search.userEmail},'%')
        </if>
        <if test="search.userName != null and search.userName != ''">
            and t1.name like concat('%',#{search.userName},'%')
        </if>

        <if test="search.userRoles != null and search.userRoles.size() > 0">
            and t1.user_type in
            <foreach collection="search.userRoles" item="userRole" open="(" separator="," close=")">
                #{userRole}
            </foreach>
        </if>

        <if test="search.companyId != null and search.companyId != ''">
            and t1.company_id like concat('%',#{search.companyId},'%')
        </if>
        <if test="search.userSourceTypes != null and search.userSourceTypes.size() > 0">
            and t1.source_type in
            <foreach collection="search.userSourceTypes" item="userSourceType" open="(" separator="," close=")">
                #{userSourceType}
            </foreach>
        </if>
        <if test="search.invitationEmail != null and search.invitationEmail != ''">
            and t3.email like concat('%',#{search.invitationEmail},'%')
        </if>
        <if test="search.companyName != null and search.companyName != ''">
            and t2.company_name like concat('%',#{search.companyName},'%')
        </if>
        <if test="search.userStates != null and search.userStates.size() > 0">
            and (t1.is_deleted = 0 and t1.account_status in
            <foreach collection="search.userStates" item="userState" open="(" separator="," close=")">
                #{userState}
            </foreach>
            <if test="search.userStates.contains(&quot;4&quot;)">
                or t1.is_deleted = 1
            </if>
            )
        </if>
        <if test="search.isEnabled != null and search.isEnabled != ''">
            and t1.is_enabled = #{search.isEnabled}
        </if>
        order by t1.create_time desc
    </select>

    <select id="selectListWithDeleted" resultType="com.chervon.fleet.user.entity.po.User">
        select *
        from t_user
    </select>

    <select id="selectOneWithDeleted" resultType="com.chervon.fleet.user.entity.po.User">
        select *
        from t_user
        where id = #{userId}
    </select>


</mapper>