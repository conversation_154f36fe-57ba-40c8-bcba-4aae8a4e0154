<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.chervon.fleet.user.mapper.CompanyMapper">

    <select id="search" resultType="com.chervon.fleet.user.api.entity.vo.FleetCompanyVo">
        select
        t1.id as companyId,
        t1.company_name as companyName,
        t1.user_id as adminId,
        t2.email as adminEmail,
        t2.first_name as adminFirstName,
        t2.last_name as adminLastName,
        t1.status as companyState,
        t1.create_time as registerTime
        from t_company as t1 left join t_user as t2 on t1.user_id = t2.id where t1.is_deleted = 0
        <if test="search.registerStartTime != null">
            and t1.create_time &gt;= #{search.registerStartTime}
        </if>
        <if test="search.registerEndTime != null">
            and t1.create_time &lt;= #{search.registerEndTime}
        </if>
        <if test="search.companyId != null and search.companyId != ''">
            and t1.id like concat('%',#{search.companyId},'%')
        </if>
        <if test="search.companyName != null and search.companyName != ''">
            and t1.company_name like concat('%',#{search.companyName},'%')
        </if>
        <if test="search.adminId != null and search.adminId != ''">
            and t1.user_id like concat('%',#{search.adminId},'%')
        </if>
        <if test="search.adminEmail != null and search.adminEmail != ''">
            and t2.email like concat('%',#{search.adminEmail},'%')
        </if>
        <if test="search.adminName != null and search.adminName != ''">
            and t2.name like concat('%',#{search.adminName},'%')
        </if>
        <if test="search.companyState != null and search.companyState.size() > 0">
            and t1.status in
            <foreach collection="search.companyState" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        order by t1.create_time desc
    </select>

    <select id="searchList" resultType="com.chervon.fleet.user.api.entity.vo.FleetCompanyVo">
        select
        t1.id as companyId,
        t1.company_name as companyName,
        t1.user_id as adminId,
        t2.email as adminEmail,
        t2.first_name as adminFirstName,
        t2.last_name as adminLastName,
        t1.status as companyState,
        t1.create_time as registerTime
        from t_company as t1 left join t_user as t2 on t1.user_id = t2.id where t1.is_deleted = 0
        <if test="search.registerStartTime != null">
            and t1.create_time &gt;= #{search.registerStartTime}
        </if>
        <if test="search.registerEndTime != null">
            and t1.create_time &lt;= #{search.registerEndTime}
        </if>
        <if test="search.companyId != null and search.companyId != ''">
            and t1.id like concat('%',#{search.companyId},'%')
        </if>
        <if test="search.companyName != null and search.companyName != ''">
            and t1.company_name like concat('%',#{search.companyName},'%')
        </if>
        <if test="search.adminId != null and search.adminId != ''">
            and t1.user_id like concat('%',#{search.adminId},'%')
        </if>
        <if test="search.adminEmail != null and search.adminEmail != ''">
            and t2.email like concat('%',#{search.adminEmail},'%')
        </if>
        <if test="search.adminName != null and search.adminName != ''">
            and t2.name like concat('%',#{search.adminName},'%')
        </if>
        <if test="search.companyState != null and search.companyState.size() > 0">
            and t1.status in
            <foreach collection="search.companyState" item="state" open="(" separator="," close=")">
                #{state}
            </foreach>
        </if>
        order by t1.create_time desc
    </select>


</mapper>