package com.chervon.fleet.user.controller;

import com.chervon.fleet.user.api.entity.dto.MailRequestDto;
import com.chervon.fleet.user.entity.po.Company;
import com.chervon.fleet.user.entity.po.RequestList;
import com.chervon.fleet.user.entity.po.Role;
import com.chervon.fleet.user.service.RequestListService;
import com.chervon.fleet.user.service.SendMailService;
import com.chervon.fleet.user.utils.RSAUtil;
import com.chervon.idgenerator.util.IdUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * test测试接口
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Api(tags = "test接口")
public class RequestController {

    @Autowired
    private RequestListService requestListService;
    @Autowired
    private SendMailService sendMailService;

    @ApiOperation("读取请求白名单列表")
    @GetMapping(value = "/requestList",produces = "application/json")
    public List<RequestList> requestList() {
        final List<RequestList> whitelist = requestListService.getWhitelist();
        return whitelist;
    }

    @ApiOperation("刷新请求名单列表")
    @GetMapping(value = "/refresh",produces = "application/json")
    public void refresh() {
        requestListService.refresh();
    }


    @ApiOperation("发送邮件测试接口")
    @GetMapping(value = "/sendMail",produces = "application/json")
    public String sendMail(@RequestParam("mail") String mail) {
        MailRequestDto mailRequestDto=new MailRequestDto();
        mailRequestDto.setSendTo(mail).setSubject("测试邮件标题")
                .setText("这是一封测试邮件,发送时间：" + LocalDateTime.now() +"收件人："+mail);
        return sendMailService.sendHtmlMail(mailRequestDto);
    }

    @ApiOperation("RSA密码加密明文，返回密文")
    @GetMapping(value = "/encrpt",produces = "application/json")
    public String encrypt(@RequestParam("password") String password) {
        return RSAUtil.encrypt(RSAUtil.PUBLIC_KEY,password);
    }

}
