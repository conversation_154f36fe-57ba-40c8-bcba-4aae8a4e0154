package com.chervon.fleet.user.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.common.core.domain.PageResult;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
public class PageResultUtils {
    /**
     * 转换mybatis plus page 2 PagedList
     *
     * @param page mybatis plus page
     * @param <E>  类型参数
     * @return 页面列表
     */
    public static <E> PageResult<E> page2PagedList(IPage<E> page) {
        List<E> list = null;
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            list = page.getRecords();
        }
        PageResult<E> resultPage = new PageResult<>();
        resultPage.setTotal((int) page.getTotal());
        resultPage.setPageNum((int) page.getCurrent());
        resultPage.setPageSize((int) page.getSize());
        resultPage.setList(list);

        return resultPage;
    }
}
