package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.fleet.user.api.entity.dto.UserRoleDto;
import com.chervon.fleet.user.api.entity.enums.UserTypeEnum;
import com.chervon.fleet.user.api.entity.query.ResourceQuery;
import com.chervon.fleet.user.api.entity.query.RoleQuery;
import com.chervon.fleet.user.entity.po.Role;
import com.chervon.fleet.user.entity.po.RoleResource;
import com.chervon.fleet.user.mapper.RoleMapper;
import com.chervon.fleet.user.service.ResourceService;
import com.chervon.fleet.user.service.RoleResourceService;
import com.chervon.fleet.user.service.RoleService;
import com.chervon.fleet.user.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 角色表服务接口实现
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    @Autowired
    RoleResourceService roleResourceService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    ResourceService resourceService;

    @Override
    public List<String> getRoleList(Long companyId) {
        List<Role> list = this.list(new QueryWrapper<Role>().select("distinct name")
                .lambda().eq(Role::getCompanyId, companyId).or().eq(Role::getRoleType, UserTypeEnum.ADMIN.getType())
                .orderByAsc(Role::getName));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(Role::getName).distinct().collect(Collectors.toList());
    }

    /**
     * 根据查询条件获取列表，不分页
     *
     * @param roleQuery 角色查询bean
     * @return
     */
    @Override
    public List<Role> getList(RoleQuery roleQuery) {
        return list(getQueryWrapper(roleQuery));
    }

    /**
     * * 验证角色名是否存在
     *
     * @param roleQuery
     * @return
     */
    @Override
    public Boolean existRole(RoleQuery roleQuery) {
        Wrapper<Role> queryWrapper=getQueryWrapper(roleQuery);
        return count(queryWrapper)>0?true:false;
    }

    /**
     * 保存角色到数据库
     *
     * @param userRoleDto dealerRoleDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Role saveRole(UserRoleDto userRoleDto) {
        //保存角色
        Role role = new Role();
        role.setName(userRoleDto.getName())
                .setCode(userRoleDto.getCode())
                .setRoleType(userRoleDto.getType())
                .setDescription(userRoleDto.getDescription())
                .setCompanyId(HeaderUtils.getCompanyId());
        boolean result = this.save(role);
        if (!result) {
            throw new ServiceException(ErrorCode.DB_OPERATION_ERROR);
        }
        //增加角色资源表
        if (!CollectionUtils.isEmpty(userRoleDto.getResourceIdList())) {
            roleResourceService.insertRoleResource(userRoleDto.getResourceIdList(), role);
        }
        return role;
    }

    /**
     * * 删除角色：验证角色关联用户，并删除角色下的关联关系
     * @param listRoleId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(List<Long> listRoleId) {
        if(CollectionUtils.isEmpty(listRoleId)){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"Role id");
        }
        // 校验改role type  0 不允许删除
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.in("id",listRoleId);
        List<Role> roles = list(wrapper);
        for (Role role : roles) {
            if (role.getRoleType() == UserTypeEnum.ADMIN.getType()) {
                throw new ServiceException(ErrorCode.PARAMETER_ERROR);
            }
        }
        final Integer count = userRoleService.countUserRole(listRoleId);
        if(count>0){
            throw new ServiceException(ErrorCode.PARAMETER_ERROR);
        }
        roleResourceService.deleteRoleResourceRef(listRoleId,null);
        final boolean result = removeByIds(listRoleId);
        return result;
    }


    /**
     * * 处理需要插入的角色资源关系
     * @param role
     * @param needInserts
     */
    public void insertRoleResource(Role role, List<Long> needInserts) {
        if(!CollectionUtils.isEmpty(needInserts)){
            ResourceQuery resourceQuery = new ResourceQuery();
            resourceQuery.setIds(needInserts);
            List<com.chervon.fleet.user.entity.po.Resource> dealerResourceList = resourceService.getResourceList(resourceQuery);
            if(CollectionUtils.isEmpty(dealerResourceList)){
                throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED);
            }
            List<RoleResource> roleResourceList=new ArrayList<>();
            for(com.chervon.fleet.user.entity.po.Resource resource:dealerResourceList){
                RoleResource roleResource = new RoleResource();
                roleResource
                        .setRoleId(role.getId())
                        .setResourceId(resource.getId());
                roleResourceList.add(roleResource);
            }
            roleResourceService.insertBatch(roleResourceList);
        }
    }


    private LambdaQueryWrapper<Role> getQueryWrapper(RoleQuery roleQuery) {
        return new LambdaQueryWrapper<Role>()
                .eq(roleQuery.getId() != null, Role::getId, roleQuery.getId())
                .eq(!StringUtils.isEmpty(roleQuery.getCode()), Role::getCode,roleQuery.getCode())
                .eq(!StringUtils.isEmpty(roleQuery.getName()), Role::getName, roleQuery.getName())
                .eq(!Objects.isNull(roleQuery.getType()), Role::getRoleType, roleQuery.getType())
                .eq(!Objects.isNull(roleQuery.getCompanyId()), Role::getCompanyId, roleQuery.getCompanyId())
                .in(!CollectionUtils.isEmpty(roleQuery.getRoleIdList()), Role::getId, roleQuery.getRoleIdList())
                ;
    }
}