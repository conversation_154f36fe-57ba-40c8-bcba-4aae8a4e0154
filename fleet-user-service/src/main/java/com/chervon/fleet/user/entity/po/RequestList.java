package com.chervon.fleet.user.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 请求黑白名单配置(t_request_list)实体类
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_request_list")
public class RequestList extends Model<RequestList> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 访问路径
     */
    private String path;
    /**
     * 匹配方法
     */
    private String matchMethod;
    /**
     * 限制类型（1.白名单，2黑名单）
     */
    private Integer type;
    /**
     * 备注
     */
    private String remark;

}