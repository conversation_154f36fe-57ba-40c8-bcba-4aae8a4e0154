package com.chervon.fleet.user.rpc;

import com.chervon.fleet.user.api.entity.dto.AppSettingDto;
import com.chervon.fleet.user.api.entity.vo.AppSettingVo;
import com.chervon.fleet.user.api.service.RemoteAppSettingService;
import com.chervon.fleet.user.service.AppSettingService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * fleet用户设置接口实现
 * <AUTHOR>
 * @since 2023-07-15 14:28:36
 * @description 
 */
@DubboService
@Service
public class RemoteAppSettingServiceImpl implements RemoteAppSettingService {

    @Autowired
    private AppSettingService appSettingService;

    /**
     * 用户登录接口 *
     *
     * @param requestDto
     * @return
     */
    @Override
    public void saveSetting(AppSettingDto requestDto) {
        appSettingService.saveSetting(requestDto);
    }

    /**
     * * 获取用户设置信息
     * @param userId
     * @return
     */
    @Override
    public AppSettingVo getUserSetting(Long userId) {
        return appSettingService.getUserSetting(userId);
    }
}