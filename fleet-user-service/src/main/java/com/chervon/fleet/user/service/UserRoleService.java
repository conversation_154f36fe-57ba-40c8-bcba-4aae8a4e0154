package com.chervon.fleet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.user.entity.po.UserRole;

import java.util.List;

/**
 * 用户角色关系表服务接口
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
public interface UserRoleService extends IService<UserRole> {
    /**
     * 根据UserId查询对应关系 *
     * @param userId
     * @return
     */
    List<UserRole> getUserRoleByUser(Long userId);
    /**
     * 统计角色下绑定的用户数量 *
     * @param listRoleIds
     * @return
     */
    Integer countUserRole(List<Long> listRoleIds);

}
