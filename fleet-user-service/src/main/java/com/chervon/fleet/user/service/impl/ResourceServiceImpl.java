package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.fleet.user.api.entity.enums.EnableEnum;
import com.chervon.fleet.user.api.entity.query.ResourceQuery;
import com.chervon.fleet.user.api.entity.vo.ResourceVo;
import com.chervon.fleet.user.entity.po.Resource;
import com.chervon.fleet.user.entity.po.RoleResource;
import com.chervon.fleet.user.mapper.ResourceMapper;
import com.chervon.fleet.user.service.ResourceService;
import com.chervon.fleet.user.service.RoleResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 资源表(菜单按钮)服务接口实现
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ResourceServiceImpl extends ServiceImpl<ResourceMapper, Resource> implements ResourceService {
    @Lazy
    @Autowired
    RoleResourceService roleResourceService;

    @Override
    public List<Resource> getResourceList(ResourceQuery query) {
        //角色查询参数
        appendRoleNameQueryParam(query);
        final Wrapper<Resource> queryWrapper = getQueryWrapper(query);
        return this.list(queryWrapper);
    }

    @Override
    public List<ResourceVo> getResourceVoList(ResourceQuery query) {
        final List<Resource> list = getResourceList(query);
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(list, ResourceVo.class);
    }


    /**
     * * 根据角色Id参数转换为资源Id列表参数
     * @param query
     */
    private void appendRoleNameQueryParam(ResourceQuery query){
        if(Objects.isNull(query.getRoleId()) || !CollectionUtils.isEmpty(query.getIds())){
            return;
        }
        LambdaQueryWrapper<RoleResource> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(!Objects.isNull(query.getRoleId()), RoleResource::getRoleId,query.getRoleId())
                .select(RoleResource::getResourceId);
        final List<RoleResource> listRoleResource = roleResourceService.list(queryWrapper);
        if(CollectionUtils.isEmpty(listRoleResource)){
            return;
        }
        final List<Long> ids = listRoleResource.stream().map(RoleResource::getResourceId).collect(Collectors.toList());
        query.setIds(ids);
    }

    private Wrapper<Resource> getQueryWrapper(ResourceQuery query){
        LambdaQueryWrapper<Resource> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(!Objects.isNull(query.getId()),Resource::getId,query.getId())
                .eq(Resource::getIsDeleted, EnableEnum.ENABLED.getType())
                .eq(!Objects.isNull(query.getApplication()),Resource::getApplication,query.getApplication())
                .eq(StringUtils.hasText(query.getCode()),Resource::getCode,query.getCode())
                .like(StringUtils.hasText(query.getName()),Resource::getName,query.getName())
                .in(!CollectionUtils.isEmpty(query.getIds()),Resource::getId,query.getIds());
        return queryWrapper;
    }
}