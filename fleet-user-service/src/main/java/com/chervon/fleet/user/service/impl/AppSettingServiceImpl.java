package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.user.api.entity.dto.AppSettingDto;
import com.chervon.fleet.user.api.entity.enums.DeviceTypeEnum;
import com.chervon.fleet.user.api.entity.vo.AppSettingVo;
import com.chervon.fleet.user.entity.po.AppSetting;
import com.chervon.fleet.user.mapper.AppSettingMapper;
import com.chervon.fleet.user.service.AppSettingService;
import com.chervon.operation.api.RemoteAppAgreementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-15 17:30:36
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppSettingServiceImpl extends ServiceImpl<AppSettingMapper, AppSetting> implements AppSettingService {
    /**
     * fleet app类型
     */
    private static final Integer FLEET_APP_TYPE=2;
    private static final Integer FLEET_WEB_TYPE=3;
    @DubboReference
    private RemoteAppAgreementService remoteAppAgreementService;

    /**
     * 用户登录接口 *
     *
     * @param requestDto
     * @return
     */
    @Override
    public void saveSetting(AppSettingDto requestDto) {
        final LambdaQueryWrapper<AppSetting> queryWrapper = new LambdaQueryWrapper<AppSetting>()
                .eq(AppSetting::getUserId, requestDto.getUserId());
        final List<AppSetting> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            //无配置信息则新建
            AppSetting appSetting = BeanCopyUtils.copy(requestDto, AppSetting.class);
            this.save(appSetting);
            return;
        }
        AppSetting appSetting = list.get(0);
        updateAppSetting(requestDto, appSetting);
    }

    private void updateAppSetting(AppSettingDto requestDto, AppSetting appSetting) {
        AppSetting newAppSetting = new AppSetting();
        newAppSetting.setId(appSetting.getId());
        newAppSetting.setCompanyId(requestDto.getCompanyId());
        final boolean flag = isExistUpdateCondition(requestDto, appSetting, newAppSetting);
        if (flag) {
            //更新配置信息
            this.updateById(newAppSetting);
        }
    }

    private static boolean isExistUpdateCondition(AppSettingDto requestDto, AppSetting appSetting, AppSetting newAppSetting) {
        boolean flag = false;
        if (!StringUtils.equals(appSetting.getLanguage(), requestDto.getLanguage())) {
            newAppSetting.setLanguage(requestDto.getLanguage());
            flag = true;
        }
        if (!Objects.equals(appSetting.getGatewayMode(), requestDto.getGatewayMode())) {
            newAppSetting.setGatewayMode(requestDto.getGatewayMode());
            flag = true;
        }
        if (!StringUtils.equals(appSetting.getAppUserAgreementVersion(), requestDto.getAppUserAgreementVersion())) {
            newAppSetting.setAppUserAgreementVersion(requestDto.getAppUserAgreementVersion());
            flag = true;
        }
        if (!StringUtils.equals(appSetting.getAppSecretAgreementVersion(), requestDto.getAppSecretAgreementVersion())) {
            newAppSetting.setAppSecretAgreementVersion(requestDto.getAppSecretAgreementVersion());
            flag = true;
        }
        if (!StringUtils.equals(appSetting.getWebUserAgreementVersion(), requestDto.getWebUserAgreementVersion())) {
            newAppSetting.setWebUserAgreementVersion(requestDto.getWebUserAgreementVersion());
            flag = true;
        }
        if (!StringUtils.equals(appSetting.getWebSecretAgreementVersion(), requestDto.getWebSecretAgreementVersion())) {
            newAppSetting.setWebSecretAgreementVersion(requestDto.getWebSecretAgreementVersion());
            flag = true;
        }
        return flag;
    }

    /**
     * * 获取用户设置信息
     *
     * @param userId
     * @return
     */
    @Override
    public AppSettingVo getUserSetting(Long userId) {
        final LambdaQueryWrapper<AppSetting> queryWrapper = new LambdaQueryWrapper<AppSetting>()
                .eq(AppSetting::getUserId, userId);
        final List<AppSetting> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return BeanCopyUtils.copy(list.get(0), AppSettingVo.class);
        }
    }

    @Override
    public void handleAgreementAfterLogin(DeviceTypeEnum deviceType, Long companyId, Long userId) {
        Integer useType = null;
        if (deviceType == DeviceTypeEnum.PC) {
            useType = FLEET_WEB_TYPE;
        } else {
            useType = FLEET_APP_TYPE;
        }
        AppSettingDto req = new AppSettingDto();
        req.setUserId(userId);
        req.setCompanyId(companyId);
        Map<String, String> latestVersion = remoteAppAgreementService.getLatestVersion(useType);
        if (deviceType == DeviceTypeEnum.PC) {
            req.setWebUserAgreementVersion(latestVersion.get("user"));
            req.setWebSecretAgreementVersion(latestVersion.get("secret"));
        } else {
            req.setAppUserAgreementVersion(latestVersion.get("user"));
            req.setAppSecretAgreementVersion(latestVersion.get("secret"));
        }
        this.saveSetting(req);
    }
}