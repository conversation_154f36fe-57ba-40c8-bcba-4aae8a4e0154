package com.chervon.fleet.user.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * (t_app_setting)实体类
 *
 * <AUTHOR>
 * @since 2023-07-15 17:30:36
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_app_setting")
public class AppSetting extends Model<AppSetting> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 租户id
     */
    private Long companyId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 默认语言
     */
    private String language;
    /**
     * 网关模式，1 正常模式  2 省电模式
     */
    private Integer gatewayMode;
    /**
     * app用户协议版本
     */
    private String appUserAgreementVersion;
    /**
     * app隐私协议版本
     */
    private String appSecretAgreementVersion;
    /**
     * web用户协议版本
     */
    private String webUserAgreementVersion;
    /**
     * web隐私协议版本
     */
    private String webSecretAgreementVersion;

}