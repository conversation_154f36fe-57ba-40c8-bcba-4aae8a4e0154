package com.chervon.fleet.user.service;


import com.chervon.fleet.user.api.entity.dto.MailRequestDto;
import com.chervon.fleet.user.api.entity.enums.MailBusinessEnum;

/**
 * 邮件发送服务
 *
 * <AUTHOR> 2023/7/4
 */
public interface SendMailService {

    /**
     * 验证邮箱Dto
     *
     * @param mailRequest 邮箱Dto
     */
    void validateMailRequestParam(MailRequestDto mailRequest);

    /**
     * 发送业务邮件
     *
     * @param email            邮箱
     * @param mailBusinessEnum 类型
     * @param firstName        姓名(可为null)
     * @param lastName         姓氏(可为null)
     * @param companyId        公司ID(可为null)
     * @param companyName      公司名(可为null)
     */
    void sendUserMailByBusiness(String email, MailBusinessEnum mailBusinessEnum,
                                String firstName, String lastName,
                                Long companyId, String companyName);

    /**
     * 发送用户角色更新后通知邮件
     *
     * @param email    邮箱
     * @param userType 用户类型(角色)
     */
    void sendUserRoleUpdateNoticeMail(String email, Integer userType, String name);

    /**
     * 简单文本邮件
     *
     * @param mailRequest 邮件Dto
     */
    void sendSimpleMail(MailRequestDto mailRequest);


    /**
     * Html格式邮件,可带附件
     *
     * @param mailRequest 邮件Dto
     */
    String sendHtmlMail(MailRequestDto mailRequest);

    /**
     * * 给dealer和staff发送邮件
     *
     * @param email 邮件
     */
    void sendMailByFileTemplate(String email);
}

