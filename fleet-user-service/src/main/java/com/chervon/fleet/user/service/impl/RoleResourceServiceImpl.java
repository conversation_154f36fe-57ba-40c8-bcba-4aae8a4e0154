package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.fleet.user.api.entity.query.ResourceQuery;
import com.chervon.fleet.user.api.entity.vo.ResourceVo;
import com.chervon.fleet.user.entity.po.Resource;
import com.chervon.fleet.user.entity.po.Role;
import com.chervon.fleet.user.entity.po.RoleResource;
import com.chervon.fleet.user.mapper.RoleResourceMapper;
import com.chervon.fleet.user.api.service.RemoteResourceService;
import com.chervon.fleet.user.service.ResourceService;
import com.chervon.fleet.user.service.RoleResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色资源表（权限表）服务接口实现
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RoleResourceServiceImpl extends ServiceImpl<RoleResourceMapper, RoleResource> implements RoleResourceService {
    @Autowired
    ResourceService resourceService;
    private static final Integer BATCH_NUM=500;

    @Override
    public List<Resource> getResourceByRoles(List<Long> roleIds){
        final LambdaQueryWrapper<RoleResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RoleResource::getRoleId,roleIds)
                .select(RoleResource::getResourceId);
        final List<RoleResource> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return Collections.emptyList();
        }
        final List<Long> listResourceIds = list.stream().map(a -> a.getResourceId()).collect(Collectors.toList());
        final ResourceQuery resourceQuery = new ResourceQuery();
        resourceQuery.setIds(listResourceIds);
        return resourceService.getResourceList(resourceQuery);
    }

    /**
     * 保存角色资源表
     * @param resourceIdList resourceIdList
     * @param role           role
     */
    @Override
    public void insertRoleResource(List<Long> resourceIdList, Role role) {
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return;
        }
        final LambdaQueryWrapper<Resource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Resource::getId,resourceIdList);
        List<Resource> resourceList = resourceService.list(queryWrapper);
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return;
        }
        List<RoleResource> roleResourceList = new ArrayList<RoleResource>(resourceList.size());
        for (Resource resource : resourceList) {
            RoleResource roleResource = new RoleResource();
            roleResource
                    .setRoleId(role.getId())
                    .setResourceId(resource.getId());
            roleResourceList.add(roleResource);
        }
        insertBatch(roleResourceList);
    }

    /**
     * * 删除角色资源关系
     * @param listRoleIds 角色Id列表
     * @param listResourceIds 资源Id列表
     * @return 删除条数
     */
    @Override
    public Integer deleteRoleResourceRef(List<Long> listRoleIds, List<Long> listResourceIds) {
        if(CollectionUtils.isEmpty(listRoleIds) && CollectionUtils.isEmpty(listResourceIds)){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"roleIds-resourceIds");
        }
        final LambdaQueryWrapper<RoleResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(listRoleIds),RoleResource::getRoleId,listRoleIds)
                .in(!CollectionUtils.isEmpty(listResourceIds),RoleResource::getResourceId,listResourceIds);
        return baseMapper.delete(queryWrapper);
    }

    /**
     * * 批量插入
     * @param list
     * @return
     */
    @Override
    public List<Long> insertBatch(List<RoleResource> list) {
        if (saveBatch(list, BATCH_NUM)) {
            return list.stream().map(RoleResource::getId).collect(Collectors.toList());
        }
        return null;
    }
}