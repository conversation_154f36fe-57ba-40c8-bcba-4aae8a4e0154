package com.chervon.fleet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.user.entity.po.RequestList;
import com.chervon.fleet.user.entity.query.RequestListQuery;

import java.io.Serializable;
import java.util.List;

/**
 * 访问名单配置 服务接口
 * <AUTHOR>
 */
public interface RequestListService extends IService<RequestList> {
    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return
     */
    boolean delete(Serializable id);

    /**
     * 根据主键查询实体信息
     * @param id 访问名单配置
     * @return
     */
    RequestList get(Serializable id);

    /**
     * 根据查询条件获取列表，不分页
     * @param requestListQuery 访问名单配置查询bean
     * @return
     */
    List<RequestList> getList(RequestListQuery requestListQuery);

    /**
     * * 请求黑白名单验证
     * @param method
     * @param url
     * @return
     */
    Boolean checkRequestList(String method, String url);

    /**
     * 获取白名单
     *
     * @return 访问名单配置列表
     */
    List<RequestList> getWhitelist();

    /**
     * 获取黑名单
     *
     * @return 访问名单配置列表
     */
    List<RequestList> getBlacklist();
    /**
     * * 校验黑名单
     * @param path
     * @param method
     * @return
     */
    boolean checkBlacklist(String path, String method);

    /**
     * 校验是否为白名单
     * @param path   请求路径
     * @param method 请求方法
     * @return 是否白名单
     */
    boolean checkWhitelist(String path, String method);

    /**
     * * 刷新缓存
     */
    void refresh();
}
