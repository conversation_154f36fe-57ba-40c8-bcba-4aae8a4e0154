package com.chervon.fleet.user.utils.jwt;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.enums.DeviceTypeEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.entity.consts.RedisConst;
import com.chervon.fleet.user.entity.dto.RedisToken;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.jwa.AlgorithmConstraints;
import org.jose4j.jws.AlgorithmIdentifiers;
import org.jose4j.jws.JsonWebSignature;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.MalformedClaimException;
import org.jose4j.jwt.NumericDate;
import org.jose4j.jwt.consumer.InvalidJwtException;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;
import org.jose4j.keys.HmacKey;
import org.jose4j.lang.JoseException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JWT验证工具
 *
 * <AUTHOR>
 * @date 2022/9/11
 */
@Slf4j
@Component
public class JwtUtils<T> {
    private static final Integer NUM_32 =32;
    /**
     * jwt 荷载 用户基础信息属性名称
     */
    public static final String USER_FIELD = "user";

    /**
     * 获取荷载属性
     *
     * @param jwt  JWT
     * @param name 荷载名称
     * @return 属性值
     */
    public static Object getProperty(String jwt, String secret, String name) throws MalformedClaimException {
        HmacKey key = buildKey(secret);
        JwtConsumer jwtConsumer = new JwtConsumerBuilder()
            .setRequireExpirationTime()
            .setAllowedClockSkewInSeconds(CommonConstant.TWO)
            .setVerificationKey(key)
            .setJwsAlgorithmConstraints(AlgorithmConstraints.ConstraintType.PERMIT, AlgorithmIdentifiers.HMAC_SHA256)
            .build();

        JwtClaims claims;
        try {
            claims = jwtConsumer.processToClaims(jwt);
        } catch (Exception e) {
            if (e instanceof InvalidJwtException && ((InvalidJwtException) e).hasExpired()) {
                log.error("JwtUtils#getProperty -> InvalidJwtException: {}", e.getMessage());
                throw new ServiceException(ErrorCode.UNAUTHORIZED);
            }
            log.error("JwtUtils#getProperty -> UnknownException: {}", e.getMessage());
            throw new ServiceException(ErrorCode.UNAUTHORIZED);
        }
        return claims.getClaimValue(name);
    }

    /**
     * 生成加密的KEY
     *
     * @param secret 盐值
     * @return HMAC 加密key
     */
    private static HmacKey buildKey(String secret) {
        byte[] keyBytes = new byte[NUM_32];
        byte[] bytes = DigestUtils.md5Digest(secret.getBytes());
        System.arraycopy(bytes, 0, keyBytes, 0, bytes.length);
        bytes = DigestUtils.md5Digest(bytes);
        System.arraycopy(bytes, 0, keyBytes, CommonConstant.SIXTEEN, bytes.length);
        return new HmacKey(keyBytes);
    }

    /**
     * 创建载荷对象
     *
     * @param payload 附加荷载
     * @return
     */
    public static JwtClaims buildClaims(Map<String, Object> payload, Integer deviceType) {
        JwtClaims claims = new JwtClaims();
        //设置过期时间
        NumericDate expirationTime = NumericDate.now();
        if (deviceType.equals(CommonConstant.ONE)) {
            // 1 PC
            expirationTime.addSeconds(RedisConst.FLEET_WEB_TOKEN_EXPIRES_SECOND);
        } else {
            // 2 ANDROID, 3 IOS
            expirationTime.addSeconds(RedisConst.FLEET_APP_TOKEN_EXPIRES_SECOND);
        }
        claims.setExpirationTime(expirationTime);
        //生成ID
        claims.setGeneratedJwtId();
        //jwt立即生效
        claims.setIssuedAtToNow();
        //存放载荷
        if (!CollectionUtils.isEmpty(payload)) {
            for (Map.Entry<String, Object> item : payload.entrySet()) {
                claims.setClaim(item.getKey(), item.getValue());
            }
        }
        return claims;
    }

    public static LoginResultVo getUserInfo(String token) {
        //用户参数验证
        Assert.hasText(token, ErrorCode.PARAMETER_NOT_PROVIDED, "token");
        Long uid = UserContext.getUserId();
        Assert.isId(uid, ErrorCode.PARAMETER_NOT_PROVIDED, "uid");
        final Integer deviceType = UserContext.getDeviceType();
        Assert.isIdInteger(deviceType, ErrorCode.PARAMETER_NOT_PROVIDED, "deviceType");
        String deviceId = UserContext.getDeviceId();
        //读取redis缓存token
        final String tokenKey = RedisConst.getFleetRedisTokenKey(deviceType, deviceId, uid);
        RedisToken redisToken = getRedisToken(tokenKey);
        LoginResultVo loginInfo = null;
        try {
            //通过jwt-token读取登录用户信息
            String strLoginInfo = (String) JwtUtils.getProperty(token, redisToken.getSecret(), JwtUtils.USER_FIELD);
            loginInfo = JsonUtils.toObject(strLoginInfo, LoginResultVo.class);
            if (Objects.isNull(loginInfo)) {
                throw new ServiceException(ErrorCode.UNAUTHORIZED);
            }
            RedisUtils.expire(tokenKey, getDeviceTokenExpireSecond(deviceType));
        } catch (MalformedClaimException e) {
            log.error("get login info by token error:{}", e);
            throw new ServiceException(UserErrorCodeEnum.TOKEN_VALIDATE_ERROR);
        }
        return loginInfo;
    }

    /**
     * * 根据设备类型读取过期时间设置
     *
     * @param deviceType
     * @return
     */
    public static long getDeviceTokenExpireSecond(Integer deviceType) {
        if (DeviceTypeEnum.ANDROID.getType() == deviceType
            || DeviceTypeEnum.IOS.getType() == deviceType) {
            return RedisConst.FLEET_APP_TOKEN_EXPIRES_SECOND;
        } else {
            return RedisConst.FLEET_WEB_TOKEN_EXPIRES_SECOND;
        }
    }

    public static RedisToken getRedisToken(String tokenKey) {
        final Object objSecret = RedisUtils.getCacheObject(tokenKey);
        if (Objects.isNull(objSecret)) {
            throw new ServiceException(ErrorCode.UNAUTHORIZED);
        }
        if (!(objSecret instanceof RedisToken)) {
            RedisUtils.deleteObject(tokenKey);
            throw new ServiceException(ErrorCode.UNAUTHORIZED);
        }
        RedisToken redisToken = (RedisToken) objSecret;
        return redisToken;
    }

    /**
     * * 记录token-redis缓存key
     *
     * @param userId 用户ID
     * @param secret MD5盐
     * @param token  jwtToken
     */
    public static void saveLoginRedisToken(Long userId, String secret, String token) {
        Integer deviceType = UserContext.getDeviceType();
        String deviceId = UserContext.getDeviceId();
        if (Objects.isNull(deviceType) || Objects.isNull(deviceId)) {
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED, "deviceId");
        }
        //app设备登录，token过期时间待定
        String key = RedisConst.getFleetRedisTokenKey(deviceType, deviceId, userId);
        RedisToken dpToken = new RedisToken()
            .setSecret(secret)
            .setTokenHash(token.hashCode());
        RedisUtils.setWithExpire(key, dpToken, getDeviceTokenExpireSecond(deviceType));
    }

    /**
     * 计算JWT
     *
     * @param secret     盐值
     * @param loginInfo  登录人的信息
     * @param deviceType DeviceTypeEnum
     * @return JWT
     */
    public String computeJwt(String secret, T loginInfo, Integer deviceType) {
        HmacKey key = buildKey(secret);
        //附加荷载
        Map<String, Object> payload = new ConcurrentHashMap<>();
        payload.put(USER_FIELD, JsonUtils.toJson(loginInfo));
        JwtClaims claims = buildClaims(payload, deviceType);
        JsonWebSignature jws = new JsonWebSignature();
        jws.setPayload(claims.toJson());
        jws.setKey(key);
        jws.setAlgorithmHeaderValue(AlgorithmIdentifiers.HMAC_SHA256);
        try {
            return jws.getCompactSerialization();
        } catch (JoseException e) {
            throw new ServiceException(UserErrorCodeEnum.TOKEN_COMPUTE_ERROR);
        }
    }
}
