package com.chervon.fleet.user.utils;

import com.chervon.fleet.user.entity.consts.NumberConst;

import java.time.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date :2022/12/15 14:19
 * @description :
 * @modyified By:
 */
public class DateUtil {
	/**
	 * 时间戳转成LocalDate
	 * @param time
	 * @return
	 */
	public static LocalDate longToLocalDate(Long time) {
		return Instant.ofEpochMilli(time).atZone(ZoneId.systemDefault()).toLocalDate();

	}
	/**
	 * LocalDate转时间戳
	 * @param date
	 * @return
	 */
	public static Long localDateToLong(LocalDate date) {
		return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
	}

	/**
	 * LocalDate转时间戳
	 * @param dateTime
	 * @return
	 */
	public static Long localDateTimeToLong(LocalDateTime dateTime) {
		return dateTime.toInstant(ZoneOffset.ofHours(NumberConst.TIME_ZONE_8)).toEpochMilli();
	}

	public static Date getDate(LocalDateTime localDateTime) {
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}
}