package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.fleet.user.entity.po.AdminTransferLog;
import com.chervon.fleet.user.mapper.AdminTransferLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.chervon.fleet.user.service.AdminTransferLogService;
import org.springframework.stereotype.Service;

/**
 * 管理员变更记录表服务接口实现
 *
 * <AUTHOR>
 * @since 2023-06-25 10:38:33
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AdminTransferLogServiceImpl extends ServiceImpl<AdminTransferLogMapper, AdminTransferLog> implements AdminTransferLogService {

}