package com.chervon.fleet.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.fleet.user.api.entity.dto.FleetUserPageDto;
import com.chervon.fleet.user.api.entity.vo.FleetUserVo;
import com.chervon.fleet.user.entity.po.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * fleet用户表(t_user)数据Mapper
 *
 * <AUTHOR>
 * @description
 * @since 2022-09-20 14:28:36
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 用户中心-查询用户信息
     *
     * @param page   分页容器
     * @param search 查询条件
     * @return 分页数据
     */
    IPage<FleetUserVo> search(IPage<FleetUserVo> page, @Param("search") FleetUserPageDto search);

    /**
     * 用户中心-查询用户信息
     *
     * @param search 查询条件
     * @return 分页数据
     */
    List<FleetUserVo> searchList(@Param("search") FleetUserPageDto search);

    /**
     * 查询所有用户信息，包含已经删除的
     *
     * @return 用户列表
     */
    List<User> selectListWithDeleted();

    /**
     * 根据用户id查询，包含已删除
     *
     * @param userId 用户id
     * @return 用户信息
     */
    User selectOneWithDeleted(@Param("userId") Long userId);
}
