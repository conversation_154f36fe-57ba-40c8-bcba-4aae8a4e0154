package com.chervon.fleet.user.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.user.api.entity.dto.UserRoleDto;
import com.chervon.fleet.user.api.entity.query.RoleQuery;
import com.chervon.fleet.user.entity.po.Role;
import java.util.List;

/**
 * 角色表服务接口
 * <AUTHOR>
 * @since 2023-07-20 14:28:36
 * @description 
 */
public interface RoleService extends IService<Role> {

    /**
     * PP:角色下拉框筛选
     * @return 名称list
     */
    List<String> getRoleList(Long dealerId);

    /**
     * 根据查询条件获取列表，不分页
     * @param roleQuery 角色查询bean
     * @return
     */
    List<Role> getList(RoleQuery roleQuery);

    /**
     * * 验证角色名是否存在
     * @param roleQuery
     * @return
     */
    Boolean existRole(RoleQuery roleQuery);

    /**
     * * 保存角色
     * @param userRoleDto
     * @return
     */
    Role saveRole(UserRoleDto userRoleDto);

    /**
     * * 删除角色
     * @param listRoleId
     * @return
     */
    boolean delete(List<Long> listRoleId);


    void insertRoleResource(Role role, List<Long> needInserts);
}
