package com.chervon.fleet.user.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 资源表(菜单按钮)(t_resource)实体类
 *
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_resource")
public class Resource extends Model<Resource> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 资源编码（资源标识）sys:user:info
     */
    private String code;
    /**
     * 资源名称
     */
    private String name;
    /**
     * 父资源id
     */
    private Long parentId;
    /**
     * 资源名称
     */
    private String parentName;
    /**
     * 所属系统应用: 1 fleet-app  2 fleet-web
     */
    private Integer application;
    /**
     * 资源类型：1-菜单 3-页面 5-页签 7-按钮
     */
    private Integer type;
    /**
     * 资源终端：1-pc，2-app
     */
    private Integer device;
    /**
     * 页面访问的uri
     */
    private String url;
    /**
     * 图标
     */
    private String icon;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 备注
     */
    private String description;

}