package com.chervon.fleet.user.entity.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 租户企业表(t_company)实体类
 *
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 * @description 
 */
@Data
@Accessors(chain = true)
@TableName("t_company")
public class Company extends Model<Company> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 管理员用户Id
     */
    private Long userId;
    /**
     * 是否启用 1：启用 0：停用
     */
    private Integer enabled;
    /**
     * 租户状态：1-正常、2-停用、4-已注销
     */
    private Integer status;

}