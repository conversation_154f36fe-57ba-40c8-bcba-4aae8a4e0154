package com.chervon.fleet.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * fleet用户表(t_user)实体类
 *
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_user")
public class User extends Model<User> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户密码（md5分对称）
     */
    private String password;
    /**
     * AES对称加密密码（AES）
     */
    private String aesPassword;
    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 全名
     */
    private String name;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 用户类型：1-超级管理员、2-管理员  3-staff员工
     */
    private Integer userType;
    /**
     * 公司id
     */
    private Long companyId;
    /**
     * 邀请人用户Id
     */
    private Long parentId;
    /**
     * 盐值密码
     */
    private String secret;
    /**
     * 账号状态：0未激活  1已激活（正常）
     */
    private Integer accountStatus;
    /**
     * 备注
     */
    private String description;
    /**
     * 邀请时间
     */
    private Long invitationTime;

    /**
     * 激活时间
     */
    private Long activationTime;
    /**
     * 账户来源
     */
    private Integer sourceType;
    /**
     * 头像颜色
     */
    private String avatarColor;
    /**
     * 启用状态：1 启用 0 停用
     */
    private Integer isEnabled;

}