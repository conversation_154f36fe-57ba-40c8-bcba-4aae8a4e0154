package com.chervon.fleet.user.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import java.time.LocalDate;

/**
 * 邮件验证码对象
 * <AUTHOR> 2023/7/4
 */
@Data
@AllArgsConstructor
public class RedisMailVerificationDto {
    private RedisMailVerificationDto(){}
    /**
     * 邮件验证码 *
     */
    private String code;

    public static RedisMailVerificationDto getInstance(){
        return new RedisMailVerificationDto("");
    }
}
