package com.chervon.fleet.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.user.api.entity.dto.FleetUserPageDto;
import com.chervon.fleet.user.api.entity.dto.UserEditDto;
import com.chervon.fleet.user.api.entity.query.UserPageQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.FleetUserVo;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.entity.vo.UserSyncVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.entity.po.User;
import java.util.List;

/**
 * fleet用户表服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-26 14:28:36
 */
public interface UserManageService extends IService<User> {

    /**
     * * 根据查询条件查询用户列表
     *
     * @param userPageQuery 查询类
     * @param companyId     公司ID
     * @return 分页结果
     */
    PageResult<UserVo> page(UserPageQuery userPageQuery, Long companyId);

    /**
     * * power-bi看板权限验证同步最近三天修改的用户信息
     *
     * @return
     */
    List<UserSyncVo> getUserSyncList();

    Boolean userExist(UserQuery userQuery);

    Boolean edit(UserEditDto userDto);

    Boolean delete(Long userId);

    /**********************用户中心************************/
    /**
     * 用户中心-查询用户信息
     *
     * @param page   分页容器
     * @param search 查询条件
     * @return 分页数据
     */
    IPage<FleetUserVo> search(IPage<FleetUserVo> page, FleetUserPageDto search);

    /**
     * 用户中心-查询用户信息
     *
     * @param search 查询条件
     * @return 分页数据
     */
    List<FleetUserVo> searchList(FleetUserPageDto search);

    /**
     * 根据userId获取登录信息,会查询company信息
     *
     * @param userId 用户ID
     * @return 登录信息
     */
    LoginResultVo getLoginResultVoById(Long userId);

    /**
     * 编辑密码后推出当前设备所有登录
     *
     * @param userId 用户ID
     */
    void logoutAllDevicesByUserId(Long userId);

    /**
     * 根据用户id查询，包含已删除的
     *
     * @param userId 用户id
     * @return 用户信息
     */
    User getOneWithDeleted(Long userId);

    /**
     * 游离用户
     *
     * @param userId 用户id
     */
    void freeUser(Long userId);

    /**
     * 检查密码格式
     * @param input
     * @return
     */
    boolean checkPasswordFormat(String input);
}
