package com.chervon.fleet.user.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.chervon.fleet.user.api.entity.enums.OperateLogTypeEnum;
import com.chervon.fleet.user.utils.OperationLogUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 用户操作日志表(t_log_operate)实体类
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_log_operate")
public class LogOperate extends Model<LogOperate> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 真实姓名
     */
    private String realName;
    /**
     * 组织机构
     */
    private String regionName;
    /**
     * 访问ip地址
     */
    private String ipAddress;
    /**
     * 操作类型：待产品定义
     */
    private Integer operateType;
    /**
     * 操作标题：xxx操作角色add
     */
    private String operateTitle;
    /**
     * 操作的页面资源名称：如：Role management-edit
     */
    private String operateResource;

    public LogOperate setOperateEnum(OperateLogTypeEnum operateEnum){
        this.setOperateType(operateEnum.getType()).setOperateTitle(operateEnum.getDesc());
        return this;
    }

    public void build(){
        OperationLogUtils.append(this);
    }
}