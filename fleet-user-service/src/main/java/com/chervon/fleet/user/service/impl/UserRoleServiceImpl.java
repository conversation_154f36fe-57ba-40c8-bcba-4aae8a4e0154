package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.fleet.user.api.entity.query.RoleQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.entity.po.Role;
import com.chervon.fleet.user.entity.po.UserRole;
import com.chervon.fleet.user.mapper.UserRoleMapper;
import com.chervon.fleet.user.service.RoleService;
import com.chervon.fleet.user.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户角色关系表服务接口实现
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements UserRoleService {

    @Autowired
    RoleService roleService;

    @Override
    public List<UserRole> getUserRoleByUser(Long userId){
        final RoleQuery roleQuery = new RoleQuery();
        roleQuery.setUserId(userId);
        final Wrapper<UserRole> queryWrapper = getQueryWrapper(roleQuery);
        final List<UserRole> userRoleList = list(queryWrapper);
        return userRoleList;
    }

    @Override
    public Integer countUserRole(List<Long> listRoleIds) {
        final LambdaQueryWrapper<UserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserRole::getRoleId,listRoleIds);
        return (int)count(queryWrapper);
    }

    private Wrapper<UserRole> getQueryWrapper(RoleQuery query) {
        return new LambdaQueryWrapper<UserRole>()
                .eq(query.getId()!=null,UserRole::getRoleId,query.getId())
                .eq(query.getUserId()!=null,UserRole::getUserId,query.getUserId())
                .in(!CollectionUtils.isEmpty(query.getRoleIdList()),UserRole::getRoleId,query.getRoleIdList())
                .in(!CollectionUtils.isEmpty(query.getUserIdList()),UserRole::getUserId,query.getUserIdList());
    }

}