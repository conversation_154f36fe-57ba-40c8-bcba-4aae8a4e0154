package com.chervon.fleet.user.rpc;

import com.chervon.fleet.user.api.entity.dto.MailRequestDto;
import com.chervon.fleet.user.api.entity.enums.MailBusinessEnum;
import com.chervon.fleet.user.api.service.RemoteSendMailService;
import com.chervon.fleet.user.service.SendMailService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * rpc邮件发送服务
 * 忘记密码
 * 用户注册
 * 邀请用户
 * <AUTHOR> 2023/7/4
 */
@Service
@DubboService
public class RemoteSendMailServiceImpl implements RemoteSendMailService {
    @Autowired
    private SendMailService sendMailService;

    @Override
    public void validateMailRequestParam(MailRequestDto mailRequest) {
        sendMailService.validateMailRequestParam(mailRequest);
    }

    @Override
    public void sendUserRegisterHtml(String email) {
        sendMailService.sendUserMailByBusiness(email, MailBusinessEnum.USER_REGISTER, null ,null, null, null);
    }

    @Override
    public void sendForgetPasswordHtml(String email, String firstName, String lastName) {
        sendMailService.sendUserMailByBusiness(email, MailBusinessEnum.FORGET_PASSWORD, firstName, lastName, null, null);
    }


    //*******************************************发送邮件接口******************************************************

    @Override
    public void sendSimpleMail(MailRequestDto mailRequest) {
        sendMailService.sendSimpleMail(mailRequest);
    }

    @Override
    public String sendHtmlMail(MailRequestDto mailRequest) {
        return sendMailService.sendHtmlMail(mailRequest);
    }

    @Override
    public void sendMailAfterAddDealer(String email) {
        sendMailService.sendMailByFileTemplate(email);
    }
}
