package com.chervon.fleet.user.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.user.api.entity.dto.AppSettingDto;
import com.chervon.fleet.user.api.entity.enums.DeviceTypeEnum;
import com.chervon.fleet.user.api.entity.vo.AppSettingVo;
import com.chervon.fleet.user.entity.po.AppSetting;
import org.springframework.scheduling.annotation.Async;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-07-15 17:30:36
 */
public interface AppSettingService extends IService<AppSetting> {
    /**
     * 保存用户设置
     *
     * @param requestDto
     * @return
     */
    void saveSetting(AppSettingDto requestDto);

    /**
     * * 获取用户设置信息
     *
     * @param userId
     * @return
     */
    AppSettingVo getUserSetting(Long userId);

    /**
     * 登录成功后异步处理协议
     *
     * @param deviceType 前端类型，app，web
     * @param companyId  租户id
     * @param userId     用户id
     */
    @Async
    void handleAgreementAfterLogin(DeviceTypeEnum deviceType, Long companyId, Long userId);
}
