package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.SpringUtils;
import com.chervon.fleet.user.api.entity.dto.CompanyDto;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.enums.*;
import com.chervon.fleet.user.api.entity.error.CompanyErrorCodeEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.CompanyQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyVo;
import com.chervon.fleet.user.entity.po.Company;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.mapper.CompanyMapper;
import com.chervon.fleet.user.service.CompanyService;
import com.chervon.fleet.user.service.UserService;
import com.chervon.idgenerator.util.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 租户表服务接口实现
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 */
@Slf4j
@Service
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, Company> implements CompanyService {

    private static UserService userService = SpringUtils.getBean(UserService.class);


    /**
     * 公司信息分页查询
     *
     * @param query 查询条件
     * @return 经销商分页信息
     */
    @Override
    public PageResult<CompanyVo> getPagedList(CompanyQuery query) {
        if (StringUtils.hasText(query.getEmail()) || StringUtils.hasText(query.getName())) {
            UserQuery userQuery = new UserQuery();
            //根据邮箱查询
            if (StringUtils.hasText(query.getEmail())) {
                userQuery.setEmail(query.getEmail());
            }
            //根据姓名查询
            if (StringUtils.hasText(query.getName())) {
                userQuery.setName(query.getName());
            }
            //邮箱和用户查询转换为userId查询
            final List<User> listUser = userService.getList(userQuery);
            if (!CollectionUtils.isEmpty(listUser)) {
                final List<Long> userIds = listUser.stream().map(User::getId).collect(Collectors.toList());
                query.setListUserId(userIds);
            }
        }

        //分页查询
        IPage<Company> page = new Page<>(query.getPageNum(), query.getPageSize());
        LambdaQueryWrapper<Company> queryWrapper = getWrapper(query);
        queryWrapper.orderByAsc(Company::getCreateTime);
        page = this.page(page, queryWrapper);
        //分页结果
        PageResult<CompanyVo> pageResult = new PageResult<>();
        pageResult.setTotal(page.getTotal());
        pageResult.setPageSize(page.getSize());
        pageResult.setPageNum(page.getCurrent());
        List<Company> queryList = page.getRecords();
        if (CollectionUtils.isEmpty(queryList)) {
            pageResult.setList(new ArrayList<>());
            return pageResult;
        }
        final List<CompanyVo> companyVos = BeanCopyUtils.copyList(page.getRecords(), CompanyVo.class);
        pageResult.setList(companyVos);
        return pageResult;
    }

    /**
     * 根据Id获取租户信息
     *
     * @param id 租户主键id
     * @return 租户信息
     */
    @Override
    public CompanyVo getDetail(Long id) {
        Company company = this.getOne(new QueryWrapper<Company>().lambda()
                .eq(Company::getId, id)
                .eq(Company::getEnabled, EnableEnum.ENABLED.getType()));
        if (Objects.isNull(company)) {
            return null;
        }
        return BeanCopyUtils.copy(company, CompanyVo.class);
    }

    /**
     * 根据名称查询租户信息
     *
     * @param name 租户名称
     * @return 企业信息
     */
    @Override
    public CompanyVo getCompanyByName(String name) {
        final List<Company> companyList = this.list(new QueryWrapper<Company>().lambda()
                .eq(Company::getCompanyName, name)
                .eq(Company::getEnabled, EnableEnum.ENABLED.getType()));
        if (CollectionUtils.isEmpty(companyList)) {
            return null;
        }
        return BeanCopyUtils.copy(companyList.get(0), CompanyVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(String companyName, Long userId) {
        final CompanyVo companyVo = getCompanyByName(companyName);
        if (!Objects.isNull(companyVo)) {
            log.error("CompanyService#add -> 要编辑的名称已存在: {}", companyName);
            throw new ServiceException(CompanyErrorCodeEnum.COMPANY_NAME_ALREADY_EXISTED);
        }
        Company company = new Company()
                .setCompanyName(companyName)
                .setEnabled(EnableEnum.ENABLED.getType())
                .setStatus(CompanyStatusEnum.NORMAL.getType())
                .setUserId(userId)
                .setId(IdUtils.snowFlakeNext());
        //保存数据库
        save(company);

        if (null != userId) {
            User user = userService.getById(userId);
            if (null == user) {
                throw new ServiceException(UserErrorCodeEnum.USER_NOT_EXIST);
            }
            if (user.getCompanyId() != null) {
                log.error("要绑定的用户({})已经绑定过企业了", userId);
                throw new ServiceException(CompanyErrorCodeEnum.USER_ALREADY_BOUND_COMPANY);
            }
            user.setCompanyId(company.getId());
            // 给当前操作用户赋予admin角色
            user.setUserType(UserTypeEnum.ADMIN.getType());
            user.setAccountStatus(AccountStatusEnum.ACTIVATED.getType());
            user.setActivationTime(System.currentTimeMillis());
            user.setIsEnabled(UserEnabledEnum.ACTIVE.getType());
            userService.updateById(user);
        }
        return company.getId();
    }

    /**
     * 租户信息新增
     *
     * @param companyDto companyDto
     * @return 新增结果
     */
    @Override
    public boolean edit(CompanyDto companyDto) {
        Assert.isId(companyDto.getId(), ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        Company company = getById(companyDto.getId());
        if (Objects.isNull(company)) {
            throw new ServiceException(ErrorCode.DATA_NOT_FOUND,"companyId");
        }
        if (companyDto.getUserId() != null && companyDto.getUserId() > 0L) {
            company.setUserId(companyDto.getUserId());
        }
        if (StringUtils.hasText(companyDto.getCompanyName())) {
            company.setCompanyName(companyDto.getCompanyName());
            final CompanyVo companyVo = getCompanyByName(companyDto.getCompanyName());
            if (!Objects.isNull(companyVo)) {
                log.error("CompanyService#edit -> 要编辑的名称已存在: {}", companyDto.getCompanyName());
                throw new ServiceException(CompanyErrorCodeEnum.COMPANY_NAME_ALREADY_EXISTED);
            }
        }
        if (companyDto.getEnabled() != null) {
            company.setEnabled(companyDto.getEnabled());
        }
        if (companyDto.getStatus() != null && companyDto.getStatus() > 0L) {
            company.setStatus(companyDto.getStatus());
        }
        //更新数据库
        return updateById(company);
    }

    @Override
    public IPage<FleetCompanyVo> search(IPage<FleetCompanyVo> page, FleetCompanyPageDto search) {
        return this.getBaseMapper().search(page, search);
    }

    @Override
    public List<FleetCompanyVo> searchList(FleetCompanyPageDto search) {
        return this.getBaseMapper().searchList(search);
    }

    /**
     * * 拼接综合查询条件
     *
     * @param query 查询条件类
     * @return 条件构造器
     */
    private static LambdaQueryWrapper<Company> getWrapper(CompanyQuery query) {
        LambdaQueryWrapper<Company> queryWrapper = new QueryWrapper<Company>().lambda();
        queryWrapper.like(StringUtils.hasText(query.getCompanyName()), Company::getCompanyName, query.getCompanyName())
                .eq(query.getUserId() != null, Company::getUserId, query.getUserId())
                .eq(query.getEnabled() != null, Company::getEnabled, EnableEnum.ENABLED.getType())
                .eq(query.getStatus() != null, Company::getStatus, query.getStatus())
                .ge(query.getBeginTime() != null, Company::getCreateTime, query.getBeginTime())
                .lt(query.getEndTime() != null, Company::getCreateTime, query.getEndTime())
                .in(!CollectionUtils.isEmpty(query.getListUserId()), Company::getUserId, query.getListUserId());
        return queryWrapper;
    }
}
