package com.chervon.fleet.user.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.AesUtils;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.dto.*;
import com.chervon.fleet.user.api.entity.enums.*;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.UserRegisterActiveCheckVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.entity.consts.RedisConst;
import com.chervon.fleet.user.entity.consts.StringConst;
import com.chervon.fleet.user.entity.po.AdminTransferLog;
import com.chervon.fleet.user.entity.po.Company;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.mapper.UserMapper;
import com.chervon.fleet.user.service.*;
import com.chervon.fleet.user.utils.RSAUtil;
import com.chervon.fleet.user.utils.SecurityUtil;
import com.chervon.fleet.user.utils.jwt.JwtUtils;
import com.chervon.idgenerator.util.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * fleet用户表服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2022-09-20 14:28:36
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    private static final Integer REGISTER_SEND_MAIL_CHECK=1;
    private static final Integer REGISTER_CHECK=2;
    @Autowired
    RoleService roleService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    SendMailService sendMailService;
    @Autowired
    JwtUtils jwtUtils;
    @Autowired
    ResourceService resourceService;
    @Autowired
    UserManageService userManageService;
    @Autowired
    RoleResourceService roleResourceService;
    @Autowired
    AdminTransferLogService adminTransferLogService;
    @Lazy
    @Autowired
    private CompanyService companyService;

    @Override
    public Boolean forgetPasswordCheck(ForgetPasswordRequestDto forgetPasswordRequestDto) {
        //读取邮箱验证码缓存对象
        return verificationCheck(forgetPasswordRequestDto.getEmail(), MailBusinessEnum.FORGET_PASSWORD, true, forgetPasswordRequestDto.getVerificationCode(), null);
    }

    /**
     * 注册发送邮件
     * @param email 邮件
     */
    @Override
    public void registerSendMail(String email) {
        //验证用户是否存在,存在则提示用户
        checkUserHadRegister(null, email,REGISTER_SEND_MAIL_CHECK);
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock("register_" + email);
        if (!lock.isLocked()) {
            lock.lock(CommonConstant.TEN, TimeUnit.SECONDS);
            try {
                //验证通过发送邮件验证码
                sendMailService.sendUserMailByBusiness(email, MailBusinessEnum.USER_REGISTER, null, null, null, null);
            } finally {
                lock.unlock();
            }
        } else {
            log.error("registerSendMail -> 频繁请求触发分布式锁,email:{}", email);
            throw new ServiceException(UserErrorCodeEnum.MAIL_SERVER_SLOW);
        }
    }

    /**
     * 注册用户-2：注册管理员用户点击超链接跳转验证身份
     *
     * @param userRegisterCheckDto 验证请求对象
     * @return
     */
    @Override
    public Boolean registerCheck(UserRegisterCheckDto userRegisterCheckDto) {

        return verificationCheck(userRegisterCheckDto.getEmail(), MailBusinessEnum.USER_REGISTER, true, userRegisterCheckDto.getVerificationCode(), null);
    }

    /**
     * 邀请员工注册2：点击链接进行激活验证码过期验证
     *
     * @param dto 验证请求对象
     * @return
     */
    @Override
    public UserRegisterActiveCheckVo userActivityCheck(UserRegisterCheckDto dto) {
        verificationCheck(dto.getEmail(), MailBusinessEnum.USER_INVITATION, true, dto.getVerificationCode(), dto.getCompanyId());
        List<User> userInDbList = this.list(new LambdaQueryWrapper<User>().eq(User::getEmail, dto.getEmail()));

        if (CollectionUtils.isEmpty(userInDbList)) {
            throw new ServiceException(UserErrorCodeEnum.USER_NOT_EXIST);
        }
        boolean activatedInAnotherCompany = userInDbList.stream().anyMatch(user -> user.getAccountStatus() == AccountStatusEnum.ACTIVATED.getType()
                && !Objects.isNull(user.getCompanyId()) && !Objects.equals(user.getCompanyId(), dto.getCompanyId()));
        if (activatedInAnotherCompany) {
            // 用户已被其他公司邀请
            throw new ServiceException(UserErrorCodeEnum.ACTIVATION_FAILED_ALREADY_AN_EMPLOYEE_OF_ANOTHER_COMPANY);
        }
        User user = userInDbList.stream().filter(u -> Objects.equals(u.getCompanyId(), dto.getCompanyId())).findFirst().orElse(null);
        if (user == null) {
            throw new ServiceException(UserErrorCodeEnum.USER_NOT_EXIST);
        }
        if (user.getAccountStatus() == AccountStatusEnum.ACTIVATED.getType()) {
            throw new ServiceException(UserErrorCodeEnum.ACTIVATION_FAILED_ALREADY_ACTIVATED);
        }
        UserRegisterActiveCheckVo activeCheckVo=new UserRegisterActiveCheckVo();
        activeCheckVo.setUserId(user.getId());
        activeCheckVo.setHadPassword(StringUtils.hasText(user.getPassword()));
        return activeCheckVo;
    }

    /**
     * 根据AES加密后密码,解析出新的MD5密码+AES密码,并赋值给user对象
     *
     * @param aesEncryptedPwd AES加密后密码
     * @return 携带password, secret, aesPassword属性的user对象
     */
    public User getMd5PwdAndAesPwd(String aesEncryptedPwd) {
        //解密前端传过来的加密密码,通过指定的密钥解密密码密文
        String decryptPwd = RSAUtil.decrypt(aesEncryptedPwd);
        if (!StringUtils.hasText(decryptPwd)) {
            throw new ServiceException(UserErrorCodeEnum.NEW_PASSWORD_RULE_ERROR);
        }
        //校验密码是否符合规则
        if (!userManageService.checkPasswordFormat(decryptPwd)) {
            throw new ServiceException(UserErrorCodeEnum.PASSWORD_RULE);
        }
        //生成加密密码
        final Tuple2<String, String> tuple2 = SecurityUtil.buildMd5Password(decryptPwd);
        String aesPassword = AesUtils.encrypt(decryptPwd, tuple2.getT2());
        User result = new User();
        result.setPassword(tuple2.getT1()).setSecret(tuple2.getT2()).setAesPassword(aesPassword);
        return result;
    }

    /**
     * 注册用户-3：注册创建管理员账户
     *
     * @param registerDto Dto
     * @return 用户信息
     */
    @Override
    public UserVo register(UserRegisterDto registerDto) {
        // 验证验证码是否过期
        verificationCheck(registerDto.getEmail(), MailBusinessEnum.USER_REGISTER, false, null, null);
        //验证用户是否已经注册
        checkUserHadRegister(null, registerDto.getEmail(),REGISTER_CHECK);
        //userActiveCheck(null, registerDto.getEmail());//前面已验证无需再验证
        //构造数据库用户对象
        User user = BeanCopyUtils.copyProperties(registerDto, User.class);
        // 根据DTO中AES加密后的密码,获取md5密码,盐以及aes密码
        User md5PwdAndAesPwd = getMd5PwdAndAesPwd(registerDto.getPassword());
        //构造保存对象
        user.setId(IdUtils.snowFlakeNext())
                .setPassword(md5PwdAndAesPwd.getPassword())
                .setSecret(md5PwdAndAesPwd.getSecret())
                .setAesPassword(md5PwdAndAesPwd.getAesPassword())
                .setUserName(registerDto.getEmail())
                .setEmail(registerDto.getEmail())
                .setName(getName(registerDto.getFirstName(),registerDto.getLastName()))
                .setParentId(0L)
                .setAccountStatus(AccountStatusEnum.NOT_ACTIVATED.getType())
                .setDescription("user register")
                .setIsDeleted(DeleteStatusEnum.NORMAL.getType())
                .setSourceType(UserSourceTypeEnum.REGISTER_BY_SELF.getType())
                .setAvatarColor(AvatarColorEnum.randomColor());
        boolean success = save(user);
        if (!success) {
            throw new ServiceException(ErrorCode.DB_OPERATION_ERROR);
        }
//        // 将此邮箱的其他记录的用户密码相关的设置为null
//        this.update(new User(), new LambdaUpdateWrapper<User>()
//                .set(User::getPassword, null).set(User::getSecret, null).set(User::getAesPassword, null)
//                .eq(User::getUserName, user.getUserName()).ne(User::getId, user.getId()));
        // 失效验证码
        RedisUtils.deleteObject(getVerificationRedisKey(user.getEmail(), MailBusinessEnum.USER_REGISTER, null));
        return BeanCopyUtils.copy(user, UserVo.class);
    }


    /**
     * 邀请员工注册3：激活员工账户(邀请员工后)
     *
     * @param activityDto
     * @return
     */
    @Override
    public void activityUser(UserActivityDto activityDto) {
        //读取用户信息
        User user = getById(activityDto.getUserId());
        //验证用户状态是否是未激活
        if (user.getAccountStatus().equals(AccountStatusEnum.ACTIVATED.getType())) {
            throw new ServiceException(UserErrorCodeEnum.USER_ALREADY_ACTIVITY);
        }
        if(StringUtils.isEmpty(user.getPassword()) && StringUtils.isEmpty(activityDto.getPassword())){
            throw new ServiceException(ErrorCode.PARAMETER_NOT_PROVIDED,"password");
        }
        // 检验用户是否已在其它公司注册过
        userActiveCheck(user.getCompanyId(), user.getEmail());
        // 验证邀请码是否过期
        verificationCheck(user.getEmail(), MailBusinessEnum.USER_INVITATION, false, null, user.getCompanyId());
        //构造更新对象
        user.setAccountStatus(AccountStatusEnum.ACTIVATED.getType())
                .setDescription("invited activity")
                .setActivationTime(System.currentTimeMillis())
                // 激活的账户为启用状态，防止之前停用过改用户
                .setIsEnabled(UserEnabledEnum.ACTIVE.getType());
        if(!StringUtils.isEmpty(activityDto.getPassword())) {
            // 根据DTO中AES加密后的密码,获取md5密码,盐以及aes密码
            User md5PwdAndAesPwd = getMd5PwdAndAesPwd(activityDto.getPassword());
            user.setPassword(md5PwdAndAesPwd.getPassword())
                 .setSecret(md5PwdAndAesPwd.getSecret())
                 .setAesPassword(md5PwdAndAesPwd.getAesPassword());
        }
        //更新数据库
        boolean success = updateById(user);
        if (!success) {
            throw new ServiceException(ErrorCode.DB_OPERATION_ERROR);
        }
        // 失效验证码
        RedisUtils.deleteObject(getVerificationRedisKey(user.getEmail(), MailBusinessEnum.USER_INVITATION, user.getCompanyId()));
    }

    /**
     * 邀请注册接口检测用户是否已被邀请
     *
     * @param email     邮箱
     * @param companyId 公司ID
     */
    private void inviteRegisterCheck(String email, Long companyId) {
        final List<User> userInDbList = getUserExist(email);
        if (userInDbList == null) {
            return;
        }
        for (User userInDb : userInDbList) {
            //先验证已激活，直接给与提示
            if (userInDb.getAccountStatus().intValue() == AccountStatusEnum.ACTIVATED.getType()) {
                checkActivated(companyId, userInDb);
            }//是当前用户租户下的未激活，验证邀请是否已过期
            else if (userInDb.getAccountStatus().intValue() == AccountStatusEnum.NOT_ACTIVATED.getType()) {
                checkNotActivated(email, companyId, userInDb);
                //非本租户未激活，无需验证
            }else{
                //用户已注销,无需验证
            }
        }
    }

    private static void checkNotActivated(String email, Long companyId, User userInDb) {
        //本租户下邀请，且未激活，提示：An invitation was sent. You can resend the invitation via "invite Again".
        if (Objects.equals(userInDb.getCompanyId(), companyId)) {
            //用户已被邀请，但未激活，查看邀请是否已过期
            String prevVerificationCode = RedisUtils.getCacheObject(RedisConst.getInviteUserMailCodeKey(email, companyId));
            if (StringUtils.hasText(prevVerificationCode)) {
                throw new ServiceException(UserErrorCodeEnum.INVITATION_WITHIN_72_HOURS);
            }else{
                throw new ServiceException(UserErrorCodeEnum.USER_ALREADY_SEND_INVITATION);
            }
        }
    }

    private static void checkActivated(Long companyId, User userInDb) {
        if (Objects.equals(userInDb.getCompanyId(), companyId)) {
            // 用户已在邀请人公司内且已激活
            throw new ServiceException(UserErrorCodeEnum.USER_ALREADY_AN_EMPLOYEE_OF_YOUR_COMPANY);
        } else {
            // 用户已被其他公司邀请并激活
            throw new ServiceException(UserErrorCodeEnum.USER_ALREADY_AN_EMPLOYEE_OF_ANOTHER_COMPANY);
        }
    }

    private List<User> getFreeUserExist(String email) {
        List<User> userFreeList = this.list(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email)
                .isNull(User::getCompanyId)
                .isNotNull(User::getPassword)
                .eq(User::getAccountStatus,AccountStatusEnum.NOT_ACTIVATED.getType())
                .orderByAsc(User::getCreateTime));
        return userFreeList;
    }

    @Nullable
    private List<User> getUserExist(String email) {
        List<User> userInDbList = this.list(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email)
                .isNotNull(User::getCompanyId)
                .orderByDesc(User::getAccountStatus));
        if (CollectionUtils.isEmpty(userInDbList)) {
            //未被邀请过或注册过，无需验证
            return null;
        }
        return userInDbList;
    }

    @Override
    public void checkUserHadRegister(Long companyId, String email,Integer checkFlag) {
        // 按照规则一个email同时只能有一个激活状态的用户
        List<User> listUser = this.list(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email)
                .in(User::getAccountStatus, Arrays.asList(AccountStatusEnum.ACTIVATED.getType(), AccountStatusEnum.NOT_ACTIVATED.getType())));
        if (CollectionUtils.isEmpty(listUser)) {
            return;
        }
        // 有被其他租户邀请但是都未激活的，可以注册
        if (listUser.stream().allMatch(user -> user.getAccountStatus() == AccountStatusEnum.NOT_ACTIVATED.getType()
                && user.getParentId() != null && user.getParentId() > 0L && user.getInvitationTime() != null)) {
            log.info("有被其他租户邀请但是未激活的，可以注册");
            return;
        }
        if(listUser.stream().anyMatch(user -> user.getAccountStatus() == AccountStatusEnum.ACTIVATED.getType()
                && user.getCompanyId() != null && !user.getCompanyId().equals(companyId))){
            // 该用户已在其他公司注册并激活
            log.info("该用户({})已在其他公司注册并激活");
            if(checkFlag.equals(REGISTER_CHECK)){
                throw new ServiceException(UserErrorCodeEnum.ACTIVATION_FAILED_ALREADY_AN_EMPLOYEE_OF_ANOTHER_COMPANY);
            }
        }
        throw new ServiceException(UserErrorCodeEnum.EMAIL_REGISTERED);
    }

    private void userActiveCheck(Long companyId, String email) {
        // 按照规则一个email同时只能有一个激活状态的用户
        User user = this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email)
                .eq(User::getAccountStatus, AccountStatusEnum.ACTIVATED.getType())
                // 排除相同邮箱自行注册的账户
                .isNotNull(User::getCompanyId)
                .last(StringConst.SQL_LIMIT));
        if (user != null && !Objects.equals(user.getCompanyId(), companyId)) {
            // 该用户已在其他公司注册并激活
            throw new ServiceException(UserErrorCodeEnum.ACTIVATION_FAILED_ALREADY_AN_EMPLOYEE_OF_ANOTHER_COMPANY);
        }
    }

    private static String getName(String firstName,String lastName){
        return firstName.concat(" ").concat(lastName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean inviteRegister(UserRegisterDto registerDto) {
        //验证租户下是否用户已经注册过
        final Long companyId = UserContext.getCompanyId();
        //邀请人用户id
        Long inviteUserId = UserContext.getUserId();
        Assert.isId(companyId, ErrorCode.PARAMETER_NOT_PROVIDED, "companyId");
        Assert.isId(inviteUserId, ErrorCode.PARAMETER_NOT_PROVIDED, "inviteUserId");
        // 角色不允许为1
        if (registerDto.getUserType().intValue() == UserTypeEnum.ADMIN.getType()) {
            throw new ServiceException(UserErrorCodeEnum.INVITED_USER_CANT_BE_ADMIN);
        }
        //验证租户下是否用户已经注册激活；按照规则一个email同时只能有一个已激活状态的用户
        inviteRegisterCheck(registerDto.getEmail(), companyId);
        final List<User> freeUserList = getFreeUserExist(registerDto.getEmail());
        User user = null;
        boolean isUpdate= false;
        if(CollectionUtils.isEmpty(freeUserList)){
            user = BeanCopyUtils.copyProperties(registerDto, User.class);
            //构造数据库用户对象
            user.setId(IdUtil.getSnowflakeNextId())
                    .setUserName(registerDto.getEmail())
                    .setEmail(registerDto.getEmail())
                    .setName(getName(registerDto.getFirstName(),registerDto.getLastName()))
                    .setUserType(registerDto.getUserType())
                    .setCompanyId(companyId)
                    //邀请人
                    .setParentId(inviteUserId)
                    //被邀请，未激活
                    .setAccountStatus(AccountStatusEnum.NOT_ACTIVATED.getType())
                    .setInvitationTime(System.currentTimeMillis())
                    .setDescription("invite user")
                    .setIsDeleted(DeleteStatusEnum.NORMAL.getType())
                    .setCreator(UserContext.getName())
                    .setModifier(UserContext.getName())
                    .setCreateTime(System.currentTimeMillis())
                    .setModifyTime(System.currentTimeMillis())
                    .setSourceType(UserSourceTypeEnum.INVITED.getType())
                    .setAvatarColor(AvatarColorEnum.randomColor());
        }else {
            user = freeUserList.get(0);
            isUpdate = true;
            user.setName(getName(registerDto.getFirstName(), registerDto.getLastName()))
                 .setUserType(registerDto.getUserType())
                 .setCompanyId(companyId)
                 //邀请人
                 .setParentId(inviteUserId)
                 //被邀请，未激活
                 .setAccountStatus(AccountStatusEnum.NOT_ACTIVATED.getType())
                 .setInvitationTime(System.currentTimeMillis())
                 .setDescription("invite user")
                 .setIsDeleted(DeleteStatusEnum.NORMAL.getType())
                 .setModifier(UserContext.getName())
                 .setModifyTime(System.currentTimeMillis())
                 .setSourceType(UserSourceTypeEnum.INVITED.getType());
        }
        //保存用户
        log.info("insert invite user. user->{}", JsonUtils.toJson(user));
        boolean success = isUpdate ? updateById(user) : save(user);
        //发送邀请邮件
        Company company = Optional.ofNullable(companyService.getById(companyId)).orElse(new Company());
        sendMailService.sendUserMailByBusiness(registerDto.getEmail(), MailBusinessEnum.USER_INVITATION,
                registerDto.getFirstName(), registerDto.getLastName(), companyId, company.getCompanyName());
        return success;
    }

    @Override
    public void inviteRegisterAgain(Long id) {
        User user = this.getById(id);
        if (null == user) {
            throw new ServiceException(UserErrorCodeEnum.USER_NOT_EXIST);
        }
        // 检查之前的验证码是否已过期
        final String inviteUserMailCodeKey = RedisConst.getInviteUserMailCodeKey(user.getEmail(), user.getCompanyId());
        String prevVerificationCode = RedisUtils.getCacheObject(inviteUserMailCodeKey);
        if (StringUtils.hasText(prevVerificationCode)) {
            throw new ServiceException(UserErrorCodeEnum.PREVIOUS_VERIFICATION_NOT_EXPIRE);
        }
        Company company = Optional.ofNullable(companyService.getById(user.getCompanyId())).orElse(new Company());
        sendMailService.sendUserMailByBusiness(user.getEmail(), MailBusinessEnum.USER_INVITATION,
                user.getFirstName(), user.getLastName(), user.getCompanyId(), company.getCompanyName());
    }

    /**
     * 验证邮箱验证码
     *
     * @param email                     邮箱
     * @param businessEnum              邮件类型枚举
     * @param linkVerificationCode      验证码
     * @param ifCheckVerificationEquals 是否检测验证码内容,邀请注册3接口不需要验证内容,只需要验证过期
     * @param companyId                 公司ID,仅用户邀请需要,否则传null
     * @return 验证是否成功
     */
    private Boolean verificationCheck(String email, MailBusinessEnum businessEnum, Boolean ifCheckVerificationEquals, String linkVerificationCode, Long companyId) {
        //读取邮箱验证码缓存对象
        String key = getVerificationRedisKey(email, businessEnum, companyId);
        final String verificationCode = RedisUtils.getCacheObject(key);
        if (StrUtil.isBlank(verificationCode) || (ifCheckVerificationEquals && !Objects.equals(verificationCode, linkVerificationCode))) {
            switch (businessEnum) {
                case USER_REGISTER: {
                    throw new ServiceException(UserErrorCodeEnum.USER_REGISTER_VERIFICATION_ERROR);
                }
                case USER_INVITATION: {
                    throw new ServiceException(UserErrorCodeEnum.USER_INVITATION_VERIFICATION_ERROR);
                }
                case FORGET_PASSWORD: {
                    throw new ServiceException(UserErrorCodeEnum.FORGET_PASSWORD_VERIFICATION_ERROR);
                }
                default: {
                    //如果验证码不一致，提示错误
                    throw new ServiceException(UserErrorCodeEnum.VERIFICATION_ERROR);
                }
            }
        }
        //验证验证码是否与用户输入验证码一致
        return true;
    }

    /**
     * 获取邮箱验证码在Redis的key
     *
     * @param email        邮箱
     * @param businessEnum 邮件类型枚举
     * @param companyId    公司ID,仅用户邀请需要,否则传null
     * @return 邮箱验证码在Redis的key
     */
    private String getVerificationRedisKey(String email, MailBusinessEnum businessEnum, Long companyId) {
        String key;
        if (businessEnum == MailBusinessEnum.USER_INVITATION) {
            key = RedisConst.getInviteUserMailCodeKey(email, companyId);
        } else {
            key = RedisConst.getUserMailCodeKey(email, businessEnum.name());
        }
        return key;
    }

    @Override
    public boolean forgetPasswordSetNewPassword(ForgetPasswordRequestDto requestDto) {
        //读取请求参数验证
        String verificationCode = requestDto.getVerificationCode();
        String newPassword = requestDto.getNewPassword();
        String email = requestDto.getEmail();
        Assert.hasText(email, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.EMAIL);
        Assert.hasText(verificationCode, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.VERIFICATION_CODE);
        Assert.hasText(newPassword, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.NEW_PASSWORD);
        //验证码验证是否正确
        String forgetMailCodeKey = RedisConst.getUserMailCodeKey(email, MailBusinessEnum.FORGET_PASSWORD.name());
        final String verificationCodeInRedis = RedisUtils.getCacheObject(forgetMailCodeKey);
        if (StrUtil.isBlank(verificationCodeInRedis) || !verificationCode.equals(verificationCodeInRedis)) {
            throw new ServiceException(UserErrorCodeEnum.FORGET_PASSWORD_VERIFICATION_ERROR);
        }
        //根据邮箱读取用户信息
        final User user = getValidUser(new UserQuery().setEmail(requestDto.getEmail()));
        if(Objects.isNull(user)){
            throw new ServiceException(UserErrorCodeEnum.DATA_NULL);
        }
        // 根据DTO中AES加密后的密码,获取md5密码,盐以及aes密码
        User updateUser = getMd5PwdAndAesPwd(requestDto.getNewPassword());
        updateUser.setId(user.getId());
        boolean result = this.updateById(updateUser);
        if (!result) {
            throw new ServiceException(ErrorCode.DB_OPERATION_ERROR);
        }
        //删除验证码缓存
        RedisUtils.deleteObject(forgetMailCodeKey);
        // 推出当前用户登录的所有账号
        userManageService.logoutAllDevicesByUserId(user.getId());
        return true;
    }

    @Override
    public User getValidUser(UserQuery userQuery) {
        userQuery.setIsEnabled(EnableEnum.ENABLED.getType());
        final List<User> userList = getList(userQuery);
        if (CollectionUtils.isEmpty(userList)) {
            return null;
        }
        //获取已激活有效用户
        Optional<User> optionalUser = userList.stream().filter(a -> a.getAccountStatus().equals(AccountStatusEnum.ACTIVATED.getType())).findFirst();
        if (optionalUser.isPresent()) {
            return optionalUser.get();
        }
        //获取未激活，游离账户
        optionalUser = userList.stream().filter(a -> a.getAccountStatus().equals(AccountStatusEnum.NOT_ACTIVATED.getType())
                && a.getCompanyId() == null).findFirst();
        if (optionalUser.isPresent()) {
            return optionalUser.get();
        }
        return null;
    }

    @Override
    public List<User> getList(UserQuery userQuery) {
        LambdaQueryWrapper<User> queryWrapper = getQueryWrapper(userQuery);
        return list(queryWrapper);
    }

    @Override
    public Boolean modifyPasswordCheck(ModifyPasswordDto modifyPasswordDto) {
        //根据用户id读取用户信息
        User fleetUser = getById(modifyPasswordDto.getUserId());
        //验证用户是否存在
        Assert.notNull(fleetUser, UserErrorCodeEnum.USER_NOT_EXIST);
        //解密原密码
        String decryptPwd = RSAUtil.decrypt(modifyPasswordDto.getOldPassword());
        //构造出数据库中加盐的密码
        String dbPassword = SecurityUtil.getDbMd5Password(decryptPwd, fleetUser.getSecret());
        //和数据库密码进行比对是否密码一致
        Assert.isTrue(fleetUser.getPassword().equals(dbPassword), UserErrorCodeEnum.OLD_PASSWORD_NOT_RIGHT);
        return true;
    }

    @Override
    public Boolean modifyPassword(ModifyPasswordDto modifyPasswordDto) {
        //读取用户信息
        User fleetUser = this.getById(modifyPasswordDto.getUserId());
        //验证用户是否存在
        Assert.notNull(fleetUser, UserErrorCodeEnum.USER_NOT_EXIST);
        //解密原密码
        String decryptPwd = RSAUtil.decrypt(modifyPasswordDto.getOldPassword());
        //构造出数据库中加盐的密码
        String dbPassword = SecurityUtil.getDbMd5Password(decryptPwd, fleetUser.getSecret());
        //和数据库密码进行比对是否密码一致
        Assert.isTrue(fleetUser.getPassword().equals(dbPassword), UserErrorCodeEnum.OLD_PASSWORD_NOT_RIGHT);
        // 根据DTO中AES加密后的密码,获取md5密码,盐以及aes密码
        User md5PwdAndAesPwd = getMd5PwdAndAesPwd(modifyPasswordDto.getNewPassword());
        //更新数据库
        fleetUser.setPassword(md5PwdAndAesPwd.getPassword())
                .setSecret(md5PwdAndAesPwd.getSecret())
                .setAesPassword(md5PwdAndAesPwd.getAesPassword());
        boolean result = updateById(fleetUser);
        if (result) {
            // 推出当前用户登录的所有账号
            userManageService.logoutAllDevicesByUserId(fleetUser.getId());
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean transferAdmin(TransferAdminDto request) {
        //读取转移操作人用户信息
        Long sourceUserId = UserContext.getUserId();
        User sourceUser = getById(sourceUserId);
        if (sourceUser.getUserType() != UserTypeEnum.ADMIN.getType()) {
            throw new ServiceException(UserErrorCodeEnum.USER_TYPE_NOT_ADMIN);
        }
        //验证转移目标用户
        Long targetUserId = request.getTargetUserId();
        User targetUser = getById(targetUserId);
        if (Objects.isNull(targetUser)) {
            throw new ServiceException(UserErrorCodeEnum.DATA_NULL);
        }
        //转移人和目标人不能相同
        if (sourceUser.getId().equals(targetUserId)) {
            throw new ServiceException(UserErrorCodeEnum.ADMIN_TRANSFER_TO_SELF);
        }
        //租户必须相同
        if (!sourceUser.getCompanyId().equals(targetUser.getCompanyId())) {
            throw new ServiceException(UserErrorCodeEnum.ADMIN_TRANSFER_TO_OTHER_COMPANY);
        }

        sourceUser.setUserType(UserTypeEnum.MANAGER.getType());
        this.updateById(sourceUser);

        targetUser.setUserType(UserTypeEnum.ADMIN.getType());
        this.updateById(targetUser);

        // 更新company表user_id字段
        Company company = companyService.getById(sourceUser.getCompanyId());
        company.setUserId(targetUserId);
        companyService.updateById(company);

        // 异步发送用户角色变更邮箱
        sendMailService.sendUserRoleUpdateNoticeMail(targetUser.getEmail(), UserTypeEnum.ADMIN.getType(), targetUser.getName());

        //统计租户下是否已有转移记录：如果没有，追加源和目标两条日志；如果之前已有转移记录，只记录目标转移日志；
        final long count = adminTransferLogService.count(new LambdaQueryWrapper<AdminTransferLog>()
                .eq(AdminTransferLog::getCompanyId, sourceUser.getCompanyId())
                .select());

        List<AdminTransferLog> logs = new ArrayList<>();
        Long modifyTime = System.currentTimeMillis();
        if (count == 0) {
            //记录原admin的用户记录
            logs.add(new AdminTransferLog()
                    .setId(IdUtil.getSnowflakeNextId())
                    .setUserId(sourceUser.getId())
                    .setUserName(sourceUser.getUserName())
                    .setUserFirstName(sourceUser.getFirstName())
                    .setUserLastName(sourceUser.getLastName())
                    .setEmail(sourceUser.getEmail())
                    .setCompanyId(sourceUser.getCompanyId())
                    .setModifyTime(modifyTime));
        }
        //记录被转移的目标用户记录
        logs.add(new AdminTransferLog()
                .setId(IdUtil.getSnowflakeNextId())
                .setUserId(targetUser.getId())
                .setUserName(targetUser.getUserName())
                .setUserFirstName(targetUser.getFirstName())
                .setUserLastName(targetUser.getLastName())
                .setEmail(targetUser.getEmail())
                .setCompanyId(targetUser.getCompanyId())
                .setModifyTime(modifyTime));
        boolean saveBatch = adminTransferLogService.saveBatch(logs);
        if (saveBatch) {
            // 强制退出
            userManageService.logoutAllDevicesByUserId(sourceUser.getId());
            userManageService.logoutAllDevicesByUserId(targetUser.getId());
        }
        return saveBatch;
    }


    private LambdaQueryWrapper<User> getQueryWrapper(UserQuery userQuery) {
        return new LambdaQueryWrapper<User>()
                .eq(userQuery.getId() != null, User::getId, userQuery.getId())
                .eq(userQuery.getUserName() != null, User::getUserName, userQuery.getUserName())
                .like(userQuery.getName() != null, User::getName, userQuery.getName())
                .eq(userQuery.getCompanyId() != null && userQuery.getCompanyId() != 0L, User::getCompanyId, userQuery.getCompanyId())
                .eq(userQuery.getParentId() != null, User::getParentId, userQuery.getParentId())
                .in(!CollectionUtils.isEmpty(userQuery.getUserTypeList()), User::getUserType, userQuery.getUserTypeList())
                .in(!CollectionUtils.isEmpty(userQuery.getListAccountStatus()), User::getAccountStatus, userQuery.getListAccountStatus())
                .eq(userQuery.getAccountStatus() != null, User::getAccountStatus, userQuery.getAccountStatus())
                .eq(StringUtils.hasText(userQuery.getEmail()), User::getEmail, userQuery.getEmail())
                .ge(userQuery.getBeginTime() != null, User::getCreateTime, userQuery.getBeginTime())
                .lt(userQuery.getEndTime() != null, User::getCreateTime, userQuery.getEndTime())
                .ge(userQuery.getModifyBeginTime() != null, User::getModifyTime, userQuery.getModifyBeginTime())
                .lt(userQuery.getModifyEndTime() != null, User::getModifyTime, userQuery.getModifyEndTime())
                .in(!CollectionUtils.isEmpty(userQuery.getIdList()), User::getId, userQuery.getIdList())
                .eq(userQuery.getIsEnabled() != null, User::getIsEnabled, userQuery.getIsEnabled());
    }
}
