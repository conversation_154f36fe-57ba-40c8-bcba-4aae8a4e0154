package com.chervon.fleet.user.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.common.core.domain.PageResult;
import com.chervon.fleet.user.api.entity.dto.CompanyDto;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.query.CompanyQuery;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyVo;
import com.chervon.fleet.user.entity.po.Company;

import java.util.List;

/**
 * 租户表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-20 14:28:36
 */
public interface CompanyService extends IService<Company> {

    /**
     * 租户信息分页查询
     *
     * @param query 查询条件
     * @return 经销商分页信息
     */
    PageResult<CompanyVo> getPagedList(CompanyQuery query);

    /**
     * 租户详情查询
     *
     * @param id 主键ID
     * @return 租户详情
     */
    CompanyVo getDetail(Long id);

    /**
     * 根据名称查询租户信息
     *
     * @param name 租户名称
     * @return 企业信息
     */
    CompanyVo getCompanyByName(String name);

    /**
     * 租户信息新增,更新用户表对应记录的company_id字段
     *
     * @param companyName 租户名称
     * @param userId      用户ID
     * @return companyId
     */
    Long add(String companyName, Long userId);

    /**
     * * 租户编辑
     *
     * @param companyDto 编辑对象
     * @return 编辑结果
     */
    boolean edit(CompanyDto companyDto);

    /***********************用户中心***************************/
    /**
     * 用户中心-查询租户信息
     *
     * @param page   分页容器
     * @param search 查询条件
     * @return 分页数据
     */
    IPage<FleetCompanyVo> search(IPage<FleetCompanyVo> page, FleetCompanyPageDto search);

    /**
     * 用户中心-查询租户信息
     *
     * @param search 查询条件
     * @return 分页数据
     */
    List<FleetCompanyVo> searchList(FleetCompanyPageDto search);

}
