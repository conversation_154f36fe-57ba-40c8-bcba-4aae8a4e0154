package com.chervon.fleet.user.entity.consts;

import java.text.MessageFormat;

/**
 * <AUTHOR> 2022/10/17
 */
public class StringConst {

    public static final String EMAIL_FORMAT="^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$";

    /**
     * 邮件标题国际化资源Id 占位符：业务名称
     */
    public static final String EMAIL_TITLE_ID="EMAIL_SUBJECT_{0}";
    public static final String SQL_LIMIT = "limit 1";

    public static String getEmailTitleResourceId(String businessType){
        return MessageFormat.format(EMAIL_TITLE_ID,businessType);
    }

    /**
     * 以下故障码提示语常量声明
     */
    public static final String ID="id";
    public static final String UID="uid";
    public static final String USER_ID="userId";
    public static final String LANGUAGE="language";
    public static final String COMPANY_ID="companyId";
    public static final String COMPANY_NAME = "companyName";
    public static final String DEVICE_ID="deviceId";
    public static final String REQUEST_DTO="requestDto";
    public static final String EMAIL="email";
    public static final String FIRST_NAME="firstName";
    public static final String LAST_NAME="lastName";
    public static final String PASSWORD="password";
    public static final String START_TIME="startTime";
    public static final String NEW_PASSWORD="newPassword";
    public static final String OLD_PASSWORD="oldPassword";
    public static final String END_TIME="endTime";
    public static final String VERIFICATION_CODE="verificationCode";
    public static final String MAINTENANCE_LOG_ID="maintenanceLogId";
}