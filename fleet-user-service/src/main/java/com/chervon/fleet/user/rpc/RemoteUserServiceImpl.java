package com.chervon.fleet.user.rpc;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.dto.*;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import com.chervon.fleet.user.api.entity.enums.EnableEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.UserPageQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.entity.vo.UserRegisterActiveCheckVo;
import com.chervon.fleet.user.api.entity.vo.UserSyncVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.api.service.RemoteUserService;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.service.*;
import com.chervon.fleet.user.utils.jwt.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * fleet用户表服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2022-09-20 14:28:36
 */
@Slf4j
@DubboService
@Service
public class RemoteUserServiceImpl implements RemoteUserService {
    @Autowired
    RoleService roleService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    ResourceService resourceService;
    @Autowired
    SendMailService sendMailService;
    @Autowired
    JwtUtils jwtUtils;
    @Autowired
    UserService userService;
    @Autowired
    UserManageService userManageService;
    @Autowired
    RequestListService requestListService;

    @Override
    public List<UserVo> getList(UserQuery userQuery) {
        final List<User> userList = userService.getList(userQuery);
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyList(userList, UserVo.class);
    }

    public UserVo getValidUser(UserQuery userQuery) {
        final User validUser = userService.getValidUser(userQuery);
        if(Objects.isNull(validUser)){
            return null;
        }
        return BeanCopyUtils.copy(validUser, UserVo.class);
    }

    /**
     * * power-bi看板权限验证同步最近三天修改的用户信息
     *
     * @return
     */
    @Override
    public List<UserSyncVo> getUserSyncList() {
        return userManageService.getUserSyncList();
    }

    @Override
    public PageResult<UserVo> page(UserPageQuery userPageQuery, Long companyId) {
        return userManageService.page(userPageQuery, companyId);
    }

    @Override
    public UserVo getDetail(UserQuery userQuery) {
        userQuery.setCompanyId(UserContext.getCompanyId());
        final List<User> userList = userService.getList(userQuery);
        if (CollectionUtils.isEmpty(userList)) {
            return null;
        }
        UserVo res = new UserVo();
        BeanUtils.copyProperties(userList.get(0), res);
        if (userList.get(0).getParentId() != null) {
            User user = userService.getById(userList.get(0).getParentId());
            if (user != null) {
                res.setInviterAddress(user.getEmail());
            }
        }
        return res;
    }

    @Override
    public Boolean userExist(UserQuery userQuery) {
        userQuery.setCompanyId(UserContext.getCompanyId());
        return userManageService.userExist(userQuery);
    }

    @Override
    public Boolean modifyPasswordCheck(ModifyPasswordDto modifyPasswordDto) {
        return userService.modifyPasswordCheck(modifyPasswordDto);
    }

    @Override
    public Boolean modifyPassword(ModifyPasswordDto modifyPasswordDto) {
        Long userId = UserContext.getUserId();
        if (!modifyPasswordDto.getUserId().equals(userId)) {
            throw new ServiceException(UserErrorCodeEnum.ILLEGAL_GET_USER_PERMISSIONS);
        }
        return userService.modifyPassword(modifyPasswordDto);
    }

    @Override
    public Boolean forgetPasswordCheck(ForgetPasswordRequestDto forgetPasswordRequestDto) {
        return userService.forgetPasswordCheck(forgetPasswordRequestDto);
    }

    @Override
    public Boolean forgetPasswordSetNewPassword(ForgetPasswordRequestDto forgetPasswordRequestDto) {
        return userService.forgetPasswordSetNewPassword(forgetPasswordRequestDto);
    }

    @Override
    public UserVo register(UserRegisterDto registerDto) {
        return userService.register(registerDto);
    }

    @Override
    public void activityUser(UserActivityDto activityDto) {
        userService.activityUser(activityDto);
    }

    @Override
    public Boolean inviteRegister(UserRegisterDto registerDto) {
        return userService.inviteRegister(registerDto);
    }

    @Override
    public void inviteRegisterAgain(Long id) {
        userService.inviteRegisterAgain(id);
    }

    @Override
    public Boolean registerCheck(UserRegisterCheckDto userRegisterCheckDto) {
        return userService.registerCheck(userRegisterCheckDto);
    }

    @Override
    public UserRegisterActiveCheckVo activityCheck(UserRegisterCheckDto userActivityCheckDto) {
        return userService.userActivityCheck(userActivityCheckDto);
    }

    @Override
    public Boolean edit(UserEditDto userDto) {
        return userManageService.edit(userDto);
    }

    /**
     * 逻辑删除用户
     * @param userId
     * @return
     */
    @Override
    public Boolean delete(Long userId) {
        return userManageService.delete(userId);
    }
    /**
     * 用户转为游离状态（解除租户绑定关系和未激活状态）
     * @param userId 用户id
     */
    @Override
    public void freeUser(Long userId) {
        userManageService.freeUser(userId);
    }
    @Override
    public Boolean transferAdmin(TransferAdminDto request) {
        return userService.transferAdmin(request);
    }


    @Override
    public void registerSendMail(String email) {
        userService.registerSendMail(email);
    }

    @Override
    public LoginResultVo getLoginResultVoById(Long userId) {
        return userManageService.getLoginResultVoById(userId);
    }
}