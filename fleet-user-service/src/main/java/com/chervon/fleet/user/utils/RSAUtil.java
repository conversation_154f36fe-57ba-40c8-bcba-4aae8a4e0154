package com.chervon.fleet.user.utils;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * RSA算法，实现数据的加密解密。
 */
@Slf4j
public class RSAUtil {

    private static final Integer PAIR_NUM=2;
    private static final Integer NUM2048=2048;
    private static final Cipher CIPHER;
    private static final String RSA = "RSA";
    //公钥
    public static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp6Eo/pGbgM/LkzeU/NVFTxq/Z2ysWF4pBKc2/M0Q30lN29RRqFPXHuypsgZNJFtjcWuKZr4NTNOhh1DkC5UDfVZwIp3o1ZYwPDa7LABMTCrwjOzQLrldMnnDzb1yqYLDFB6aC4nOGE76SERuPeKD2j4T2NRLqzAjP8DvlT3povb3uTF+bP5HAzsZ1q6aBrhXyDlkAcHdRdfJU8PHtpURciQfrJ0jOfxHnuSu/4WoZIKfsFV0b9Iu2crgndXrX5jcFDM1sDmFlsLPJT9WlU0UBx7CKyPOfQDLPwOiPi+tCXwpQUypfHgozKeONl66nYO5hWK6B0145iQQCSlYFLviVwIDAQAB";
    //私钥
    private static final String PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCnoSj+kZuAz8uTN5T81UVPGr9nbKxYXikEpzb8zRDfSU3b1FGoU9ce7KmyBk0kW2Nxa4pmvg1M06GHUOQLlQN9VnAinejVljA8NrssAExMKvCM7NAuuV0yecPNvXKpgsMUHpoLic4YTvpIRG494oPaPhPY1EurMCM/wO+VPemi9ve5MX5s/kcDOxnWrpoGuFfIOWQBwd1F18lTw8e2lRFyJB+snSM5/Eee5K7/hahkgp+wVXRv0i7ZyuCd1etfmNwUMzWwOYWWws8lP1aVTRQHHsIrI859AMs/A6I+L60JfClBTKl8eCjMp442Xrqdg7mFYroHTXjmJBAJKVgUu+JXAgMBAAECggEAJR+p5ob0u1hJq3bZxgytZtFzpkIJASEJUIB/ywjXPbwMRC0HUJIQyEZ9ZnHCz9R1d37rjcl1RJhx43fkqTP9K9rixqfWEsdKM2ujlCEUxKGV2H/xswdWPoF8/amBJU1SSrhkQT3pqLSE9VBCYe4yaTjqSFDr2SO2Cr590L1/apmOQPKgwfB680+4OiB9sdaZwkxxkFNHqbHkwQPGM2HVsXUsat504IutvvQu5/AjJD2eQloGVw3Wk4Pr3I+jty52KxP/un7D1WnzvB0d1diXJr3WY9KXBTmJMyggluHdUQRlZ4dQFHSURrG2VB3YdBw7k/20UmNNTHu5nVipalpn4QKBgQD1FgLrJ2KIF/3V1cSdOZhL1L5WINcLziN1goCWY3Y2TB6TIlUWuEjInzkTl1znyYqPZC0JnY5FuEW0pbA8ezyBTsjuiHaZDQzqp9XFnMbL/v7p068gDPm3kIOyCuwKaY3UcSlUWKaQehk9t32kAnE0/ZBpmDa3lqoC9cTo11NfHwKBgQCvGCR90F0LS7VIoKHiy/263s4h3d5drHNFIGC7lltD7ntEpXbLXnNJwXG4nQ4ij5i9Ar4wLFKOjYcEYX2xT8Yf1SU6YGgL5TiSJ2TODWaM5x+mFDOwBz9MvxdrS/MYkrUbESzCHMmA+36HJEBcB78YQ4EIOMUhnlYjWfms0HptyQKBgQCKnTplWU8m/bq42EBtVAfIy7gi6kv2B06SxY6IgBR5vS+Fs+NACFZR8YvMgs0u4xQU6Wl3iL7Qu9ngw90bN/AOikqmblP1WCY4CVikfXkKI1x1Tx47GFub8IYrinXZpb1rq9zeMY1zJTtlZoQO4Lq6Z7bnoglAaFwbdHk735ztrQKBgAM65clwY5cGwYRiejoN21NsMs9LIYQG32WCEGbFgk+BgcKlbsiXqHbO28M1GoKmtYGfIFURLEC2+auqBMCVoSkLLJGkH6dAhkej9GcctiyZQus0ajOwHonS6KKxFv34DuE98t5t24XGnfHuSEWqWuseq620JmbVXe9vBH0cp3WJAoGAQN8IWxAgfUi4YQa3eGwV9m2kTJitxRNyiSHUJjhsOtKNQ8QlS3+qORgFOYkoaiIIBGH5O7f6SOrpgXv97VyaN/kJNCsq/NauQyHe+Q5uMMNXlmfNGkZGcd9Sb6kw1MTUKXsDqDbpASgyCdux2uZT8SqP+neB4A4WOk35JM2Rp/A=";

    static {
        try {
            CIPHER = Cipher.getInstance(RSA);
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException(ErrorCode.INTERNAL_SERVER_ERROR);
        } catch (NoSuchPaddingException e) {
            throw new ServiceException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 生成密钥对
     * @return Map
     */
    public static Map<String, String> generateKeyPair() {
        try {
            KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(RSA);
            // 密钥位数
            keyPairGen.initialize(NUM2048);
            // 密钥对
            KeyPair keyPair = keyPairGen.generateKeyPair();
            // 公钥
            PublicKey publicKey = keyPair.getPublic();
            // 私钥
            PrivateKey privateKey = keyPair.getPrivate();
            //得到公钥字符串
            String publicKeyString = getKeyString(publicKey);
            //得到私钥字符串
            String privateKeyString = getKeyString(privateKey);
            //将生成的密钥对返回
            Map<String, String> map = new ConcurrentHashMap<>(PAIR_NUM);
            map.put("publicKey", publicKeyString);
            map.put("privateKey", privateKeyString);
            log.info("生成RSA密钥对-公钥：{}",publicKeyString);
            log.info("生成RSA密钥对-私钥：{}",privateKeyString);
            return map;
        } catch (NoSuchAlgorithmException e) {
            log.error("RSAUtil generateKeyPair error.", e);
        }
        return null;
    }

    /**
     * 根据公钥串得到公钥对象
     *
     * @param key 密钥字符串（经过base64编码）
     * @throws Exception 异常
     */
    public static PublicKey getPublicKeyWrapper(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        byte[] keyBytes;
        keyBytes = Base64Util.decode2Byte(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        PublicKey publicKey = null;
        try {
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);
            publicKey = keyFactory.generatePublic(keySpec);
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage());
        } catch (InvalidKeySpecException e) {
            log.error(e.getMessage());
        }

        return publicKey;
    }

    /**
     * 得到私钥
     *
     * @param key 密钥字符串（经过base64编码）
     * @return PrivateKey
     * @throws Exception 异常
     */
    public static PrivateKey getPrivateKeyWrapper(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        try {
            byte[] keyBytes;
            keyBytes = Base64Util.decode2Byte(key);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA);

            return keyFactory.generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new ServiceException(ErrorCode.INTERNAL_SERVER_ERROR);
        } catch (InvalidKeySpecException e) {
            throw new ServiceException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 得到密钥字符串（经过base64编码）
     * @return String
     */
    public static String getKeyString(Key key) {
        byte[] keyBytes = key.getEncoded();

        return Base64Util.encodeByte(keyBytes);
    }


    /**
     * 使用公钥对明文进行加密
     *
     * @param publicKey 公钥
     * @param plainText 明文
     * @return 解密后文本
     */
    public static String encrypt(String publicKey, String plainText) {
        try {
            CIPHER.init(Cipher.ENCRYPT_MODE, getPublicKeyWrapper(publicKey));
            byte[] enBytes = CIPHER.doFinal(plainText.getBytes());

            return Base64Util.encodeByte(enBytes);
        } catch (Exception e) {
            log.error("RSAUtil encrypt error.", e);
        }

        return null;
    }

    /**
     * 使用私钥对密文进行解密
     *
     * @param privateKey 私钥
     * @param enStr      密文
     * @return 解密后文本
     */
    private static String decrypt(String privateKey, String enStr) {
        try {
            CIPHER.init(Cipher.DECRYPT_MODE, getPrivateKeyWrapper(privateKey));
            byte[] deBytes = CIPHER.doFinal(Base64Util.decode2Byte(enStr));

            return new String(deBytes);
        } catch (Exception e) {
            log.error("RSAUtil decrypt error.", e);
        }

        return null;
    }

    /**
     * 使用私钥对密文进行解密
     *
     * @param enStr 密文
     * @return 解密后文本
     */
    public static String decrypt(String enStr) {
        try {
            CIPHER.init(Cipher.DECRYPT_MODE, getPrivateKeyWrapper(PRIVATE_KEY));
            byte[] deBytes = CIPHER.doFinal(Base64Util.decode2Byte(enStr));
            return new String(deBytes);
        } catch (Exception e) {
            log.error("RSAUtil decrypt error.", e);
        }
        return null;
    }

    public static void main(String[] args){
        String password="123456";
        String aData2 = RSAUtil.encrypt(PUBLIC_KEY, password);
        log.info("加密后文字：\r\n" + aData2);

        String dData2 = RSAUtil.decrypt(PRIVATE_KEY, aData2);
        log.info("解密后文字: \r\n" + dData2);

        //生成秘钥对
        generateKeyPair();
    }

}
