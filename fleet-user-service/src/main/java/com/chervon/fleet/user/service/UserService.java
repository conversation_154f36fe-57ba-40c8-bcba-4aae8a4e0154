package com.chervon.fleet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.user.api.entity.dto.*;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.UserRegisterActiveCheckVo;
import com.chervon.fleet.user.api.entity.vo.UserVo;
import com.chervon.fleet.user.entity.po.User;
import java.util.List;

/**
 * fleet用户表服务接口
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-26 14:28:36
 */
public interface UserService extends IService<User> {

    /**
     * 忘记密码设置新密码
     *
     * @param requestDto 邮箱验证码
     * @return 重置结果是否成功
     */
    boolean forgetPasswordSetNewPassword(ForgetPasswordRequestDto requestDto);

    /**
     * 忘记密码等根据邮箱获取有效的用户信息
     * @param userQuery
     * @return
     */
    User getValidUser(UserQuery userQuery);

    List<User> getList(UserQuery userQuery);

    /**
     * * 修改密码验证旧密码接口
     *
     * @param modifyPasswordDto 修改密码对象
     * @return 返回校验结果
     */
    Boolean modifyPasswordCheck(ModifyPasswordDto modifyPasswordDto);

    /**
     * fleet修改密码
     *
     * @param modifyPasswordDto 修改密码对象
     * @return 密码修改结果
     */
    Boolean modifyPassword(ModifyPasswordDto modifyPasswordDto);

    /**
     * * 忘记密码检查验证码是否过期
     *
     * @param forgetPasswordRequestDto 忘记密码请求对象
     * @return 返回验证结果
     */
    Boolean forgetPasswordCheck(ForgetPasswordRequestDto forgetPasswordRequestDto);

    /**
     * * 点击邮件中的跳转链接进行注册的验证码检查是否过期
     *
     * @param userRegisterCheckDto 验证请求对象
     * @return 返回验证结果
     */
    Boolean registerCheck(UserRegisterCheckDto userRegisterCheckDto);

    /**
     * 邀请员工注册2：点击邮件中的跳转链接进行激活的验证码检查是否过期
     *
     * @param userRegisterCheckDto 验证请求对象
     * @return 激活用户的ID
     */
    UserRegisterActiveCheckVo userActivityCheck(UserRegisterCheckDto userRegisterCheckDto);

    /**
     * 用户注册
     *
     * @param registerDto
     * @return
     */
    UserVo register(UserRegisterDto registerDto);

    /**
     * 邀请注册检测用户是否已存在
     *
     * @param companyId 公司ID,传空为用户自注册逻辑判断
     * @param email     邮箱
     */
    void checkUserHadRegister(Long companyId, String email,Integer checkFlag);

    /**
     * 邀请员工注册1：发送邀请邮件并创建用户
     *
     * @param registerDto
     * @return
     */
    Boolean inviteRegister(UserRegisterDto registerDto);

    /**
     * 邀请员工注册1.5: 重新发送邀请注册邮件
     *
     * @param id 用户ID
     */
    void inviteRegisterAgain(Long id);

    /**
     * * 激活用户
     *
     * @param activityDto
     * @return
     */
    void activityUser(UserActivityDto activityDto);

    /**
     * * 管理员角色转移
     *
     * @param request 请求参数
     * @return 转移结果
     */
    Boolean transferAdmin(TransferAdminDto request);
    /**
     * 注册用户-1：注册管理员用户发送邮件
     *
     * @param email 邮件
     */
    void registerSendMail(String email);

}
