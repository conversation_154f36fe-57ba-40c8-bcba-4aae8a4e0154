package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.fleet.user.api.entity.vo.RequestListVo;
import com.chervon.fleet.user.entity.consts.NumberConst;
import com.chervon.fleet.user.entity.enums.IsDeletedEnum;
import com.chervon.fleet.user.entity.enums.RequestListType;
import com.chervon.fleet.user.entity.po.RequestList;
import com.chervon.fleet.user.entity.query.RequestListQuery;
import com.chervon.fleet.user.mapper.RequestListMapper;
import com.chervon.fleet.user.service.RequestListService;
import com.chervon.fleet.user.utils.PageResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.io.Serializable;
import java.util.List;

import static cn.dev33.satoken.spring.SaPathMatcherHolder.pathMatcher;

/**
 * 访问名单配置 服务实现类
 * <AUTHOR>
 * 
 */
@Slf4j
@Service
public class RequestListServiceImpl extends ServiceImpl<RequestListMapper, RequestList> implements RequestListService {
    private static final String GATEWAY_WHITELIST = "GATEWAY_WHITELIST";
    private static final String GATEWAY_BLACKLIST = "GATEWAY_BLACKLIST";

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(Serializable id) {
        return removeById(id);
    }

    /**
     * 根据主键查询实体信息
     *
     * @param id 访问名单配置
     * @return
     */
    @Override
    public RequestList get(Serializable id) {
        return getById(id);
    }

    /**
     * 根据查询条件获取列表，不分页
     * @param requestListQuery 访问名单配置查询bean
     * @return
     */
    @Override
    public List<RequestList> getList(RequestListQuery requestListQuery) {
        Wrapper<RequestList> queryWrapper = getQueryWrapper(requestListQuery);
        return list(queryWrapper);
    }

    /**
     * 获取白名单
     * @return 访问名单配置列表
     */
    @Override
    public List<RequestList> getWhitelist() {
        if (!RedisUtils.hasKey(GATEWAY_WHITELIST)) {
            RequestListQuery query = new RequestListQuery()
                    .setIsDeleted(IsDeletedEnum.NORMAL.getType())
                    .setType(RequestListType.WHITELIST.getType());
            List<RequestList> requestListList = getList(query);
            RedisUtils.setWithExpire(GATEWAY_WHITELIST, requestListList, NumberConst.WHITE_LIST_EXPIRE_SECOND);//5分钟
        }
        return RedisUtils.getCacheObject(GATEWAY_WHITELIST);
    }

    /**
     * 获取黑名单
     * @return 访问名单配置列表
     */
    @Override
    public List<RequestList> getBlacklist() {
        if (!RedisUtils.hasKey(GATEWAY_BLACKLIST)) {
            RequestListQuery query = new RequestListQuery()
                    .setIsDeleted(IsDeletedEnum.NORMAL.getType())
                    .setType(RequestListType.BLACKLIST.getType());
            List<RequestList> requestListList = getList(query);
            final List<RequestListVo> requestListVos = BeanCopyUtils.copyList(requestListList, RequestListVo.class);
            RedisUtils.setWithExpire(GATEWAY_BLACKLIST, requestListVos, NumberConst.BLACK_LIST_EXPIRE_SECOND);//20分钟
        }
        return RedisUtils.getCacheObject(GATEWAY_BLACKLIST);
    }

    /**
     * * 请求黑白名单验证
     * @param method
     * @param url
     * @return
     */
    public Boolean checkRequestList(String method, String url) {
        final boolean whiteResult = checkWhitelist(url, method);
        if(whiteResult){
            return true;
        }
        return false;
    }

    /**
     * 校验是否为黑名单
     */
    public boolean checkBlacklist(String path, String method) {
        //黑名单校验
        List<RequestList> requestListList = getBlacklist();
        return requestListList.stream()
                .filter(x -> method.equalsIgnoreCase(x.getMatchMethod()) || !StringUtils.hasText(x.getMatchMethod()))
                .anyMatch(x -> pathMatcher.match(x.getPath(), path));
    }

    /**
     * 校验是否为白名单
     * @param path   请求路径
     * @param method 请求方法
     * @return 是否白名单
     */
    public boolean checkWhitelist(String path, String method) {
        //白名单校验
        List<RequestList> requestListList = getWhitelist();
        return requestListList.stream()
                .filter(x -> method.equalsIgnoreCase(x.getMatchMethod()) || !StringUtils.hasText(x.getMatchMethod()))
                .anyMatch(x -> pathMatcher.match(x.getPath(), path));
    }


    /**
     * 刷新名单
     */
    @Override
    public void refresh() {
        RedisUtils.deleteObject(GATEWAY_BLACKLIST);
        RedisUtils.deleteObject(GATEWAY_WHITELIST);
    }

    /**
     * 生成查询条件
     *
     * @param requestListQuery
     * @return
     */
    private LambdaQueryWrapper<RequestList> getQueryWrapper(RequestListQuery requestListQuery) {
        return Wrappers.<RequestList>lambdaQuery()
                .eq(StringUtils.hasText(requestListQuery.getPath()), RequestList::getPath, requestListQuery.getPath())
                .eq(requestListQuery.getType() != null, RequestList::getType, requestListQuery.getType())
                .eq(requestListQuery.getIsDeleted() != null, RequestList::getIsDeleted, requestListQuery.getIsDeleted())
                .select(RequestList::getPath,RequestList::getMatchMethod,RequestList::getType)
                ;
    }
}
