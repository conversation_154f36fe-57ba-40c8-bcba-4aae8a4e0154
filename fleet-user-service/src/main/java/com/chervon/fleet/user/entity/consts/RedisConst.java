package com.chervon.fleet.user.entity.consts;

import com.chervon.fleet.user.api.entity.enums.MailBusinessEnum;

import java.text.MessageFormat;

/**
 * redis 常量配置
 *
 * <AUTHOR> 2023/7/4
 */
public class RedisConst {

    private static final String USER_MAIL_CODE_PREFIX = "fleet:user:mail_{0}_{1}";

    /**
     * fleet用户邮箱发送验证码
     * redisKey举例 fleet:user:<EMAIL>
     *
     * @param email            邮箱
     * @param mailBusinessEnum 枚举值 .eg USER_REGISTER
     * @return
     */
    public static String getUserMailCodeKey(String email, String mailBusinessEnum) {
        return MessageFormat.format(USER_MAIL_CODE_PREFIX, mailBusinessEnum, email);
    }

    private static final String INVITE_USER_MAIL_CODE_PREFIX = "fleet:user:mail_{0}_{1}_{2}";

    /**
     * fleet邀请用户邮箱发送验证码
     * redisKey举例 fleet:user:mail_USER_INVITATION_test@mail.com_companyId
     *
     * @param email     邮箱
     * @param companyId 公司ID
     * @return redisKey
     */
    public static String getInviteUserMailCodeKey(String email, Long companyId) {
        return MessageFormat.format(INVITE_USER_MAIL_CODE_PREFIX, MailBusinessEnum.USER_INVITATION.name(), email, companyId.toString());
    }

    /**
     * fleet用户忘记密码、修改密码发送验证码
     */
    private static final String FORGET_PASSWORD_MAIL_CODE = "fleet:user:forget_password_mail_code_{0}";

    public static String getForgetPasswordMailCodeKey(String key) {
        return MessageFormat.format(FORGET_PASSWORD_MAIL_CODE, key);
    }

    /**
     * 登录缓存key
     */
    //参数说明：deviceType+deviceId+userId
    public static final String FLEET_LOGIN_TOKEN_PREFIX = "fleet:user:token_{0}_{1}_{2}";

    public static String getFleetRedisTokenKey(Integer deviceType, String deviceId, Long userId) {
        return MessageFormat.format(RedisConst.FLEET_LOGIN_TOKEN_PREFIX, deviceType.toString(), deviceId, userId.toString());
    }

    //登录用户权限列表:userId
    public static final String FLEET_LOGIN_PERMISSION = "fleet:user:permission_{0}_{1}";

    public static String getFleetPermissionKey(Long userId, String app) {
        return MessageFormat.format(RedisConst.FLEET_LOGIN_PERMISSION, userId.toString());
    }

    /**
     * 用户邮箱验证码过期时间：72小时
     */
    public static final long USER_MAIL_CODE_EXPIRE_SECOND = 3600L * 72L;

    // Token过期时间: app：5天  web:12小时
    public static final long FLEET_APP_TOKEN_EXPIRES_SECOND = 3600L * 24L * 5L;
    public static final long FLEET_WEB_TOKEN_EXPIRES_SECOND = 3600L * 12L;

    /**
     * 用户登录记录,目前仅登录时进行更新存储
     * 存储内容见：
     *
     * @see com.chervon.fleet.user.entity.dto.RedisUserLoginLog
     */
    public static final String USER_LOGIN_LOG = "fleet:user:login:log:{0}";

    public static String getUserLoginLogKey(Long userId) {
        return MessageFormat.format(USER_LOGIN_LOG, userId.toString());
    }
}
