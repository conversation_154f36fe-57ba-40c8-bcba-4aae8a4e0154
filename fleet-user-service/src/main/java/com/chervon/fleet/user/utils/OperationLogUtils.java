package com.chervon.fleet.user.utils;

import com.chervon.fleet.user.entity.po.LogOperate;
import java.util.Objects;

/**
 * 操作日志帮助类
 */
public class OperationLogUtils {

    private final static ThreadLocal<LogOperate> LOCAL_OPERATION_LOG = new InheritableThreadLocal<>();

    public static LogOperate get() {
        LogOperate operationLog = LOCAL_OPERATION_LOG.get();
        if(Objects.isNull(operationLog)){
            operationLog=new LogOperate();
            append(operationLog);
        }
        return operationLog;
    }

    public static LogOperate append(LogOperate operationLog) {
        LOCAL_OPERATION_LOG.set(operationLog);
        return operationLog;
    }

    public static void remove() {
        LOCAL_OPERATION_LOG.remove();
    }
}
