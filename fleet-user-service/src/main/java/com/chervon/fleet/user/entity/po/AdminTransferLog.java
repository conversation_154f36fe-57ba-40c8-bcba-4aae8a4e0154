package com.chervon.fleet.user.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 管理员变更记录表(t_admin_transfer_log)实体类
 *
 * <AUTHOR>
 * @since 2023-06-25 10:38:33
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_admin_transfer_log")
public class AdminTransferLog extends Model<AdminTransferLog> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 管理员userId
     */
    private Long userId;
    /**
     * 租户Id
     */
    private Long companyId;
    /**
     * 管理员邮箱
     */
    private String email;
    /**
     * 姓名
     */
    private String userName;
    /**
     * first name
     */
    private String userFirstName;
    /**
     * last name
     */
    private String userLastName;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;

}