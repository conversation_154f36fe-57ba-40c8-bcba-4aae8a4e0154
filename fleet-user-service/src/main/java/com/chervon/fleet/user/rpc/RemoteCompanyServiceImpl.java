package com.chervon.fleet.user.rpc;

import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.utils.ConvertUtil;
import com.chervon.fleet.user.api.entity.dto.CompanyDto;
import com.chervon.fleet.user.api.entity.dto.CompanyNameDto;
import com.chervon.fleet.user.api.entity.query.CompanyQuery;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;
import com.chervon.fleet.user.api.service.RemoteCompanyService;
import com.chervon.fleet.user.service.CompanyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 租户服务
 * <AUTHOR> 2023/7/6
 */
@Slf4j
@DubboService
@Service
public class RemoteCompanyServiceImpl implements RemoteCompanyService {
    @Autowired
    private CompanyService companyService;

    @Override
    public Long add(String companyName, Long userId) {
        return companyService.add(companyName, userId);
    }

    @Override
    public boolean edit(CompanyDto companyDto) {
        return companyService.edit(companyDto);
    }

    @Override
    public void editName(CompanyNameDto companyNameDto) {
        CompanyDto companyDto = ConvertUtil.convert(companyNameDto, CompanyDto.class);
        companyService.edit(companyDto);
    }

    /**
     * 租户信息分页查询
     *
     * @param query 查询条件
     * @return 经销商分页信息
     */
    @Override
    public PageResult<CompanyVo> getPagedList(CompanyQuery query) {
        return companyService.getPagedList(query);
    }

    /**
     * 租户详情查询
     *
     * @param id 主键ID
     * @return 租户详情
     */
    @Override
    public CompanyVo getDetail(Long id) {
        return companyService.getDetail(id);
    }

    /**
     * 根据名称查询租户信息
     *
     * @param name 租户名称
     * @return 企业信息
     */
    @Override
    public CompanyVo getCompanyByName(String name) {
        return companyService.getCompanyByName(name);
    }
}
