package com.chervon.fleet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.fleet.user.api.entity.dto.FleetUserPageDto;
import com.chervon.fleet.user.api.entity.dto.UserEditDto;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import com.chervon.fleet.user.api.entity.enums.UserTypeEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.UserPageQuery;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.*;
import com.chervon.fleet.user.entity.consts.RedisConst;
import com.chervon.fleet.user.entity.consts.StringConst;
import com.chervon.fleet.user.entity.dto.RedisUserLoginLog;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.mapper.UserMapper;
import com.chervon.fleet.user.service.*;
import com.chervon.fleet.user.utils.jwt.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * fleet用户管理服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2022-09-20 14:28:36
 */
@Slf4j
@Service
public class UserManageServiceImpl extends ServiceImpl<UserMapper, User> implements UserManageService {
    @Autowired
    RoleService roleService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    SendMailService sendMailService;
    @Autowired
    JwtUtils jwtUtils;
    @Autowired
    ResourceService resourceService;
    @Autowired
    RoleResourceService roleResourceService;
    @Autowired
    AdminTransferLogService adminTransferLogService;
    @Lazy
    @Autowired
    private CompanyService companyService;


    /**
     * 编辑密码后退出当前设备所有登录
     *
     * @param userId 用户ID
     */
    @Override
    public void logoutAllDevicesByUserId(Long userId) {
        if (userId == null) {
            return;
        }
        Map<String, RedisUserLoginLog> cacheMap = RedisUtils.getCacheMap(RedisConst.getUserLoginLogKey(userId));
        if (cacheMap != null) {
            for (Map.Entry<String, RedisUserLoginLog> entry : cacheMap.entrySet()) {
                if (entry.getValue().getRedisKey() != null) {
                    RedisUtils.deleteObject(entry.getValue().getRedisKey());
                }
            }
        }
        RedisUtils.deleteObject(RedisConst.getUserLoginLogKey(userId));
    }

    @Override
    public User getOneWithDeleted(Long userId) {
        return this.getBaseMapper().selectOneWithDeleted(userId);
    }

    /**
     * 用户转为游离状态（解除租户绑定关系和激活状态）
     * @param userId 用户id
     */
    @Override
    public void freeUser(Long userId) {
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.USER_ID);
        final User user = this.getById(userId);
        if (Objects.isNull(user)) {
            throw new ServiceException(UserErrorCodeEnum.DATA_NULL);
        }
        if (user.getUserType() == 1) {
            throw new ServiceException(UserErrorCodeEnum.USER_TYPE_EDIT_INVALID);
        }
        this.logoutAllDevicesByUserId(userId);
        this.update(new User(), new LambdaUpdateWrapper<User>()
                .eq(User::getId, userId)
                .set(User::getCompanyId, null)
                .set(User::getAccountStatus, AccountStatusEnum.NOT_ACTIVATED.getType())
                .set(User::getUserType, null));
    }

    @Override
    public IPage<FleetUserVo> search(IPage<FleetUserVo> page, FleetUserPageDto search) {
        return this.getBaseMapper().search(page, search);
    }

    @Override
    public List<FleetUserVo> searchList(FleetUserPageDto search) {
        return this.getBaseMapper().searchList(search);
    }


    @Override
    public LoginResultVo getLoginResultVoById(Long userId) {
        User fleetUser = this.getById(userId);
        //验证用户状态是否已注销
        if (fleetUser.getAccountStatus().equals(AccountStatusEnum.CANCELED.getType())) {
            throw new ServiceException(UserErrorCodeEnum.LOGIN_PASSWORD_OR_NAME_ERROR);
        }
        //构建登录返回信息
        LoginResultVo loginInfoDto = BeanCopyUtils.copyProperties(fleetUser, LoginResultVo.class);
        //查询公司租户信息
        final CompanyVo companyVo = companyService.getDetail(fleetUser.getCompanyId());
        if (!Objects.isNull(companyVo)) {
            loginInfoDto.setCompanyName(companyVo.getCompanyName());
        }
        loginInfoDto.setUserType(fleetUser.getUserType());
        return loginInfoDto;
    }

    /**
     * 正则表达式验证密码
     *
     * @param input 字符串
     * @return boolean
     */
    public boolean checkPasswordFormat(String input) {
        // 6-18 位，字母、数字、特殊字符
        String reg = "^([A-Z]|[a-z]|[0-9]|[`\\-=\\[\\];,'./~!@#$%^&*()_+}{:?|\"<>]){6,18}$";
        return input.matches(reg);
    }


    @Override
    public PageResult<UserVo> page(UserPageQuery userPageQuery, Long companyId) {
        //查询当前租户下用户分页列表
        IPage<User> page = new Page<>(userPageQuery.getPageNum(), userPageQuery.getPageSize());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getCompanyId, companyId)
                .eq(null != userPageQuery.getUserType(), User::getUserType, userPageQuery.getUserType())
                .eq(null != userPageQuery.getAccountStatus(), User::getAccountStatus, userPageQuery.getAccountStatus())
                .like(StringUtils.hasText(userPageQuery.getName()), User::getName, userPageQuery.getName())
                .orderByDesc(User::getInvitationTime);
        page = this.page(page, queryWrapper);
        PageResult<UserVo> pageResult = new PageResult<>();
        pageResult.setTotal(page.getTotal());
        pageResult.setPageNum(page.getCurrent());
        pageResult.setPageSize(page.getSize());
        if (page.getTotal() <= 0) {
            return pageResult;
        }
        final List<UserVo> userVos = BeanCopyUtils.copyList(page.getRecords(), UserVo.class);
        // 补充是否可以重复邀请字段
        assert userVos != null;
        userVos.stream().filter(userVo -> userVo.getAccountStatus().intValue() == AccountStatusEnum.NOT_ACTIVATED.getType()).forEach(
                userVo -> {
                    String verification = RedisUtils.getCacheObject(RedisConst.getInviteUserMailCodeKey(userVo.getEmail(), userVo.getCompanyId()));
                    userVo.setCanInviteAgain(StringUtils.hasText(verification) ? 0 : 1);
                }
        );
        pageResult.setList(userVos);
        return pageResult;
    }

    /**
     * 逻辑删除用户
     * @param userId
     * @return
     */
    @Override
    public Boolean delete(Long userId) {
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.ID);
        final User user = this.getById(userId);
        if (Objects.isNull(user)) {
            throw new ServiceException(UserErrorCodeEnum.DATA_NULL);
        }
        if (user.getUserType() == 1) {
            throw new ServiceException(UserErrorCodeEnum.USER_TYPE_EDIT_INVALID);
        }
        logoutAllDevicesByUserId(userId);
        return removeById(user);
    }

    @Override
    public Boolean edit(UserEditDto userDto) {
        Assert.isId(userDto.getId(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.ID);
        Assert.hasText(userDto.getFirstName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.FIRST_NAME);
        Assert.hasText(userDto.getLastName(), ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.LAST_NAME);
        User fleetUser = this.getById(userDto.getId());
        if (null == fleetUser) {
            throw new ServiceException(UserErrorCodeEnum.DATA_NULL);
        }
        Integer originalUserType = fleetUser.getUserType();
        if (fleetUser.getUserType() == UserTypeEnum.ADMIN.getType() && userDto.getUserType() != UserTypeEnum.ADMIN.getType()) {
            throw new ServiceException(UserErrorCodeEnum.USER_TYPE_EDIT_INVALID);
        }
        if (fleetUser.getUserType() != UserTypeEnum.ADMIN.getType() && userDto.getUserType() == UserTypeEnum.ADMIN.getType()) {
            throw new ServiceException(UserErrorCodeEnum.USER_TYPE_EDIT_INVALID);
        }
        //更新用户信息
        fleetUser.setFirstName(userDto.getFirstName());
        fleetUser.setLastName(userDto.getLastName());
        fleetUser.setName(getName(userDto.getFirstName(),userDto.getLastName()));
        fleetUser.setUserType(userDto.getUserType());
        boolean result = updateById(fleetUser);
        // 发送用户角色变更邮箱
        if (result && !Objects.equals(originalUserType, userDto.getUserType())) {
            sendMailService.sendUserRoleUpdateNoticeMail(fleetUser.getEmail(), userDto.getUserType(), fleetUser.getName());
            logoutAllDevicesByUserId(fleetUser.getId());
        }
        return result;
    }

    @Override
    public List<UserSyncVo> getUserSyncList() {
        final LocalDateTime localDateTime = LocalDateTime.now().minus(3, ChronoUnit.DAYS);
        final long milli = localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        UserQuery userQuery = new UserQuery().setModifyBeginTime(milli)
                .setModifyEndTime(Instant.now().toEpochMilli());
        LambdaQueryWrapper<User> queryWrapper = getQueryWrapper(userQuery);
        final List<User> userList = list(queryWrapper);
        return userList.stream().map(a -> new UserSyncVo().setUserId(a.getId()).setCompanyId(a.getCompanyId()).setUserStatus(a.getAccountStatus())).collect(Collectors.toList());
    }

    @Override
    public Boolean userExist(UserQuery userQuery) {
        LambdaQueryWrapper<User> queryWrapper = getQueryWrapper(userQuery);
        long userCount = this.count(queryWrapper);
        return userCount > 0;
    }

    private LambdaQueryWrapper<User> getQueryWrapper(UserQuery userQuery) {
        return new LambdaQueryWrapper<User>()
                .eq(userQuery.getId() != null, User::getId, userQuery.getId())
                .eq(userQuery.getUserName() != null, User::getUserName, userQuery.getUserName())
                .like(userQuery.getName() != null, User::getName, userQuery.getName())
                .eq(userQuery.getCompanyId() != null && userQuery.getCompanyId() != 0L, User::getCompanyId, userQuery.getCompanyId())
                .eq(userQuery.getParentId() != null, User::getParentId, userQuery.getParentId())
                .in(!CollectionUtils.isEmpty(userQuery.getUserTypeList()), User::getUserType, userQuery.getUserTypeList())
                .in(!CollectionUtils.isEmpty(userQuery.getListAccountStatus()), User::getAccountStatus, userQuery.getListAccountStatus())
                .eq(userQuery.getAccountStatus() != null, User::getAccountStatus, userQuery.getAccountStatus())
                .eq(StringUtils.hasText(userQuery.getEmail()), User::getEmail, userQuery.getEmail())
                .ge(userQuery.getBeginTime() != null, User::getCreateTime, userQuery.getBeginTime())
                .lt(userQuery.getEndTime() != null, User::getCreateTime, userQuery.getEndTime())
                .ge(userQuery.getModifyBeginTime() != null, User::getModifyTime, userQuery.getModifyBeginTime())
                .lt(userQuery.getModifyEndTime() != null, User::getModifyTime, userQuery.getModifyEndTime())
                .in(!CollectionUtils.isEmpty(userQuery.getIdList()), User::getId, userQuery.getIdList())
                .eq(userQuery.getIsEnabled() != null, User::getIsEnabled, userQuery.getIsEnabled());
    }

    private static String getName(String firstName,String lastName){
        return firstName.concat(" ").concat(lastName);
    }
}
