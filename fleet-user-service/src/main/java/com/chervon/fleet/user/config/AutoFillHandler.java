package com.chervon.fleet.user.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.chervon.common.web.util.UserContext;
import com.chervon.idgenerator.util.IdUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * 自动填充处理器
 *
 * <AUTHOR> 2023/6/29
 */
@Primary
@Component
public class AutoFillHandler implements MetaObjectHandler {
    private static final String MODIFY_TIME="modifyTime";
    private static final String MODIFIER="modifier";
    @Override
    public void insertFill(MetaObject metaObject) {
        final boolean hasId = metaObject.hasSetter("id");
        final boolean hasCreateTime = metaObject.hasSetter("createTime");
        final boolean hasCreator = metaObject.hasSetter("creator");
        final boolean hasModifyTime = metaObject.hasSetter(MODIFY_TIME);
        final boolean hasModifier = metaObject.hasSetter(MODIFIER);
        final boolean hasIsDeleted = metaObject.hasSetter("isDeleted");
        if (hasId) {
            setFieldValByName("id", IdUtils.snowFlakeNext(), metaObject);
        }
        if (hasCreateTime) {
            setFieldValByName("createTime", System.currentTimeMillis(), metaObject);
        }
        if (hasCreator) {
            setFieldValByName("creator", UserContext.getUserName(), metaObject);
        }
        if (hasModifier) {
            setFieldValByName(MODIFIER, UserContext.getUserName(), metaObject);
        }
        if (hasModifyTime) {
            setFieldValByName(MODIFY_TIME, System.currentTimeMillis(), metaObject);
        }
        if (hasIsDeleted) {
            setFieldValByName("isDeleted", 0, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        final boolean hasUpdateTime = metaObject.hasSetter("updateTime");
        final boolean hasModifyTime = metaObject.hasSetter(MODIFY_TIME);
        final boolean hasModifier = metaObject.hasSetter(MODIFIER);
        if (hasUpdateTime) {
            setFieldValByName("updateTime", System.currentTimeMillis(), metaObject);
        }
        if (hasModifyTime) {
            setFieldValByName(MODIFY_TIME, System.currentTimeMillis(), metaObject);
        }
        if (hasModifier) {
            setFieldValByName(MODIFIER, UserContext.getUserName(), metaObject);
        }
    }
}
