package com.chervon.fleet.user.entity.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 操作基本信息基类 *
 * <AUTHOR> 2023/7/18
 */
@Data
@Accessors(chain = true)
public class BaseOperate<T extends Model<?>> extends Model<T>  {
    private static final long serialVersionUID = 1L;
    /**
     * 创建人姓名
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 更新人姓名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
}
