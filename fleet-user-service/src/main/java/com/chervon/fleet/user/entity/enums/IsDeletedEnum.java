package com.chervon.fleet.user.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 * 是否删除枚举
 * <AUTHOR> 2023/5/25
 */
public enum IsDeletedEnum implements TypeEnum {
    /**
     * 正常
     */
    NORMAL(0, "正常"),
    /**
     * 已删除
     */
    DELETED(1, "已删除");

    private int type;
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    IsDeletedEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取类型
     * @return 值id
     */
    @Override
    public int getType() {
        return this.type;
    }

    /**
     * 获取描述
     * @return 描述
     */
    @Override
    public String getDesc() {
        return this.desc;
    }
}