package com.chervon.fleet.user.entity.dto;

import com.chervon.common.core.constant.StringPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 访问名单配置
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "RequestList数据传输对象", description = "访问名单配置")
public class RequestListAddDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 访问路径
     */
    @ApiModelProperty(value = "访问路径", required = true)
    private String path;
    /**
     * 匹配方法
     */
    @ApiModelProperty(value = "匹配方法")
    private String matchMethod = StringPool.EMPTY;
    /**
     * 来源ip地址
     */
    @ApiModelProperty(value = "来源ip地址")
    private String sourceIp = StringPool.ASTERISK;
    /**
     * mac地址
     */
    @ApiModelProperty(value = "mac地址")
    private String mac = StringPool.ASTERISK;
    /**
     * 限制类型（1.白名单，2正常，3黑名单）
     */
    @ApiModelProperty(value = "限制类型（1.白名单，2正常，3黑名单）", required = true)
    private Integer type;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
