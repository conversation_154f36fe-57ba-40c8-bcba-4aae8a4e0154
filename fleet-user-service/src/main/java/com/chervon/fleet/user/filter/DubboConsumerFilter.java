package com.chervon.fleet.user.filter;

import com.chervon.common.web.entity.ClientInfo;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.common.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;

/**
 * （通过统一过滤器）服务调用方将上游请求头传递到下游rpc服务中
 *
 * <AUTHOR> 2023/6/26
 */
@Slf4j
@Activate(group = {CommonConstants.CONSUMER})
public class DubboConsumerFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        try {
            ClientInfo clientInfo = UserContext.getClientInfo();
            final Long userId = clientInfo.getUserId();
            final String userName = clientInfo.getUserName();
            final String name = clientInfo.getName();
            final Integer userType = clientInfo.getUserType();
            final Integer deviceType = clientInfo.getDeviceType();
            final String deviceId = clientInfo.getDeviceId();
            final String language = clientInfo.getLanguage();
            final String traceId = clientInfo.getTraceId();
            final Long companyId = clientInfo.getCompanyId();
            RpcContext.getServiceContext().setAttachment(HeaderUtils.USER_ID, userId);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.USER_NAME, userName);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.NAME, name);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.USER_TYPE, userType);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.DEVICE_TYPE, deviceType);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.DEVICE_ID, deviceId);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.LANGUAGE, language);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.TRACE_ID, traceId);
            RpcContext.getServiceContext().setAttachment(HeaderUtils.COMPANY_ID, companyId);
        } catch (Exception e) {
            log.error("Exception in process DubboConsumerFilter", e);
        }
        return invoker.invoke(invocation);
    }
}
