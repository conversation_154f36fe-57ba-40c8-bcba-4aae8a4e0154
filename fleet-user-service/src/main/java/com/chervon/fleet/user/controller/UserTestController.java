package com.chervon.fleet.user.controller;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.fleet.user.api.entity.vo.FleetUserVo;
import com.chervon.fleet.user.api.service.RemoteFleetUserCenterService;
import com.chervon.fleet.user.entity.consts.StringConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * test测试接口
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
@RestController
@RequestMapping("/test/user")
@Api(tags = "test接口")
public class UserTestController {

    @Autowired
    private RemoteFleetUserCenterService remoteFleetUserCenterService;


    @ApiOperation("测试用户详情")
    @GetMapping(value = "/userDetail",produces = "application/json")
    public FleetUserVo userDetail(@RequestParam("userId") Long userId) {
        Assert.isId(userId, ErrorCode.PARAMETER_NOT_PROVIDED, StringConst.USER_ID);
        return remoteFleetUserCenterService.userDetail(userId);
    }

}
