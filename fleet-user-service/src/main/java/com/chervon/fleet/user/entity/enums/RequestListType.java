package com.chervon.fleet.user.entity.enums;

import com.chervon.common.core.enums.TypeEnum;

/**
 *  请求列表类型
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
public enum RequestListType implements TypeEnum {
    /**
     * 白名单
     */
    WHITELIST(1, "白名单"),
    /**
     * 正常
     */
    NORMAL(2, "正常"),
    /**
     * 黑名单
     */
    BLACKLIST(3, "黑名单"),
    ;

    /**
     * 类型
     */
    private int type;
    /**
     * 描述
     */
    private String desc;

    /**
     * 构造函数
     * @param type
     * @param desc
     */
    RequestListType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 获取类型
     * @return 返回值id
     */
    @Override
    public int getType() {
        return this.type;
    }

    /**
     * 获取描述
     * @return 描述
     */
    @Override
    public String getDesc() {
        return this.desc;
    }
}
