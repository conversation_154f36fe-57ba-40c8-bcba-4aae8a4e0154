package com.chervon.fleet.user.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.user.api.entity.query.ResourceQuery;
import com.chervon.fleet.user.api.entity.vo.ResourceVo;
import com.chervon.fleet.user.entity.po.Resource;

import java.util.List;

/**
 * 资源表(菜单按钮)服务接口
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
public interface ResourceService extends IService<Resource> {

    /**
     *  根据查询参数获取资源列表（子列表为空） *
     * @param query
     * @return
     */
    List<Resource> getResourceList(ResourceQuery query);

    /**
     *  根据查询参数获取资源列表（子列表为空） *
     * @param query
     * @return
     */
    List<ResourceVo> getResourceVoList(ResourceQuery query);

}
