package com.chervon.fleet.user.utils;

import com.chervon.common.core.constant.CommonConstant;
import com.chervon.common.core.constant.StringPool;
import org.springframework.util.DigestUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Description: This is description
 * <AUTHOR>
 * @Date: 2020/8/24 16:11
 */
public class SecurityUtil {

    private final static String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private final static String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private final static String NUMBER = "12345465870";
    private final static String SYMBOL = "!`~@#$%^&*()_+-=|}]{[\"':;?/>.<,'\\";
    private final static Random RANDOM = new Random();

    public static String getInitPassword(){
        String number = numberGenerator();
        String uppCase = uppercaseGenerator();
        String lowCase = lowercaseGenerator();
        String allStr=lowCase+number+uppCase;
        StringBuilder sb = new StringBuilder();
        Set<Integer> set=new HashSet<>();
        do {
            int next = RANDOM.nextInt(allStr.length());
            if(set.contains(next)){
                continue;
            }
            else{
                set.add(next);
            }
            sb.append(allStr.charAt(next));
        } while (sb.length() < allStr.length());
        return sb.toString();
    }

    /**
     * * 根据传入密码明文生成数据库加盐密码
     * @param password 密码明文
     * @return 加密后密码和盐值
     */
    public static Tuple2<String,String> buildMd5Password(String password){
        //生成16位盐值 3位符号+3位数字+5位大写字母+5位小写字母
        String secret = SecurityUtil.salt();
        int secretTimes = CommonConstant.ONE;
        String dbPassword = password + StringPool.OR + secret;
        for (int i = 0; i < secretTimes; i++) {
            dbPassword = DigestUtils.md5DigestAsHex(dbPassword.getBytes(StandardCharsets.UTF_8));
        }
        return Tuples.of(dbPassword,secret);
    }

    /**
     * * 根据现有密码原文和盐值生成数据库存储的密码（加盐后的）
     * @param password
     * @param secret
     * @return
     */
    public static String getDbMd5Password(String password, String secret){
        String dbPassword = password + StringPool.OR + secret;
        dbPassword = DigestUtils.md5DigestAsHex(dbPassword.getBytes(StandardCharsets.UTF_8));
        return dbPassword;
    }

    /**
     * 生成16位盐值 3位符号+3位数字+5位大写字母+5位小写字母
     * @return
     */
    public static String salt() {
        String symbol = symbolGenerator();
        String number = numberGenerator();
        String uppCase = uppercaseGenerator();
        String lowCase = lowercaseGenerator();

        String allStr = symbol + number + uppCase + lowCase;

        StringBuilder sb = new StringBuilder();
        HashMap<Integer, Integer> hashMap = new HashMap<>();

        do {
            int next = RANDOM.nextInt(allStr.length());
            if (!Objects.isNull(hashMap.get(next))) {
                continue;
            }
            hashMap.put(next, CommonConstant.ONE);

            sb.append(allStr.charAt(next));
        } while (sb.length() < allStr.length());

        return sb.toString();
    }

    /**
     * 符号生成 3位
     */
    private static String symbolGenerator() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < CommonConstant.THREE; i++) {
            int r = RANDOM.nextInt(SYMBOL.length());
            char s = SYMBOL.charAt(r);
            sb.append(s);
        }

        return sb.toString();
    }

    /**
     * 数字生成 3位
     */
    private static String numberGenerator() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < CommonConstant.THREE; i++) {
            int r = RANDOM.nextInt(NUMBER.length());
            char s = NUMBER.charAt(r);
            sb.append(s);
        }

        return sb.toString();
    }

    /**
     * 大写生成 5位
     */
    private static String uppercaseGenerator() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < CommonConstant.FIVE; i++) {
            int r = RANDOM.nextInt(UPPERCASE.length());
            char s = UPPERCASE.charAt(r);
            sb.append(s);
        }
        return sb.toString();
    }

    /**
     * 小写生成 5位
     */
    private static String lowercaseGenerator() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < CommonConstant.FIVE; i++) {
            int r = RANDOM.nextInt(LOWERCASE.length());
            char s = LOWERCASE.charAt(r);
            sb.append(s);
        }
        return sb.toString();
    }
}
