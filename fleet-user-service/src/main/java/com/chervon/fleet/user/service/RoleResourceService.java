package com.chervon.fleet.user.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chervon.fleet.user.api.entity.vo.ResourceVo;
import com.chervon.fleet.user.entity.po.Resource;
import com.chervon.fleet.user.entity.po.Role;
import com.chervon.fleet.user.entity.po.RoleResource;

import java.util.Date;
import java.util.List;

/**
 * 角色资源表（权限表）服务接口
 *
 * <AUTHOR>
 * @since 2022-09-20 14:28:36
 * @description 
 */
public interface RoleResourceService extends IService<RoleResource> {
    /**
     * 根据角色Id列表查询对应资源列表 *
     * @param roleIds
     * @return
     */
    List<Resource> getResourceByRoles(List<Long> roleIds);

    /**
     * * 批量插入角色资源
     * @param list
     * @return
     */
    List<Long> insertBatch(List<RoleResource> list);

    /**
     * 插入角色资源关系 *
     * @param resourceIdList
     * @param role
     */
    void insertRoleResource(List<Long> resourceIdList, Role role);

    /**
     * 删除角色资源关系
     * @param listRoleIds
     * @return
     */
    Integer deleteRoleResourceRef(List<Long> listRoleIds, List<Long> listResourceIds);
}
