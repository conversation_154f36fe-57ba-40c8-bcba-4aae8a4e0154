package com.chervon.fleet.user.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * @Description: This is description
 * <AUTHOR>
 * @Date: 2020/8/27 16:17
 */
@Slf4j
public class Base64Util {

    /**
     * 解密
     *
     * @param data
     * @return
     */
    public static String decode(String data) {
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            if (null == data) {
                return null;
            }

            return new String(decoder.decode(data.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(String.format("字符串：%s，解密异常", data), e);
        }

        return null;
    }

    /**
     * 解密
     *
     * @param data
     * @return
     */
    public static byte[] decode2Byte(String data) {
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            if (null == data) {
                return null;
            }
            return decoder.decode(data.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error(String.format("字符串：%s，解密异常", data), e);
        }

        return null;
    }

    /**
     * 加密
     *
     * @param data
     * @return
     */
    public static String encode(String data) {
        Base64.Encoder encoder = Base64.getEncoder();
        try {
            if (null == data) {
                return null;
            }
            return new String(encoder.encode(data.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(String.format("字符串：%s，加密异常", data), e);
        }

        return null;
    }

    /**
     * 解密
     *
     * @param fileByte
     * @return
     */
    public static String encodeByte(byte[] fileByte) {
        if (Objects.isNull(fileByte) || fileByte.length < 1) {
            return null;
        }
        try {
            Base64.Encoder decoder = Base64.getEncoder();
            return new String(decoder.encode(fileByte), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("base64加密失败");
        }

        return null;
    }

}
