package com.chervon.fleet.user.filter;

import com.chervon.common.web.entity.ClientInfo;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.common.web.util.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * （通过统一过滤器）接收上游请求头传递到下游的请求头信息
 *
 * <AUTHOR> 2023/6/26
 */
@Slf4j
@Activate(group = {CommonConstants.PROVIDER})
public class DubboProviderFilter implements Filter, BaseFilter.Listener {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        try {
            ClientInfo clientInfo = UserContext.getClientInfo();
            clientInfo.setUserId((Long) RpcContext.getServerAttachment().getObjectAttachment(HeaderUtils.USER_ID));
            clientInfo.setUserName(RpcContext.getServerAttachment().getAttachment(HeaderUtils.USER_NAME));
            clientInfo.setName(RpcContext.getServerAttachment().getAttachment(HeaderUtils.NAME));
            clientInfo.setUserType((Integer) RpcContext.getServerAttachment().getObjectAttachment(HeaderUtils.USER_TYPE));
            clientInfo.setDeviceType((Integer) RpcContext.getServerAttachment().getObjectAttachment(HeaderUtils.DEVICE_TYPE));
            clientInfo.setDeviceId(RpcContext.getServerAttachment().getAttachment(HeaderUtils.DEVICE_ID));
            String language = RpcContext.getServerAttachment().getAttachment(HeaderUtils.LANGUAGE);
            //传递语言参数
            clientInfo.setLanguage(language);
            LocaleContextHolder.setLocale(new Locale(language));
            clientInfo.setTraceId(RpcContext.getServerAttachment().getAttachment(HeaderUtils.TRACE_ID));
            clientInfo.setCompanyId((Long) RpcContext.getServerAttachment().getObjectAttachment(HeaderUtils.COMPANY_ID));
            UserContext.setClientInfo(clientInfo);
            RpcContext.getServerAttachment().clearAttachments();
        } catch (Exception e) {
            log.error("Exception in process DubboConsumerFilter", e);
        }
        return invoker.invoke(invocation);
    }

    @Override
    public void onResponse(Result appResponse, Invoker<?> invoker, Invocation invocation) {
        UserContext.remove();
    }

    @Override
    public void onError(Throwable t, Invoker<?> invoker, Invocation invocation) {
        UserContext.remove();
    }
}
