package com.chervon.fleet.user.service.impl;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.JsonUtils;
import com.chervon.common.core.utils.UUIDUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.controller.I18nController;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.dto.MailRequestDto;
import com.chervon.fleet.user.api.entity.enums.MailBusinessEnum;
import com.chervon.fleet.user.api.entity.enums.UserTypeEnum;
import com.chervon.fleet.user.api.entity.enums.ValidationCodeTypeEnum;
import com.chervon.fleet.user.entity.consts.NumberConst;
import com.chervon.fleet.user.entity.consts.RedisConst;
import com.chervon.fleet.user.entity.consts.StringConst;
import com.chervon.fleet.user.service.SendMailService;
import com.chervon.fleet.user.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mail.MailSendException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件发送服务
 * <AUTHOR> 2022/9/20
 */
@RefreshScope
@Service
@Slf4j
public class SendMailServiceImpl implements SendMailService {
    @Resource
    private JavaMailSender javaMailSender;
    @Value("${spring.mail.from}")
    private String sendMailer;
    @Value("${mail.prefix.register-personal}")
    private String registerPersonal;
    @Value("${mail.prefix.reset-password}")
    private String resetPassword;
    @Value("${mail.prefix.register-invite}")
    private String registerInvite;
    private static final String PLACEHOLDER0="\\{0}";
    private static final String PLACEHOLDER1="\\{1}";
    private static final String PLACEHOLDER2="\\{2}";
    private static final String PLACEHOLDER3="\\{3}";
    private static final String PLACEHOLDER4="\\{4}";
    private static final String PLACEHOLDER5="\\{5}";
    /**
     * 将线程池注入进来
     */
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    private ResourceLoader resourceLoader;

    @Override
    public void validateMailRequestParam(MailRequestDto mailRequest) {
        Assert.notNull(mailRequest, ErrorCode.PARAMETER_NOT_PROVIDED, "mailRequest");
        Assert.notNull(mailRequest.getSendTo(), ErrorCode.PARAMETER_NOT_PROVIDED, "sendTo");
        Assert.notNull(mailRequest.getSubject(), ErrorCode.PARAMETER_NOT_PROVIDED, "subject");
        Assert.notNull(mailRequest.getText(), ErrorCode.PARAMETER_NOT_PROVIDED, "content");
    }

    @Override
    public void sendUserMailByBusiness(String email, MailBusinessEnum mailBusinessEnum,
                                       String firstName, String lastName,
                                       Long companyId, String companyName) {
        //请求参数验证
        Assert.hasText(email, ErrorCode.PARAMETER_NOT_PROVIDED, "email");
        Assert.isTrue(Pattern.matches(StringConst.EMAIL_FORMAT, email), ErrorCode.PARAMETER_FORMAT_ERROR, "email");
        //根据业务类型构建邮件主题，内容
        MailRequestDto mailRequestDto = buildMailRequestByBusiness(email, mailBusinessEnum, firstName, lastName, companyId, companyName);
        log.info("mailRequestDto: {}", mailRequestDto);
        //发送邮件
        sendHtmlMail(mailRequestDto);
    }

    @Override
    public void sendUserRoleUpdateNoticeMail(String email, Integer userType, String name) {
        MailRequestDto mailRequestDto = new MailRequestDto();
        String language = UserContext.getLanguage();
        final String emailTitleResourceId = StringConst.getEmailTitleResourceId(MailBusinessEnum.USER_ROLE_UPDATED.name());
        //读取静态国际化邮件标题
        String subject = I18nController.getResourceById(language,emailTitleResourceId);
        //读取静态国际化邮件内容
        String templateContent = I18nController.getFileResourceById(language,"role");
        String content = templateContent.replaceFirst(PLACEHOLDER0, name)
            .replaceFirst(PLACEHOLDER1, UserTypeEnum.getEnum(userType).getDesc());
        mailRequestDto.setSendTo(email)
            .setSubject(subject)
            .setText(content);
        sendHtmlMail(mailRequestDto);
    }

    /**
     * * 根据业务类型生成验证码，并发送业务邮件
     *
     * @param email            收件人地址
     * @param mailBusinessEnum 业务类型
     * @return 邮件对象
     */
    private MailRequestDto buildMailRequestByBusiness(String email, MailBusinessEnum mailBusinessEnum,
                                                      String firstName, String lastName,
                                                      Long companyId, String companyName) {
        //生成发送邮件的验证码并设置到redis缓存
        String verificationCode = generateMailVerificationCode(mailBusinessEnum, email, companyId);

        //读取业务对应的国际化资源id
        final String emailTitleResourceId = StringConst.getEmailTitleResourceId(mailBusinessEnum.name());
        String language = UserContext.getClientInfo().getLanguage();
        //读取静态国际化邮件标题
        String subject = I18nController.getResourceById(language,emailTitleResourceId);
        //读取静态国际化邮件内容
        String content = "";
        String info = "email=" + email + "&verificationCode=" + verificationCode;
        switch (mailBusinessEnum) {
            case USER_REGISTER: {
                content = buildUserRegister(email, language, info);
                break;
            }
            case FORGET_PASSWORD: {
                content = buildForgetPassword(firstName, lastName, language, info);
                break;
            }
            case USER_INVITATION: {
                content = buildInventUser(firstName, lastName, companyId, companyName, language, info);
                break;
            }
            default:
                break;
        }
        MailRequestDto mailRequestDto = new MailRequestDto();
        mailRequestDto.setSendTo(email)
            .setSubject(subject)
            .setText(content);
        return mailRequestDto;
    }

    /**
     * 生成用户邀请注册的邮件内容
     * @param firstName
     * @param lastName
     * @param companyId
     * @param companyName
     * @param language
     * @param info
     * @return
     */
    private String buildInventUser(String firstName, String lastName, Long companyId, String companyName, String language, String info) {
        String content;
        String inviteTemplateContent = I18nController.getFileResourceById(language,"invite");
        String appendInfo = info + "&firstName=" +
                firstName + "&lastName=" + lastName + "&companyId=" + companyId + "&companyName=" + companyName;
        String url = registerInvite + appendInfo;
        String userName=UserContext.getName();
        content = inviteTemplateContent.replaceFirst(PLACEHOLDER0, firstName + ' ' + lastName)
            .replaceFirst(PLACEHOLDER1, userName).replaceFirst(PLACEHOLDER2, Matcher.quoteReplacement(companyName))
            .replaceFirst(PLACEHOLDER3, url).replaceFirst(PLACEHOLDER4, url).replaceFirst(PLACEHOLDER5, url);
        return content;
    }

    /**
     * 生成忘记密码的邮件内容
     * @param firstName
     * @param lastName
     * @param language
     * @param info
     * @return
     */
    private String buildForgetPassword(String firstName, String lastName, String language, String info) {
        String content;
        String forgetTemplateContent = I18nController.getFileResourceById(language,"forget");
        String url = resetPassword + info;
        content = forgetTemplateContent.replaceFirst(PLACEHOLDER0, firstName + ' ' + lastName)
            .replaceFirst(PLACEHOLDER1, url).replaceFirst(PLACEHOLDER2, url).replaceFirst(PLACEHOLDER3, url);
        return content;
    }

    /**
     * 生成用户注册的邮件内容
     * @param email
     * @param language
     * @param info
     * @return
     */
    private String buildUserRegister(String email, String language, String info) {
        String content;
        String registerTemplateContent = I18nController.getFileResourceById(language,"register");
        String url = registerPersonal + info;
        // 邮箱@符号前面的前缀
        String emailPrefix = findEmailName(email);
        content = registerTemplateContent.replaceFirst(PLACEHOLDER0, emailPrefix)
            .replaceFirst(PLACEHOLDER1, url).replaceFirst(PLACEHOLDER2, url).replaceFirst(PLACEHOLDER3, url);
        return content;
    }

    /**
     * 获取邮箱的前缀姓名部分
     * @param email
     * @return
     */
    public static String findEmailName(String email) {
        String regex = "^([^@]+)@";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(email);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return email;
    }

    /**
     * * 生成发送邮件的验证码并设置到redis缓存
     *
     * @param mailBusinessEnum 邮箱类型枚举
     * @param email            邮箱地址
     * @param companyId        公司ID,仅邀请注册时有效,否则传空
     * @return 随机验证码
     */
    private String generateMailVerificationCode(MailBusinessEnum mailBusinessEnum, String email, Long companyId) {
        String mailCodeKey;
        if (mailBusinessEnum == MailBusinessEnum.USER_INVITATION) {
            mailCodeKey = RedisConst.getInviteUserMailCodeKey(email, companyId);
        } else {
            mailCodeKey = RedisConst.getUserMailCodeKey(email, mailBusinessEnum.name());
        }
        final String verificationCode = getVerificationCode(ValidationCodeTypeEnum.UUID);
        RedisUtils.setWithExpire(mailCodeKey, verificationCode, RedisConst.USER_MAIL_CODE_EXPIRE_SECOND);
        return verificationCode;
    }

    /**
     * * 根据验证码类型生成指定类型的验证码
     *
     * @param codeTypeEnum 验证码类型枚举
     * @return 随机UUID
     */
    private String getVerificationCode(ValidationCodeTypeEnum codeTypeEnum) {
        if (codeTypeEnum==ValidationCodeTypeEnum.NUMBER) {
            return RandomStringUtils.randomNumeric(NumberConst.MAIL_VERIFICATION_CODE_LEN);
        } else {
            return UUIDUtils.randomUUID();
        }
    }
    /**
     * * 发送邮件工具方法
     *
     * @param mailRequestDto 邮件Dto
     */
    @Override
    public void sendSimpleMail(MailRequestDto mailRequestDto) {
        log.info("发送邮件开始:{}", JsonUtils.toJson(mailRequestDto));
        SimpleMailMessage message = new SimpleMailMessage();
        validateMailRequestParam(mailRequestDto);
        //邮件发件人
        message.setFrom(sendMailer);
        //邮件收件人 1或多个
        message.setTo(mailRequestDto.getSendTo().split(","));
        //邮件主题
        message.setSubject(mailRequestDto.getSubject());
        //邮件内容
        message.setText(mailRequestDto.getText());
        //邮件发送时间
        message.setSentDate(DateUtil.getDate(LocalDateTime.now()));
        long startTime = System.currentTimeMillis();
        javaMailSender.send(message);
        long endTime = System.currentTimeMillis();
        log.info("发送邮件成功:{}->{},耗时毫秒：{}", sendMailer, mailRequestDto,endTime - startTime);
    }


    @Override
    public String sendHtmlMail(MailRequestDto mailRequest) {
        String sendResult=null;
        try {
            final MimeMessage message = buildSendMimeMessage(mailRequest);
            if(mailRequest.isAsyncSend()){
                threadPoolTaskExecutor.execute(() -> {
                    log.info(doMailSend(mailRequest, message));
                });
                return "async send";
            }else{
                sendResult= doMailSend(mailRequest, message);
                log.info(sendResult);
                return sendResult;
            }
        } catch (Exception e) {
            String err=" send mail error:{}";
            log.error(err,e);
            return sendResult+err+e.getMessage();
        }
    }

    @NotNull
    private MimeMessage buildSendMimeMessage(MailRequestDto mailRequest) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        validateMailRequestParam(mailRequest);
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        //邮件发件人
        helper.setFrom(sendMailer);
        //邮件收件人 1或多个
        helper.setTo(mailRequest.getSendTo().split(","));
        //邮件主题
        helper.setSubject(mailRequest.getSubject());
        //邮件内容
        helper.setText(mailRequest.getText(), true);
        //邮件发送时间
        helper.setSentDate(DateUtil.getDate(LocalDateTime.now()));

        String filePath = mailRequest.getFilePath();
        if (StringUtils.hasText(filePath)) {
            FileSystemResource file = new FileSystemResource(new File(filePath));
            String fileName = filePath.substring(filePath.lastIndexOf(File.separator));
            helper.addAttachment(fileName, file);
        }
        return message;
    }

    private String doMailSend(MailRequestDto mailRequest, MimeMessage message) {
        StringBuilder sb=new StringBuilder();
        try {
            String preSend="准备发送邮件："+mailRequest.getSendTo();
            sb.append(preSend);
            long startTime = System.currentTimeMillis();
            javaMailSender.send(message);
            long endTime = System.currentTimeMillis();
            String sendResult= MessageFormat.format("发送邮件成功:发送耗时毫秒：{0}，发送内容：{1}",endTime - startTime, JsonUtils.toJson(mailRequest));
            sb.append(sendResult);
        } catch (MailSendException e) {
            mailErrorInfo(mailRequest, e, sb);
        }catch (Exception e) {
            mailErrorInfo(mailRequest, e, sb);
        }
        return sb.toString();
    }

    private static void mailErrorInfo(MailRequestDto mailRequest, Exception e, StringBuilder sb) {
        String errorMsg="发送邮件失败,发送内容："+JsonUtils.toJson(mailRequest)+"异常信息：{}";
        log.error(errorMsg, e);
        sb.append(errorMsg+ e.getMessage());
    }

    /**
     * 发送富文本文件模板邮件
     *
     * @param email 邮箱
     */
    @Override
    public void sendMailByFileTemplate(String email) {
        MailRequestDto mailRequest = new MailRequestDto();
        String content = getContent();
        mailRequest.setSubject("Welcome to Kress Partner !").setText(content).setSendTo(email);
        sendHtmlMail(mailRequest);
    }

    private String getContent() {
        //加载邮件html模板
        String fileName = "classpath:emailTemplate/add-dealer.html";
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        try (InputStream inputStream = resourceLoader.getResource(fileName).getInputStream();
             BufferedReader fileReader = new BufferedReader(new InputStreamReader(inputStream))) {
            while ((line = fileReader.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (Exception e) {
            log.error("读取邮件html模板失败，fileName[{}]:{}", fileName, e.getMessage());
        }
        return stringBuilder.toString();
    }

}

