package com.chervon.fleet.user.rpc;

import com.chervon.common.core.error.ErrorCode;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.Assert;
import com.chervon.common.core.utils.BeanCopyUtils;
import com.chervon.common.redis.utils.RedisUtils;
import com.chervon.common.web.enums.LanguageEnum;
import com.chervon.common.web.util.HeaderUtils;
import com.chervon.common.web.util.UserContext;
import com.chervon.fleet.user.api.entity.dto.LoginDto;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import com.chervon.fleet.user.api.entity.enums.DeviceTypeEnum;
import com.chervon.fleet.user.api.entity.enums.EnableEnum;
import com.chervon.fleet.user.api.entity.error.UserErrorCodeEnum;
import com.chervon.fleet.user.api.entity.query.UserQuery;
import com.chervon.fleet.user.api.entity.vo.CompanyVo;
import com.chervon.fleet.user.api.entity.vo.LoginResultVo;
import com.chervon.fleet.user.api.service.RemoteAuthService;
import com.chervon.fleet.user.entity.consts.RedisConst;
import com.chervon.fleet.user.entity.dto.RedisUserLoginLog;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.service.AppSettingService;
import com.chervon.fleet.user.service.CompanyService;
import com.chervon.fleet.user.service.RequestListService;
import com.chervon.fleet.user.service.UserService;
import com.chervon.fleet.user.utils.RSAUtil;
import com.chervon.fleet.user.utils.SecurityUtil;
import com.chervon.fleet.user.utils.jwt.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * fleet用户表服务接口实现
 *
 * <AUTHOR>
 * @description
 * @since 2022-09-20 14:28:36
 */
@Slf4j
@DubboService
@Service
public class RemoteAuthServiceImpl implements RemoteAuthService {
    @Autowired
    private JwtUtils jwtUtils;
    @Lazy
    @Autowired
    private UserService userService;
    @Autowired
    private RequestListService requestListService;
    @Lazy
    @Autowired
    private CompanyService companyService;

    @Value("${user.login.web}")
    private Integer webLoginLimit;
    @Value("${user.login.app}")
    private Integer appLoginLimit;

    @Autowired
    private AppSettingService appSettingService;

    /**
     * 获取公钥
     *
     * @return 公钥字符串
     */
    @Override
    public String getPublicKey() {
        return RSAUtil.PUBLIC_KEY;
    }

    public void loginParamValidate(LoginDto loginDto) {
        Assert.notNull(loginDto, ErrorCode.PARAMETER_NOT_PROVIDED, "loginDto");
        Assert.hasText(loginDto.getUserName(), ErrorCode.PARAMETER_NOT_PROVIDED, "userName");
        Assert.hasText(loginDto.getPassword(), ErrorCode.PARAMETER_NOT_PROVIDED, "password");
        Integer deviceType = UserContext.getDeviceType();
        Assert.notNull(deviceType, ErrorCode.PARAMETER_NOT_PROVIDED,"deviceType");
        DeviceTypeEnum de = DeviceTypeEnum.getEnum(deviceType);
        Assert.notNull(de, ErrorCode.PARAMETER_FORMAT_ERROR, "deviceType");

        String language = HeaderUtils.getLanguage();
        Assert.notNull(language, ErrorCode.PARAMETER_NOT_PROVIDED, "lang");
        LanguageEnum languageEnum = LanguageEnum.getEnumByCode(language.toLowerCase());
        Assert.notNull(languageEnum, ErrorCode.PARAMETER_FORMAT_ERROR, "languageEnum");

        Assert.notNull(loginDto, UserErrorCodeEnum.USER_NOT_EXIST);
        Assert.hasText(loginDto.getUserName(), ErrorCode.PARAMETER_NOT_PROVIDED, "userName");
        Assert.hasText(loginDto.getPassword(), UserErrorCodeEnum.PASSWORD_NULL);
    }


    @Override
    public LoginResultVo login(LoginDto loginDto) {
        Integer deviceType = UserContext.getDeviceType();
        String deviceId = UserContext.getDeviceId();

        //登录请求参数验证
        loginParamValidate(loginDto);
        //获取前端请求参数用户名和密码
        String userName = loginDto.getUserName();
        String password = loginDto.getPassword();
        //验证用户是否存在，及是否启用
        User fleetUser = validateUserExistAndGet(userName);
        //验证用户状态是否已注销
        validateUserStatus(fleetUser);
        //验证密码
        checkPassword(password, fleetUser);
        String loginLogKey = RedisConst.getUserLoginLogKey(fleetUser.getId());
        // 校验登录次数限制
        checkLoginLimit(loginLogKey, deviceType, deviceId, fleetUser.getId());
        //构建登录返回信息
        LoginResultVo loginInfoDto = BeanCopyUtils.copyProperties(fleetUser, LoginResultVo.class);
        //查询公司租户信息
        final CompanyVo companyVo = companyService.getDetail(fleetUser.getCompanyId());
        if (!Objects.isNull(companyVo)) {
            loginInfoDto.setCompanyName(companyVo.getCompanyName());
        }
        loginInfoDto.setUserType(fleetUser.getUserType());
        //生成令牌
        String jwtToken = jwtUtils.computeJwt(fleetUser.getSecret(), loginInfoDto, deviceType);
        //设置令牌
        loginInfoDto.setToken(jwtToken);
        //记录redis缓存token及过期时间
        JwtUtils.saveLoginRedisToken(fleetUser.getId(), fleetUser.getSecret(), jwtToken);
        // redis缓存记录当前登录信息,方便根据userId查询登录记录
        String fleetRedisTokenKey = RedisConst.getFleetRedisTokenKey(deviceType, deviceId, fleetUser.getId());
        RedisUtils.setCacheMapValue(loginLogKey, deviceId, new RedisUserLoginLog(fleetRedisTokenKey, deviceType, System.currentTimeMillis()));
        // 异步处理用户设置的同意协议版本部分
        appSettingService.handleAgreementAfterLogin(DeviceTypeEnum.getEnum(deviceType), loginInfoDto.getCompanyId(), loginInfoDto.getId());
        //返回登录结果
        return loginInfoDto;
    }

    /**
     * 检测超过最大登录限制时抛异常,同时清除login:limit中过期的HashKey
     *
     * @param loginLogKey 登录记录redisKey
     * @param deviceType  登录设备类型
     * @param deviceId    登录设备ID
     * @param userId      用户ID
     */
    public void checkLoginLimit(String loginLogKey, Integer deviceType, String deviceId, Long userId) {
        Map<String, RedisUserLoginLog> cacheMap = RedisUtils.getCacheMap(loginLogKey);
        long currentTimestamp = System.currentTimeMillis();
        // 根据deviceType获取登录数限制
        int loginLimit = deviceType == DeviceTypeEnum.PC.getType() ? webLoginLimit : appLoginLimit;
        cacheMap.entrySet().stream().filter(cache -> cache.getValue().getLoginTime() + JwtUtils.getDeviceTokenExpireSecond(deviceType) * 1000L < currentTimestamp)
                .forEach(cache -> RedisUtils.delCacheMapValue(loginLogKey, cache.getKey()));
        List<RedisUserLoginLog> listLogin = cacheMap.values().stream().filter(a -> Objects.equals(a.getDeviceType(), deviceType)).collect(Collectors.toList());
        if (listLogin.size() + 1 > loginLimit) {
            log.error("login -> 登录已超过最大限制,本次登录 deviceId:{}, deviceType:{}, userId:{}", deviceId, deviceType, userId);
            throw new ServiceException(UserErrorCodeEnum.USER_LOGIN_TOO_MANY_DEVICES);
        }
    }

    /**
     * * 注销用户登录状态
     *
     * @return 注销结果
     */
    @Override
    public Boolean logout() {
        Long userId = UserContext.getUserId();
        if (Objects.isNull(userId)) {
            return false;
        }
        log.info("logout userId->{}", userId);
        Integer deviceType = UserContext.getDeviceType();
        DeviceTypeEnum device = DeviceTypeEnum.getEnum(deviceType);
        String deviceId = UserContext.getDeviceId();
        log.info("logout deviceType->{}", deviceType);
        if (Objects.isNull(device)) {
            return false;
        }
        String key = RedisConst.getFleetRedisTokenKey(deviceType, deviceId, userId);
        if (!RedisUtils.hasKey(key)) {
            return true;
        }
        RedisUtils.deleteObject(key);
        RedisUtils.delCacheMapValue(RedisConst.getUserLoginLogKey(userId), deviceId);
        return true;
    }

    private void checkPassword(String password, User fleetUser) {
        //解密前端传过来的加密密码,通过指定的密钥解密密码密文
        String decryptPwd = RSAUtil.decrypt(password);
        if (!StringUtils.hasText(decryptPwd)) {
            log.error("RemoteAuthServiceImpl#checkPassword -> 用户({})密码解密失败", fleetUser.getUserName());
            throw new ServiceException(UserErrorCodeEnum.LOGIN_PASSWORD_OR_NAME_ERROR);
        }
        //md5后密码密文
        String passwordSecret = SecurityUtil.getDbMd5Password(decryptPwd, fleetUser.getSecret());
        //比对密码密文与数据库中是否相符
        if (!passwordSecret.equals(fleetUser.getPassword())) {
            log.error("RemoteAuthServiceImpl#checkPassword -> 用户({})密码与数据库比对失败", fleetUser.getUserName());
            throw new ServiceException(UserErrorCodeEnum.LOGIN_PASSWORD_OR_NAME_ERROR);
        }
    }

    /**
     * * 验证用户信息存在性
     *
     * @param userName
     * @return
     */
    private User validateUserExistAndGet(String userName) {
        final User fleetUser = getUserByUserName(userName);
        if (Objects.isNull(fleetUser)) {
            throw new ServiceException(UserErrorCodeEnum.LOGIN_PASSWORD_OR_NAME_ERROR);
        }
        return fleetUser;
    }

    /**
     * * 验证用户状态
     *
     * @param fleetUser
     */
    private void validateUserStatus(User fleetUser) {
        if (fleetUser.getAccountStatus().equals(AccountStatusEnum.CANCELED.getType())) {
            log.error("RemoteAuthServiceImpl#validateUserStatus -> 要登录用户已被禁用: {}", fleetUser.getId());
            throw new ServiceException(UserErrorCodeEnum.LOGIN_PASSWORD_OR_NAME_ERROR);
        }
        if (!fleetUser.getIsEnabled().equals(EnableEnum.ENABLED.getType())) {
            log.error("RemoteAuthServiceImpl#validateUserStatus -> 要登录用户已停用: {}", fleetUser.getId());
            throw new ServiceException(UserErrorCodeEnum.ACCOUNT_IS_NOT_ENABLED);
        }
    }

    private User getUserByUserName(String userName) {
        UserQuery userQuery = new UserQuery();
        userQuery.setUserName(userName);
        userQuery.setIsDeleted(false);
        userQuery.setListAccountStatus(Arrays.asList(AccountStatusEnum.ACTIVATED.getType(), AccountStatusEnum.NOT_ACTIVATED.getType()));
        List<User> list = userService.getList(userQuery);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if(list.size() == 1){
            return list.get(0);
        }
        // 排除没有设置密码的，也就是之前的free用户
        list.removeIf(user -> !StringUtils.hasText(user.getSecret()));
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        // 已激活优先
        list = list.stream().sorted(Comparator.comparing(User::getAccountStatus).reversed()).collect(Collectors.toList());
        return list.get(0);
    }

    /**
     * 请求黑白名单验证
     *
     * @param method
     * @param url
     * @return
     */
    @Override
    public Boolean checkRequestList(String method, String url) {
        return requestListService.checkRequestList(method, url);
    }

    /**
     * token登录鉴权
     *
     * @param token
     */
    @Override
    public LoginResultVo getUserInfoByJwt(String token) {
        return JwtUtils.getUserInfo(token);
    }

    @Override
    public LoginResultVo getUserInfoByDb(String token) {
        LoginResultVo userInfo = JwtUtils.getUserInfo(token);
        User fleetUser = userService.getById(userInfo.getId());
        //验证用户状态是否已注销
        validateUserStatus(fleetUser);
        //构建登录返回信息
        LoginResultVo loginInfoDto = BeanCopyUtils.copyProperties(fleetUser, LoginResultVo.class);
        //查询公司租户信息
        final CompanyVo companyVo = companyService.getDetail(fleetUser.getCompanyId());
        if (!Objects.isNull(companyVo)) {
            loginInfoDto.setCompanyName(companyVo.getCompanyName());
        }
        loginInfoDto.setUserType(fleetUser.getUserType());
        //设置令牌
        loginInfoDto.setToken(token);
        return loginInfoDto;
    }

    @Override
    public LoginResultVo refresh(String token) {
        LoginResultVo userInfo = JwtUtils.getUserInfo(token);
        User fleetUser = userService.getById(userInfo.getId());
        //验证用户状态是否已注销
        validateUserStatus(fleetUser);
        //构建登录返回信息
        LoginResultVo loginInfoDto = BeanCopyUtils.copyProperties(fleetUser, LoginResultVo.class);
        //查询公司租户信息
        final CompanyVo companyVo = companyService.getDetail(fleetUser.getCompanyId());
        if (!Objects.isNull(companyVo)) {
            loginInfoDto.setCompanyName(companyVo.getCompanyName());
        }
        loginInfoDto.setUserType(fleetUser.getUserType());
        //生成令牌
        String jwtToken = jwtUtils.computeJwt(fleetUser.getSecret(), loginInfoDto, UserContext.getDeviceType());
        //设置令牌
        loginInfoDto.setToken(jwtToken);
        //记录redis缓存token及过期时间
        JwtUtils.saveLoginRedisToken(fleetUser.getId(), fleetUser.getSecret(), jwtToken);
        return loginInfoDto;
    }
}