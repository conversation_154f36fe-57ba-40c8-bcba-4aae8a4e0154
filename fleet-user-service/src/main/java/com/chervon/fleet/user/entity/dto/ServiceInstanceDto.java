package com.chervon.fleet.user.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:00
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "服务实例数据传输对象", description = "服务实例信息")
public class ServiceInstanceDto implements Serializable {
    private static final long serialVersionUID = 1840773351968038899L;
    /**
     * 服务名称
     */
    @ApiModelProperty(value = "服务名称")
    private String applicationName;
    /**
     * 服务PATH
     */
    @ApiModelProperty(value = "服务PATH")
    private String contextPath;
    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    private String ip;
    /**
     * 端口
     */
    @ApiModelProperty(value = "端口")
    private String port;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "Tags信息")
    private String tags;
    /**
     * 运行时间
     */
    @ApiModelProperty(value = "运行时间")
    private Long runningTime;
    /**
     * heartbeat time.
     */
    @ApiModelProperty(value = "心跳时间")
    private Long heartbeatTime;
}
