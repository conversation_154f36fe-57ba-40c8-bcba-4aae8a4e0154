package com.chervon.fleet.user.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户隐私协议授权表(t_user_agreement)实体类
 *
 * <AUTHOR>
 * @since 2023-06-25 10:38:34
 * @description 
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("t_user_agreement")
public class UserAgreement extends Model<UserAgreement> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private Long id;
    /**
     * 创建人userId-userName
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;
    /**
     * 更新人userId-userName
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifyTime;
    /**
     * 是否删除：0 正常  1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDeleted;
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 用户邮箱
     */
    private String userEmail;
    /**
     * 用户协议id
     */
    private Long userAgreementId;
    /**
     * 用户协议版本
     */
    private String userAgreementVersion;
    /**
     * 隐私协议id
     */
    private Long privacyAgreementId;
    /**
     * 隐私协议版本
     */
    private String privacyAgreementVersion;
    /**
     * 同意时间
     */
    private Long agreeTime;
    /**
     * 撤销时间
     */
    private Long withdrawTime;
    /**
     * 状态， 1  同意  2  撤销
     */
    private Integer status;

}