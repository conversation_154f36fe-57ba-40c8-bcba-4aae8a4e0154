package com.chervon.fleet.user.rpc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chervon.common.core.domain.PageResult;
import com.chervon.common.core.exception.ServiceException;
import com.chervon.common.core.utils.StringUtils;
import com.chervon.fleet.user.api.entity.dto.CompanyAdminChangeRecordDto;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.dto.FleetUserPageDto;
import com.chervon.fleet.user.api.entity.enums.AccountStatusEnum;
import com.chervon.fleet.user.api.entity.enums.CompanyStatusEnum;
import com.chervon.fleet.user.api.entity.enums.UserEnabledEnum;
import com.chervon.fleet.user.api.entity.vo.*;
import com.chervon.fleet.user.api.service.RemoteFleetUserCenterService;
import com.chervon.fleet.user.entity.enums.IsDeletedEnum;
import com.chervon.fleet.user.entity.po.AdminTransferLog;
import com.chervon.fleet.user.entity.po.Company;
import com.chervon.fleet.user.entity.po.User;
import com.chervon.fleet.user.service.AdminTransferLogService;
import com.chervon.fleet.user.service.CompanyService;
import com.chervon.fleet.user.service.UserManageService;
import com.chervon.fleet.user.service.UserService;
import com.chervon.fleet.web.api.service.RemoteFleetDeviceService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/2 17:39
 */
@Service
@DubboService
@Slf4j
@AllArgsConstructor
public class RemoteFleetUserCenterServiceImpl implements RemoteFleetUserCenterService {

    private final CompanyService companyService;

    private final UserService userService;

    private final UserManageService userManageService;

    private final AdminTransferLogService adminTransferLogService;

    @DubboReference
    private RemoteFleetDeviceService remoteFleetDeviceService;

    @Override
    public PageResult<FleetCompanyVo> companyPage(FleetCompanyPageDto req) {
        IPage<FleetCompanyVo> search = companyService.search(new Page<>(req.getPageNum(), req.getPageSize()), req);
        return PageResult.of(search.getCurrent(), search.getSize(), search.getTotal(), search.getRecords());
    }

    @Override
    public List<FleetCompanyVo> companyList(String companyId, String companyName) throws ServiceException {
        List<Company> list = companyService.list(new LambdaQueryWrapper<Company>()
                .like(StringUtils.isNotEmpty(companyId), Company::getId, companyId)
                .like(StringUtils.isNotEmpty(companyName), Company::getCompanyName, companyName)
                .ne(Company::getStatus, CompanyStatusEnum.CANCELED.getType())
                .orderByDesc(Company::getCreateTime));
        return list.stream().map(e -> {
            FleetCompanyVo vo = new FleetCompanyVo();
            vo.setCompanyId(e.getId()).setCompanyName(e.getCompanyName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public FleetCompanyVo companyDetail(Long companyId) {
        FleetCompanyVo res = new FleetCompanyVo();
        if (companyId == null) {
            return res;
        }
        Company company = companyService.getById(companyId);
        if (company == null) {
            return res;
        }
        res.setCompanyId(company.getId()).setAdminId(company.getUserId()).setCompanyName(company.getCompanyName())
                .setCompanyState(company.getStatus() == null ? null : company.getStatus().toString()).setRegisterTime(company.getCreateTime());
        if (company.getUserId() == null) {
            return res;
        }
        User user = userService.getById(company.getUserId());
        if (user == null) {
            return res;
        }
        res.setAdminEmail(user.getEmail()).setAdminFirstName(user.getFirstName()).setAdminLastName(user.getLastName());
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void companyDeactivate(Long companyId) {
        if (companyId == null) {
            return;
        }
        Company company = companyService.getById(companyId);
        if (company == null) {
            return;
        }
        company = new Company();
        company.setId(companyId);
        company.setStatus(CompanyStatusEnum.STOP_USING.getType());
        companyService.updateById(company);
        // 租户也停用
        userService.update(new User(), new LambdaUpdateWrapper<User>().eq(User::getCompanyId, companyId).set(User::getIsEnabled, UserEnabledEnum.DEACTIVATE.getType()));
        // 设置租户下的所有用户token失效
        List<User> list = userService.list(new LambdaQueryWrapper<User>().eq(User::getCompanyId, companyId));
        list.forEach(e -> userManageService.logoutAllDevicesByUserId(e.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void companyEnable(Long companyId) {
        if (companyId == null) {
            return;
        }
        Company company = companyService.getById(companyId);
        if (company == null) {
            return;
        }
        company = new Company();
        company.setId(companyId);
        company.setStatus(CompanyStatusEnum.NORMAL.getType());
        companyService.updateById(company);
        // 租户也启用
        userService.update(new User(), new LambdaUpdateWrapper<User>().eq(User::getCompanyId, companyId).set(User::getIsEnabled, UserEnabledEnum.ACTIVE.getType()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void companyLogoff(Long companyId) {
        if (companyId == null) {
            return;
        }
        Company company = companyService.getById(companyId);
        if (company == null) {
            return;
        }
        company = new Company();
        company.setId(companyId);
        company.setStatus(CompanyStatusEnum.CANCELED.getType());
        company.setUserId(null);
        //合规要求：逻辑删除租户
        companyService.removeById(companyId);

        // 设置租户下的所有用户token失效
        List<User> list = userService.list(new LambdaQueryWrapper<User>().eq(User::getCompanyId, companyId));
        list.forEach(e -> userManageService.logoutAllDevicesByUserId(e.getId()));

        //合规要求：移除租户下所有用户
        final LambdaUpdateWrapper<User> batRemoveUser = new LambdaUpdateWrapper<User>()
                .eq(User::getCompanyId, companyId);
        userService.remove(batRemoveUser);
        // 解绑租户和设备关系
        remoteFleetDeviceService.unbindByCompanyId(companyId);
    }

    @Override
    public PageResult<AdminChangeRecordVo> companyAdminChangeRecord(CompanyAdminChangeRecordDto req) {
        PageResult<AdminChangeRecordVo> res = new PageResult<>(req.getPageNum(), req.getPageSize());
        if (req.getCompanyId() == null) {
            return res;
        }
        IPage<AdminTransferLog> page = adminTransferLogService.page(new Page<>(req.getPageNum(), req.getPageSize()),
                new LambdaQueryWrapper<AdminTransferLog>().eq(AdminTransferLog::getCompanyId, req.getCompanyId())
                        .orderByDesc(AdminTransferLog::getModifyTime));
        res.setPageNum(page.getCurrent());
        res.setPageSize(page.getSize());
        res.setTotal(page.getTotal());
        List<AdminChangeRecordVo> collect = page.getRecords().stream().map(e -> {
            AdminChangeRecordVo vo = new AdminChangeRecordVo();
            vo.setAdminId(e.getUserId()).setAdminEmail(e.getEmail()).setChangeTime(e.getModifyTime())
                    .setAdminFirstName(e.getUserFirstName()).setAdminLastName(e.getUserLastName());
            return vo;
        }).collect(Collectors.toList());
        res.setList(collect);
        return res;
    }

    @Override
    public List<FleetCompanyExcel> companyListData(FleetCompanyPageDto req) {
        return companyService.searchList(req).stream().map(e -> {
            FleetCompanyExcel excel = new FleetCompanyExcel();
            BeanUtils.copyProperties(e, excel);
            excel.setRegisterTime(e.getRegisterTime() == null ? null : e.getRegisterTime().toString());
            return excel;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<FleetUserVo> userPage(FleetUserPageDto req) {
        IPage<FleetUserVo> page = userManageService.search(new Page<>(req.getPageNum(), req.getPageSize()), req);
        PageResult<FleetUserVo> res = new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal());
        res.setList(page.getRecords());
        return res;
    }


    @Override
    public FleetUserVo userDetail(Long userId) {
        FleetUserVo res = new FleetUserVo();
        User user = userManageService.getOneWithDeleted(userId);
        if (user == null) {
            return res;
        }
        res.setUserId(user.getId())
                .setUserEmail(user.getEmail())
                .setUserFirstName(user.getFirstName())
                .setUserLastName(user.getLastName())
                .setUserRole(user.getUserType() == null ? null : String.valueOf(user.getUserType()))
                .setCompanyId(user.getCompanyId())
                .setUserSourceType(user.getSourceType() == null ? null : String.valueOf(user.getSourceType()))
                .setRegisterTime(user.getCreateTime())
                .setActiveTime(user.getActivationTime())
                .setIsEnabled(user.getIsEnabled() == null ? "0" : user.getIsEnabled().toString());
        if (user.getIsDeleted() == IsDeletedEnum.DELETED.getType()) {
            res.setUserState(String.valueOf(AccountStatusEnum.CANCELED.getType()));
        } else {
            res.setUserState(user.getAccountStatus() == null ? null : String.valueOf(user.getAccountStatus()));
        }
        setCompanyName(user, res);
        if (user.getParentId() == null || user.getParentId() == 0) {
            return res;
        }
        final FleetUserVo res1 = getFleetUserVo(user, res);
        if (res1 != null){
            return res1;
        }
        return res;
    }

    @Nullable
    private FleetUserVo getFleetUserVo(User user, FleetUserVo res) {
        User parent = userManageService.getOneWithDeleted(user.getParentId());
        if (parent == null) {
            return res;
        }else{
            res.setInvitationEmail(parent.getEmail());
        }
        return null;
    }

    private void setCompanyName(User user, FleetUserVo res) {
        if (user.getCompanyId() != null) {
            Company company = companyService.getById(user.getCompanyId());
            if (company != null) {
                res.setCompanyName(company.getCompanyName());
            }
        }
    }

    @Override
    public void userDeactivate(Long userId) {
        if (userId == null) {
            return;
        }
        User user = userService.getById(userId);
        if (user == null) {
            return;
        }
        user = new User();
        user.setId(userId);
        user.setIsEnabled(UserEnabledEnum.DEACTIVATE.getType());
        userService.updateById(user);
        // 设置用户token失效
        userManageService.logoutAllDevicesByUserId(userId);
    }

    @Override
    public void userEnable(Long userId) {
        if (userId == null) {
            return;
        }
        User user = userService.getById(userId);
        if (user == null) {
            return;
        }
        if (user.getCompanyId() != null) {
            // 判读租户是否启用
            Company company = companyService.getById(user.getCompanyId());
            if (company == null || company.getEnabled() == CompanyStatusEnum.STOP_USING.getType()) {
                return;
            }
        }
        user = new User();
        user.setId(userId);
        user.setIsEnabled(UserEnabledEnum.ACTIVE.getType());
        userService.updateById(user);
    }

    @Override
    public List<FleetUserExcel> userListData(FleetUserPageDto req) {
        List<FleetUserVo> list = userManageService.searchList(req);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<FleetUserExcel> res = new ArrayList<>();
        for (FleetUserVo e : list) {
            FleetUserExcel excel = new FleetUserExcel();
            excel.setUserId(e.getUserId());
            excel.setUserEmail(e.getUserEmail());
            excel.setUserFirstName(e.getUserFirstName());
            excel.setUserLastName(e.getUserLastName());
            excel.setUserRole(e.getUserRole());
            excel.setCompanyId(e.getCompanyId());
            excel.setCompanyName(e.getCompanyName());
            excel.setUserSourceType(e.getUserSourceType());
            excel.setInvitationEmail(e.getInvitationEmail());
            excel.setUserState(e.getUserState());
            excel.setRegisterTime(e.getRegisterTime() == null ? null : String.valueOf(e.getRegisterTime()));
            excel.setActiveTime(e.getActiveTime() == null ? null : String.valueOf(e.getActiveTime()));
            excel.setIsEnabled(e.getIsEnabled());
            res.add(excel);
        };
        return res;
    }

    @Override
    public void userLogoff(Long userId) {
        userManageService.logoutAllDevicesByUserId(userId);
    }

}
