package com.chervon.fleet.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chervon.fleet.user.api.entity.dto.FleetCompanyPageDto;
import com.chervon.fleet.user.api.entity.vo.FleetCompanyVo;
import com.chervon.fleet.user.entity.po.Company;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户企业表(t_company)数据Mapper
 *
 * <AUTHOR>
 * @description
 * @since 2023-06-25 10:38:34
 */
@Mapper
public interface CompanyMapper extends BaseMapper<Company> {

    /**
     * 用户中心-查询租户信息
     *
     * @param page   分页容器
     * @param search 查询条件
     * @return 分页数据
     */
    IPage<FleetCompanyVo> search(IPage<FleetCompanyVo> page, @Param("search") FleetCompanyPageDto search);

    /**
     * 用户中心-查询租户信息
     *
     * @param search 查询条件
     * @return 分页数据
     */
    List<FleetCompanyVo> searchList(@Param("search") FleetCompanyPageDto search);

}
