package com.chervon.fleet.user.entity.query;

import com.chervon.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 访问名单配置
 * <AUTHOR>
 * 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "RequestList查询对象", description = "访问名单配置")
public class RequestListQuery extends PageQuery<RequestListQuery> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 访问路径
     */
    @ApiModelProperty(value = "访问路径")
    private String path;
    /**
     * 匹配方法
     */
    @ApiModelProperty(value = "匹配方法")
    private String matchMethod;
    /**
     * 限制类型（1.白名单，2正常，3黑名单）
     */
    @ApiModelProperty(value = "限制类型（1.白名单，2正常，3黑名单）")
    private Integer type;
    /**
     * 是否删除：0正常   1已删除
     */
    @ApiModelProperty(value = "是否删除：0正常   1已删除")
    private Integer isDeleted;
}
