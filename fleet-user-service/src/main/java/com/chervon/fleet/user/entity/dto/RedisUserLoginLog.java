package com.chervon.fleet.user.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023-09-01 18:12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RedisUserLoginLog implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 登录的RedisToken存储key
     * 根据key删除则登录失效
     */
    private String redisKey;
    /**
     * 设备类型: 1web 2android 3ios
     */
    private Integer deviceType;
    /**
     * 登录时间戳
     */
    private Long loginTime;
}
